"""
AI Companion System - A conversational AI for emotional support and mental health.

This package provides a comprehensive AI companion system with:
- Advanced emotional intelligence
- Neural-symbolic memory architecture
- Crisis detection and intervention
- WhatsApp bot integration
- Mental health data analytics
"""

__version__ = "1.0.0"
__author__ = "AI Companion Team"
__email__ = "<EMAIL>"

from .core.models import EmotionType, InteractionType, MemoryType
from .core.conversation import ConversationService
from .core.memory import MemoryService
from .core.emotions import EmotionalIntelligenceService

__all__ = [
    "EmotionType",
    "InteractionType", 
    "MemoryType",
    "ConversationService",
    "MemoryService",
    "EmotionalIntelligenceService",
]
