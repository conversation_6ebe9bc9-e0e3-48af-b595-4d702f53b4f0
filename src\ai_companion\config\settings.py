"""
Configuration module for the AI Companion System.
Handles environment variables, API keys, and system settings.
"""

import os
import logging
from pathlib import Path
from typing import Optional, List
from dotenv import load_dotenv
from pydantic_settings import BaseSettings
from pydantic import Field, validator

# Load environment variables
load_dotenv()

class Settings(BaseSettings):
    """Main settings class for the AI Companion System."""
    
    # API Keys (Required)
    gemini_api_key: str = Field(..., env="GEMINI_API_KEY", description="Google Gemini API key")
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY", description="OpenAI API key (optional)")

    @validator('gemini_api_key')
    def validate_gemini_key(cls, v):
        if not v or len(v) < 10:
            raise ValueError('GEMINI_API_KEY must be a valid API key')
        return v
    
    # Database Configuration
    redis_url: str = Field("redis://localhost:6379", env="REDIS_URL", description="Redis connection URL")
    database_url: str = Field("sqlite:///./data/db/ai_companion.db", env="DATABASE_URL", description="Database connection URL")

    @validator('database_url')
    def validate_database_url(cls, v):
        if not v.startswith(('sqlite:///', 'postgresql://', 'mysql://')):
            raise ValueError('DATABASE_URL must be a valid database connection string')
        return v
    
    # Memory Configuration
    memory_ttl: int = Field(2592000, env="MEMORY_TTL")  # 30 days in seconds
    personal_memory_size: int = Field(1000, env="PERSONAL_MEMORY_SIZE")
    universal_memory_size: int = Field(10000, env="UNIVERSAL_MEMORY_SIZE")
    
    # Learning Configuration
    learning_rate: float = Field(0.1, env="LEARNING_RATE")
    adaptation_threshold: int = Field(5, env="ADAPTATION_THRESHOLD")
    emotion_weight: float = Field(0.3, env="EMOTION_WEIGHT")
    
    # Conversation Configuration
    max_context_length: int = Field(2000, env="MAX_CONTEXT_LENGTH")
    max_history_length: int = Field(50, env="MAX_HISTORY_LENGTH")
    response_temperature: float = Field(0.7, env="RESPONSE_TEMPERATURE")
    max_tokens: int = Field(150, env="MAX_TOKENS")
    
    # System Configuration
    log_level: str = Field("INFO", env="LOG_LEVEL", description="Logging level")
    environment: str = Field("development", env="ENVIRONMENT", description="Environment (development/staging/production)")
    debug_mode: bool = Field(True, env="DEBUG_MODE", description="Enable debug mode")

    # Security Configuration
    secret_key: str = Field("dev-secret-key-change-in-production", env="SECRET_KEY", description="Secret key for encryption")
    allowed_hosts: str = Field("localhost,127.0.0.1", env="ALLOWED_HOSTS", description="Allowed hosts (comma-separated)")
    cors_origins: str = Field("http://localhost:3000", env="CORS_ORIGINS", description="CORS allowed origins (comma-separated)")

    # Rate Limiting
    rate_limit_requests: int = Field(100, env="RATE_LIMIT_REQUESTS", description="Requests per minute per user")
    rate_limit_window: int = Field(60, env="RATE_LIMIT_WINDOW", description="Rate limit window in seconds")

    # Monitoring & Health Checks
    health_check_interval: int = Field(30, env="HEALTH_CHECK_INTERVAL", description="Health check interval in seconds")
    metrics_enabled: bool = Field(True, env="METRICS_ENABLED", description="Enable metrics collection")

    # Performance Configuration
    cache_size: int = Field(1000, env="CACHE_SIZE", description="Cache size for responses")
    thread_pool_size: int = Field(4, env="THREAD_POOL_SIZE", description="Thread pool size")
    memory_threshold_mb: int = Field(500, env="MEMORY_THRESHOLD_MB", description="Memory threshold in MB")
    target_response_time: float = Field(0.3, env="TARGET_RESPONSE_TIME", description="Target response time in seconds")
    target_cache_hit_rate: float = Field(0.8, env="TARGET_CACHE_HIT_RATE", description="Target cache hit rate")

    # Mental Health Configuration
    crisis_threshold: float = Field(0.7, env="CRISIS_THRESHOLD", description="Crisis detection threshold")
    moderate_risk_threshold: float = Field(0.4, env="MODERATE_RISK_THRESHOLD", description="Moderate risk threshold")
    enable_mental_health_platform: bool = Field(True, env="ENABLE_MENTAL_HEALTH_PLATFORM", description="Enable mental health platform")
    min_cohort_size: int = Field(10, env="MIN_COHORT_SIZE", description="Minimum cohort size for analytics")
    anonymization_enabled: bool = Field(True, env="ANONYMIZATION_ENABLED", description="Enable data anonymization")
    k_anonymity_level: int = Field(5, env="K_ANONYMITY_LEVEL", description="K-anonymity level")

    # WhatsApp Configuration
    whatsapp_webhook_verify_token: Optional[str] = Field(None, env="WHATSAPP_WEBHOOK_VERIFY_TOKEN", description="WhatsApp webhook verify token")
    whatsapp_access_token: Optional[str] = Field(None, env="WHATSAPP_ACCESS_TOKEN", description="WhatsApp access token")
    whatsapp_phone_number_id: Optional[str] = Field(None, env="WHATSAPP_PHONE_NUMBER_ID", description="WhatsApp phone number ID")

    @validator('environment')
    def validate_environment(cls, v):
        if v not in ['development', 'staging', 'production']:
            raise ValueError('ENVIRONMENT must be one of: development, staging, production')
        return v

    @validator('log_level')
    def validate_log_level(cls, v):
        if v.upper() not in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']:
            raise ValueError('LOG_LEVEL must be one of: DEBUG, INFO, WARNING, ERROR, CRITICAL')
        return v.upper()

    @validator('secret_key')
    def validate_secret_key(cls, v, values):
        if values.get('environment') == 'production' and v == 'dev-secret-key-change-in-production':
            raise ValueError('SECRET_KEY must be changed in production environment')
        if len(v) < 32:
            raise ValueError('SECRET_KEY must be at least 32 characters long')
        return v
    
    # Service Ports
    gradio_port: int = Field(7860, env="GRADIO_PORT")
    api_port: int = Field(8000, env="API_PORT")
    
    class Config:
        env_file = ".env"
        case_sensitive = False

    def get_allowed_hosts(self) -> List[str]:
        """Get allowed hosts as a list."""
        return [host.strip() for host in self.allowed_hosts.split(',')]

    def get_cors_origins(self) -> List[str]:
        """Get CORS origins as a list."""
        return [origin.strip() for origin in self.cors_origins.split(',')]

# Global settings instance
settings = Settings()

# Validation
def validate_settings():
    """Validate critical settings and provide helpful error messages."""
    if not settings.gemini_api_key:
        raise ValueError("GEMINI_API_KEY is required. Please set it in your .env file.")
    
    if settings.learning_rate <= 0 or settings.learning_rate > 1:
        raise ValueError("LEARNING_RATE must be between 0 and 1")
    
    if settings.emotion_weight < 0 or settings.emotion_weight > 1:
        raise ValueError("EMOTION_WEIGHT must be between 0 and 1")
    
    if settings.response_temperature < 0 or settings.response_temperature > 2:
        raise ValueError("RESPONSE_TEMPERATURE must be between 0 and 2")

    if settings.crisis_threshold < 0 or settings.crisis_threshold > 1:
        raise ValueError("CRISIS_THRESHOLD must be between 0 and 1")

    if settings.target_response_time <= 0:
        raise ValueError("TARGET_RESPONSE_TIME must be greater than 0")

    if settings.target_cache_hit_rate < 0 or settings.target_cache_hit_rate > 1:
        raise ValueError("TARGET_CACHE_HIT_RATE must be between 0 and 1")

# Validate on import (but don't fail in development)
try:
    validate_settings()
except ValueError as e:
    if settings.environment == 'production':
        print(f"Configuration Error: {e}")
        print("Please check your .env file and ensure all required settings are correct.")
        raise
    else:
        print(f"Configuration Warning: {e}")
        print("This is acceptable in development mode, but should be fixed for production.")
