#!/usr/bin/env python3
"""
Quick launch script for the Next-Generation AI Companion System.
Provides easy startup with configuration options.
"""

import asyncio
import argparse
import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from next_gen_main import NextGenAICompanion
from test_next_gen_system import TestNextGenSystem
from config import settings

def print_banner():
    """Print startup banner."""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🧠 NEXT-GENERATION AI COMPANION SYSTEM 🧠            ║
    ║                                                              ║
    ║  🚀 Neural-Symbolic Memory Architecture                      ║
    ║  💝 Advanced Emotional Intelligence                          ║
    ║  🔬 Psychological Profiling & Therapeutic Responses         ║
    ║  ⚡ High-Performance Optimization                            ║
    ║  🛡️ Crisis Detection & Mental Health Monitoring             ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_requirements():
    """Check if all requirements are met."""
    print("🔍 Checking system requirements...")
    
    # Check API key
    if not settings.gemini_api_key or settings.gemini_api_key == "your_gemini_api_key_here":
        print("❌ GEMINI_API_KEY not configured!")
        print("   Please set your Gemini API key in the .env file or environment variables.")
        return False
    
    print("✅ Gemini API key configured")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required!")
        return False
    
    print("✅ Python version compatible")
    
    # Check required packages
    required_packages = [
        'gradio', 'fastapi', 'uvicorn', 'google-generativeai',
        'sentence-transformers', 'redis', 'numpy', 'scikit-learn',
        'networkx', 'asyncio'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("   Please run: pip install -r requirements.txt")
        return False
    
    print("✅ All required packages installed")
    return True

async def run_tests():
    """Run comprehensive system tests."""
    print("\n🧪 Running comprehensive system tests...")

    test_suite = TestNextGenSystem()
    success = await test_suite.run_comprehensive_test()

    if success:
        print("✅ All tests passed! System is ready.")
        return True
    else:
        print("❌ Some tests failed. Please check the configuration.")
        return False

async def launch_system(args):
    """Launch the next-generation AI companion system."""
    print("\n🚀 Launching Next-Generation AI Companion...")
    
    try:
        # Create and initialize the application
        app = NextGenAICompanion()
        await app.initialize()
        
        # Launch with specified options
        await app.run(
            share=args.share,
            server_port=args.port
        )
        
    except KeyboardInterrupt:
        print("\n👋 Shutting down gracefully...")
    except Exception as e:
        print(f"❌ Launch error: {e}")
        sys.exit(1)

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Next-Generation AI Companion System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python launch_next_gen.py                    # Launch with default settings
  python launch_next_gen.py --test             # Run tests first
  python launch_next_gen.py --share            # Create public share link
  python launch_next_gen.py --port 8080        # Use custom port
  python launch_next_gen.py --test --share     # Test then launch with sharing
        """
    )
    
    parser.add_argument(
        '--test', 
        action='store_true',
        help='Run comprehensive tests before launching'
    )
    
    parser.add_argument(
        '--share', 
        action='store_true',
        help='Create a public share link (Gradio)'
    )
    
    parser.add_argument(
        '--port', 
        type=int,
        default=7860,
        help='Port to run the server on (default: 7860)'
    )
    
    parser.add_argument(
        '--skip-checks', 
        action='store_true',
        help='Skip requirement checks (not recommended)'
    )
    
    parser.add_argument(
        '--debug', 
        action='store_true',
        help='Enable debug mode'
    )
    
    args = parser.parse_args()
    
    # Print banner
    print_banner()
    
    # Set debug mode
    if args.debug:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)
        print("🐛 Debug mode enabled")
    
    # Check requirements
    if not args.skip_checks:
        if not check_requirements():
            print("\n❌ Requirements check failed. Use --skip-checks to bypass (not recommended).")
            sys.exit(1)
    
    async def run_main():
        # Run tests if requested
        if args.test:
            test_success = await run_tests()
            if not test_success:
                print("\n❌ Tests failed. Aborting launch.")
                return False
        
        # Launch the system
        await launch_system(args)
        return True
    
    # Run the main async function
    try:
        asyncio.run(run_main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
