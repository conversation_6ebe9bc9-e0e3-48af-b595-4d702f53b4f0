"""
Production-Grade WhatsApp Bot Integration for AI Companion System.
Implements enterprise-level features for emotional support and mental health assistance.

Key Features:
- Advanced message queuing and processing
- Crisis intervention protocols
- Session management with emotional context
- Privacy-first data handling
- Professional mental health integration
- Scalable architecture for high-volume deployment
"""

import asyncio
import logging
import json
import time
import hashlib
import hmac
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import queue
import threading
from concurrent.futures import ThreadPoolExecutor
import redis
import requests
from flask import Flask, request, jsonify
import os

# Import enhanced AI services
from enhanced_memory_architecture import EnhancedMemoryArchitecture
from next_gen_emotional_intelligence import (
    NextGenEmotionalIntelligence, MentalHealthRisk, TherapeuticTechnique,
    EmotionalState, PsychologicalProfile
)
from gemini_service import GeminiService
from mental_health_data_platform import MentalHealthDataPlatform

logger = logging.getLogger(__name__)

class MessagePriority(Enum):
    """Message priority levels for queue processing."""
    CRISIS = "crisis"           # Immediate processing
    HIGH = "high"              # Process within 30 seconds
    NORMAL = "normal"          # Process within 2 minutes
    LOW = "low"               # Process within 5 minutes

class SessionState(Enum):
    """User session states."""
    NEW = "new"
    ACTIVE = "active"
    CRISIS = "crisis"
    THERAPEUTIC = "therapeutic"
    DORMANT = "dormant"
    ENDED = "ended"

@dataclass
class WhatsAppMessage:
    """Enhanced WhatsApp message structure."""
    message_id: str
    from_number: str
    to_number: str
    message_body: str
    message_type: str = "text"
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Enhanced metadata
    priority: MessagePriority = MessagePriority.NORMAL
    emotional_context: Optional[Dict[str, Any]] = None
    risk_indicators: List[str] = field(default_factory=list)
    requires_human_review: bool = False
    
    # Processing metadata
    processing_started: Optional[datetime] = None
    processing_completed: Optional[datetime] = None
    response_generated: Optional[str] = None
    therapeutic_technique_used: Optional[TherapeuticTechnique] = None

@dataclass
class UserSession:
    """Enhanced user session with emotional and therapeutic context."""
    user_id: str
    phone_number: str
    session_state: SessionState = SessionState.NEW
    
    # Session metadata
    session_start: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    last_activity: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    message_count: int = 0
    
    # Emotional and therapeutic context
    current_emotional_state: Optional[EmotionalState] = None
    risk_level: MentalHealthRisk = MentalHealthRisk.LOW
    active_crisis: bool = False
    therapeutic_goals: List[str] = field(default_factory=list)
    
    # Conversation context
    conversation_history: List[Dict[str, Any]] = field(default_factory=list)
    unresolved_concerns: List[str] = field(default_factory=list)
    
    # Professional support
    escalated_to_human: bool = False
    professional_referral_made: bool = False
    emergency_contacts_notified: bool = False

class ProductionWhatsAppBot:
    """
    Production-grade WhatsApp bot for emotional support and mental health assistance.
    
    Features:
    - High-performance message processing
    - Advanced crisis detection and intervention
    - Professional mental health integration
    - Privacy-first data handling
    - Scalable microservices architecture
    """
    
    def __init__(
        self,
        webhook_verify_token: str,
        access_token: str,
        phone_number_id: str,
        redis_url: str = "redis://localhost:6379"
    ):
        """Initialize the production WhatsApp bot."""
        # WhatsApp API configuration
        self.webhook_verify_token = webhook_verify_token
        self.access_token = access_token
        self.phone_number_id = phone_number_id
        self.api_base_url = "https://graph.facebook.com/v18.0"
        
        # Enhanced AI services
        self.gemini_service = GeminiService()
        self.memory_architecture = EnhancedMemoryArchitecture()
        self.emotional_intelligence = NextGenEmotionalIntelligence(self.gemini_service)
        self.mental_health_platform = MentalHealthDataPlatform()
        
        # Session and message management
        self.user_sessions: Dict[str, UserSession] = {}
        self.message_queue = queue.PriorityQueue()
        self.crisis_queue = queue.Queue()
        
        # Redis for distributed session management
        self.redis_client = redis.from_url(redis_url)
        
        # Processing infrastructure
        self.thread_pool = ThreadPoolExecutor(max_workers=10)
        self.processing_threads = []
        self.crisis_thread = None
        self.running = False
        
        # Flask app for webhook
        self.app = Flask(__name__)
        self._setup_routes()
        
        # Performance metrics
        self.metrics = {
            'messages_processed': 0,
            'crisis_interventions': 0,
            'average_response_time': 0.0,
            'successful_de_escalations': 0,
            'professional_referrals': 0
        }
        
        # Crisis intervention protocols
        self.crisis_protocols = self._initialize_crisis_protocols()
        
        # Professional support integration
        self.professional_contacts = self._load_professional_contacts()
        
        logger.info("Production WhatsApp Bot initialized successfully")
    
    def _setup_routes(self):
        """Setup Flask routes for WhatsApp webhook."""
        
        @self.app.route('/webhook', methods=['GET'])
        def verify_webhook():
            """Verify webhook with WhatsApp."""
            mode = request.args.get('hub.mode')
            token = request.args.get('hub.verify_token')
            challenge = request.args.get('hub.challenge')
            
            if mode == 'subscribe' and token == self.webhook_verify_token:
                logger.info("Webhook verified successfully")
                return challenge
            else:
                logger.warning("Webhook verification failed")
                return 'Verification failed', 403
        
        @self.app.route('/webhook', methods=['POST'])
        def handle_webhook():
            """Handle incoming WhatsApp messages."""
            try:
                data = request.get_json()
                
                # Verify webhook signature for security
                if not self._verify_webhook_signature(request):
                    logger.warning("Invalid webhook signature")
                    return 'Invalid signature', 403
                
                # Process webhook data
                self._process_webhook_data(data)
                
                return 'OK', 200
                
            except Exception as e:
                logger.error(f"Error handling webhook: {e}")
                return 'Error', 500
        
        @self.app.route('/health', methods=['GET'])
        def health_check():
            """Health check endpoint."""
            return jsonify({
                'status': 'healthy',
                'active_sessions': len(self.user_sessions),
                'queue_size': self.message_queue.qsize(),
                'crisis_queue_size': self.crisis_queue.qsize(),
                'metrics': self.metrics
            })
        
        @self.app.route('/metrics', methods=['GET'])
        def get_metrics():
            """Get detailed performance metrics."""
            return jsonify({
                'performance_metrics': self.metrics,
                'session_statistics': self._get_session_statistics(),
                'emotional_insights': self.emotional_intelligence.get_interaction_statistics(),
                'memory_statistics': self.memory_architecture.get_memory_statistics()
            })
    
    def _verify_webhook_signature(self, request) -> bool:
        """Verify webhook signature for security."""
        signature = request.headers.get('X-Hub-Signature-256')
        if not signature:
            return False
        
        expected_signature = hmac.new(
            self.webhook_verify_token.encode(),
            request.get_data(),
            hashlib.sha256
        ).hexdigest()
        
        return hmac.compare_digest(signature, f"sha256={expected_signature}")
    
    def _process_webhook_data(self, data: Dict[str, Any]):
        """Process incoming webhook data."""
        if 'entry' not in data:
            return
        
        for entry in data['entry']:
            if 'changes' not in entry:
                continue
                
            for change in entry['changes']:
                if change.get('field') == 'messages':
                    self._handle_message_change(change.get('value', {}))
    
    def _handle_message_change(self, value: Dict[str, Any]):
        """Handle message changes from webhook."""
        if 'messages' not in value:
            return
        
        for message_data in value['messages']:
            try:
                # Create WhatsApp message object
                whatsapp_message = self._create_whatsapp_message(message_data)
                
                # Perform initial risk assessment
                self._assess_message_priority(whatsapp_message)
                
                # Add to appropriate queue
                if whatsapp_message.priority == MessagePriority.CRISIS:
                    self.crisis_queue.put(whatsapp_message)
                    logger.warning(f"Crisis message detected from {whatsapp_message.from_number}")
                else:
                    priority_value = self._get_priority_value(whatsapp_message.priority)
                    self.message_queue.put((priority_value, time.time(), whatsapp_message))
                
            except Exception as e:
                logger.error(f"Error processing message: {e}")
    
    def _create_whatsapp_message(self, message_data: Dict[str, Any]) -> WhatsAppMessage:
        """Create WhatsApp message object from webhook data."""
        return WhatsAppMessage(
            message_id=message_data.get('id', ''),
            from_number=message_data.get('from', ''),
            to_number=message_data.get('to', ''),
            message_body=message_data.get('text', {}).get('body', ''),
            message_type=message_data.get('type', 'text'),
            timestamp=datetime.now(timezone.utc)
        )
    
    def _assess_message_priority(self, message: WhatsAppMessage):
        """Assess message priority based on content analysis."""
        text = message.message_body.lower()
        
        # Crisis keywords detection
        crisis_keywords = [
            'suicide', 'kill myself', 'end it all', 'want to die',
            'hurt myself', 'can\'t go on', 'emergency', 'help me'
        ]
        
        high_priority_keywords = [
            'urgent', 'crisis', 'panic', 'can\'t breathe', 'emergency'
        ]
        
        # Check for crisis indicators
        if any(keyword in text for keyword in crisis_keywords):
            message.priority = MessagePriority.CRISIS
            message.risk_indicators.extend([kw for kw in crisis_keywords if kw in text])
            message.requires_human_review = True
        elif any(keyword in text for keyword in high_priority_keywords):
            message.priority = MessagePriority.HIGH
        else:
            message.priority = MessagePriority.NORMAL
    
    def _get_priority_value(self, priority: MessagePriority) -> int:
        """Get numeric priority value for queue ordering."""
        priority_map = {
            MessagePriority.CRISIS: 0,
            MessagePriority.HIGH: 1,
            MessagePriority.NORMAL: 2,
            MessagePriority.LOW: 3
        }
        return priority_map.get(priority, 2)
    
    def start_processing(self):
        """Start message processing threads."""
        self.running = True
        
        # Start regular message processing threads
        for i in range(5):  # 5 worker threads
            thread = threading.Thread(
                target=self._process_message_queue,
                name=f"MessageProcessor-{i}",
                daemon=True
            )
            thread.start()
            self.processing_threads.append(thread)
        
        # Start crisis intervention thread
        self.crisis_thread = threading.Thread(
            target=self._process_crisis_queue,
            name="CrisisProcessor",
            daemon=True
        )
        self.crisis_thread.start()
        
        # Start session cleanup thread
        cleanup_thread = threading.Thread(
            target=self._cleanup_inactive_sessions,
            name="SessionCleanup",
            daemon=True
        )
        cleanup_thread.start()
        
        logger.info("Message processing started")
    
    def _process_message_queue(self):
        """Process regular message queue."""
        while self.running:
            try:
                # Get message from queue with timeout
                priority, timestamp, message = self.message_queue.get(timeout=1.0)
                
                # Process message
                asyncio.run(self._process_message(message))
                
                self.message_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error in message processing: {e}")
    
    def _process_crisis_queue(self):
        """Process crisis messages with immediate priority."""
        while self.running:
            try:
                # Get crisis message
                message = self.crisis_queue.get(timeout=1.0)
                
                # Process immediately
                asyncio.run(self._handle_crisis_message(message))
                
                self.crisis_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error in crisis processing: {e}")
    
    async def _process_message(self, message: WhatsAppMessage):
        """Process individual message with full AI pipeline."""
        start_time = time.time()
        message.processing_started = datetime.now(timezone.utc)
        
        try:
            # Get or create user session
            session = self._get_or_create_session(message.from_number)
            session.last_activity = datetime.now(timezone.utc)
            session.message_count += 1
            
            # Analyze emotional state
            emotional_state = await self.emotional_intelligence.analyze_emotional_state(
                user_id=session.user_id,
                text=message.message_body,
                context={'session_state': session.session_state.value}
            )
            
            # Assess mental health risk
            risk_level = await self.emotional_intelligence.assess_mental_health_risk(
                user_id=session.user_id,
                text=message.message_body,
                emotional_state=emotional_state
            )
            
            # Update session with current state
            session.current_emotional_state = emotional_state
            session.risk_level = risk_level
            
            # Check if escalation to crisis handling is needed
            if risk_level in [MentalHealthRisk.HIGH, MentalHealthRisk.CRITICAL]:
                session.session_state = SessionState.CRISIS
                session.active_crisis = True
                
                # Move to crisis queue if not already there
                if message.priority != MessagePriority.CRISIS:
                    self.crisis_queue.put(message)
                    return
            
            # Generate therapeutic response
            therapeutic_response = await self.emotional_intelligence.generate_therapeutic_response(
                user_id=session.user_id,
                message=message.message_body,
                emotional_state=emotional_state,
                risk_level=risk_level
            )
            
            # Store memory
            await self.memory_architecture.encode_memory(
                user_id=session.user_id,
                content=f"User: {message.message_body}\nAI: {therapeutic_response.content}",
                emotion=emotional_state.primary_emotion,
                context={
                    'session_id': session.user_id,
                    'risk_level': risk_level.value,
                    'technique_used': therapeutic_response.technique.value
                }
            )
            
            # Send response
            await self._send_whatsapp_message(
                to_number=message.from_number,
                message_text=therapeutic_response.content
            )
            
            # Update conversation history
            session.conversation_history.append({
                'timestamp': message.timestamp.isoformat(),
                'user_message': message.message_body,
                'ai_response': therapeutic_response.content,
                'emotional_state': emotional_state.primary_emotion.value,
                'risk_level': risk_level.value,
                'technique_used': therapeutic_response.technique.value
            })
            
            # Keep only recent history
            if len(session.conversation_history) > 20:
                session.conversation_history = session.conversation_history[-20:]
            
            # Update metrics
            processing_time = time.time() - start_time
            self.metrics['messages_processed'] += 1
            self.metrics['average_response_time'] = (
                (self.metrics['average_response_time'] * (self.metrics['messages_processed'] - 1) + processing_time) /
                self.metrics['messages_processed']
            )
            
            message.processing_completed = datetime.now(timezone.utc)
            message.response_generated = therapeutic_response.content
            message.therapeutic_technique_used = therapeutic_response.technique
            
            logger.info(f"Processed message from {message.from_number} in {processing_time:.2f}s")
            
        except Exception as e:
            logger.error(f"Error processing message from {message.from_number}: {e}")
            
            # Send fallback response
            await self._send_whatsapp_message(
                to_number=message.from_number,
                message_text="I'm here to support you. I'm experiencing some technical difficulties right now, but please know that your message is important to me. If this is an emergency, please contact emergency services immediately."
            )
    
    async def _handle_crisis_message(self, message: WhatsAppMessage):
        """Handle crisis messages with immediate intervention protocols."""
        logger.warning(f"Processing crisis message from {message.from_number}")
        
        try:
            # Get session and mark as crisis
            session = self._get_or_create_session(message.from_number)
            session.session_state = SessionState.CRISIS
            session.active_crisis = True
            
            # Immediate emotional analysis
            emotional_state = await self.emotional_intelligence.analyze_emotional_state(
                user_id=session.user_id,
                text=message.message_body,
                context={'crisis': True}
            )
            
            # Generate crisis intervention response
            crisis_response = await self.emotional_intelligence.generate_therapeutic_response(
                user_id=session.user_id,
                message=message.message_body,
                emotional_state=emotional_state,
                risk_level=MentalHealthRisk.CRITICAL
            )
            
            # Send immediate response
            await self._send_whatsapp_message(
                to_number=message.from_number,
                message_text=crisis_response.content
            )
            
            # Send crisis resources
            crisis_resources = """
🆘 IMMEDIATE HELP AVAILABLE:

• National Suicide Prevention Lifeline: 988
• Crisis Text Line: Text HOME to 741741
• Emergency Services: 911

You are not alone. Professional help is available 24/7.
            """
            
            await self._send_whatsapp_message(
                to_number=message.from_number,
                message_text=crisis_resources
            )
            
            # Escalate to human if configured
            if self.professional_contacts:
                await self._escalate_to_professional(session, message, emotional_state)
            
            # Log crisis intervention
            self.metrics['crisis_interventions'] += 1
            
            # Store crisis event in mental health platform
            await self.mental_health_platform.record_crisis_event(
                user_id=session.user_id,
                message=message.message_body,
                emotional_state=emotional_state,
                intervention_provided=crisis_response.content
            )
            
            logger.warning(f"Crisis intervention completed for {message.from_number}")
            
        except Exception as e:
            logger.error(f"Error in crisis intervention for {message.from_number}: {e}")
            
            # Emergency fallback
            emergency_message = """
🆘 EMERGENCY SUPPORT:

If you're in immediate danger, please call:
• Emergency Services: 911
• National Suicide Prevention Lifeline: 988

You matter and help is available.
            """
            
            await self._send_whatsapp_message(
                to_number=message.from_number,
                message_text=emergency_message
            )
    
    def _get_or_create_session(self, phone_number: str) -> UserSession:
        """Get existing session or create new one."""
        if phone_number not in self.user_sessions:
            user_id = hashlib.md5(phone_number.encode()).hexdigest()
            self.user_sessions[phone_number] = UserSession(
                user_id=user_id,
                phone_number=phone_number
            )
            
            # Store session in Redis for distributed access
            session_data = {
                'user_id': user_id,
                'phone_number': phone_number,
                'session_start': datetime.now(timezone.utc).isoformat(),
                'session_state': SessionState.NEW.value
            }
            self.redis_client.setex(
                f"session:{phone_number}",
                timedelta(hours=24),
                json.dumps(session_data)
            )
        
        return self.user_sessions[phone_number]

    async def _send_whatsapp_message(self, to_number: str, message_text: str) -> bool:
        """Send message via WhatsApp Business API."""
        url = f"{self.api_base_url}/{self.phone_number_id}/messages"

        headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        }

        data = {
            'messaging_product': 'whatsapp',
            'to': to_number,
            'type': 'text',
            'text': {'body': message_text}
        }

        try:
            response = requests.post(url, headers=headers, json=data, timeout=10)
            response.raise_for_status()

            logger.info(f"Message sent successfully to {to_number}")
            return True

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to send message to {to_number}: {e}")
            return False

    async def _escalate_to_professional(
        self,
        session: UserSession,
        message: WhatsAppMessage,
        emotional_state: EmotionalState
    ):
        """Escalate crisis situation to professional support."""
        if session.escalated_to_human:
            return  # Already escalated

        try:
            # Mark as escalated
            session.escalated_to_human = True

            # Prepare escalation data
            escalation_data = {
                'user_id': session.user_id,
                'phone_number': session.phone_number,
                'crisis_message': message.message_body,
                'emotional_state': {
                    'primary_emotion': emotional_state.primary_emotion.value,
                    'intensity': emotional_state.intensity,
                    'valence': emotional_state.valence,
                    'arousal': emotional_state.arousal
                },
                'session_history': session.conversation_history[-5:],  # Last 5 exchanges
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'risk_indicators': message.risk_indicators
            }

            # Send to professional monitoring system
            for contact in self.professional_contacts:
                await self._notify_professional(contact, escalation_data)

            # Update metrics
            self.metrics['professional_referrals'] += 1

            logger.warning(f"Escalated crisis case {session.user_id} to professional support")

        except Exception as e:
            logger.error(f"Error escalating to professional: {e}")

    async def _notify_professional(self, contact: Dict[str, Any], escalation_data: Dict[str, Any]):
        """Notify professional contact about crisis escalation."""
        # This would integrate with professional monitoring systems
        # For now, we'll log the escalation
        logger.critical(f"CRISIS ESCALATION: {json.dumps(escalation_data, indent=2)}")

        # In production, this would:
        # 1. Send secure notification to mental health professionals
        # 2. Create case in professional dashboard
        # 3. Trigger appropriate intervention protocols
        # 4. Maintain HIPAA compliance and privacy

    def _initialize_crisis_protocols(self) -> Dict[str, Any]:
        """Initialize crisis intervention protocols."""
        return {
            'immediate_response_time': 30,  # seconds
            'escalation_threshold': MentalHealthRisk.HIGH,
            'professional_notification_required': True,
            'emergency_contacts': {
                'suicide_prevention': '988',
                'crisis_text_line': '741741',
                'emergency_services': '911'
            },
            'follow_up_intervals': [1, 6, 24, 72],  # hours
            'safety_planning_triggers': [
                'suicide ideation',
                'self-harm intent',
                'substance abuse crisis',
                'severe panic'
            ]
        }

    def _load_professional_contacts(self) -> List[Dict[str, Any]]:
        """Load professional mental health contacts."""
        # In production, this would load from secure configuration
        return [
            {
                'type': 'crisis_counselor',
                'contact_method': 'secure_messaging',
                'availability': '24/7',
                'specialization': 'crisis_intervention'
            },
            {
                'type': 'mental_health_professional',
                'contact_method': 'professional_dashboard',
                'availability': 'business_hours',
                'specialization': 'ongoing_support'
            }
        ]

    def _cleanup_inactive_sessions(self):
        """Clean up inactive sessions periodically."""
        while self.running:
            try:
                current_time = datetime.now(timezone.utc)
                inactive_sessions = []

                for phone_number, session in self.user_sessions.items():
                    # Mark sessions inactive after 24 hours
                    if (current_time - session.last_activity).total_seconds() > 86400:
                        if not session.active_crisis:  # Don't cleanup crisis sessions
                            inactive_sessions.append(phone_number)

                # Remove inactive sessions
                for phone_number in inactive_sessions:
                    session = self.user_sessions[phone_number]
                    session.session_state = SessionState.ENDED

                    # Archive session data
                    await self._archive_session(session)

                    # Remove from active sessions
                    del self.user_sessions[phone_number]

                    # Remove from Redis
                    self.redis_client.delete(f"session:{phone_number}")

                if inactive_sessions:
                    logger.info(f"Cleaned up {len(inactive_sessions)} inactive sessions")

                # Sleep for 1 hour before next cleanup
                time.sleep(3600)

            except Exception as e:
                logger.error(f"Error in session cleanup: {e}")
                time.sleep(300)  # Wait 5 minutes before retry

    async def _archive_session(self, session: UserSession):
        """Archive session data for analytics and compliance."""
        try:
            # Prepare anonymized session data
            archived_data = {
                'session_id': session.user_id,  # Already hashed
                'session_duration': (session.last_activity - session.session_start).total_seconds(),
                'message_count': session.message_count,
                'final_state': session.session_state.value,
                'crisis_occurred': session.active_crisis,
                'professional_escalation': session.escalated_to_human,
                'emotional_summary': self._summarize_emotional_journey(session),
                'therapeutic_techniques_used': self._get_techniques_used(session),
                'archived_at': datetime.now(timezone.utc).isoformat()
            }

            # Store in mental health data platform for research
            await self.mental_health_platform.archive_session_data(archived_data)

            logger.info(f"Archived session data for {session.user_id}")

        except Exception as e:
            logger.error(f"Error archiving session {session.user_id}: {e}")

    def _summarize_emotional_journey(self, session: UserSession) -> Dict[str, Any]:
        """Summarize the emotional journey during the session."""
        if not session.conversation_history:
            return {}

        emotions = []
        risk_levels = []

        for exchange in session.conversation_history:
            if 'emotional_state' in exchange:
                emotions.append(exchange['emotional_state'])
            if 'risk_level' in exchange:
                risk_levels.append(exchange['risk_level'])

        return {
            'primary_emotions': list(set(emotions)),
            'emotion_progression': emotions,
            'risk_levels': list(set(risk_levels)),
            'highest_risk': max(risk_levels) if risk_levels else 'low',
            'emotional_volatility': len(set(emotions)) / max(len(emotions), 1)
        }

    def _get_techniques_used(self, session: UserSession) -> List[str]:
        """Get list of therapeutic techniques used in session."""
        techniques = []
        for exchange in session.conversation_history:
            if 'technique_used' in exchange:
                techniques.append(exchange['technique_used'])
        return list(set(techniques))

    def _get_session_statistics(self) -> Dict[str, Any]:
        """Get comprehensive session statistics."""
        if not self.user_sessions:
            return {}

        total_sessions = len(self.user_sessions)
        active_crises = sum(1 for s in self.user_sessions.values() if s.active_crisis)
        average_messages = sum(s.message_count for s in self.user_sessions.values()) / total_sessions

        session_states = {}
        for session in self.user_sessions.values():
            state = session.session_state.value
            session_states[state] = session_states.get(state, 0) + 1

        return {
            'total_active_sessions': total_sessions,
            'active_crisis_sessions': active_crises,
            'average_messages_per_session': round(average_messages, 2),
            'session_states_distribution': session_states,
            'escalated_sessions': sum(1 for s in self.user_sessions.values() if s.escalated_to_human)
        }

    def run_webhook_server(self, host: str = '0.0.0.0', port: int = 5000, debug: bool = False):
        """Run the webhook server."""
        logger.info(f"Starting WhatsApp webhook server on {host}:{port}")

        # Start message processing
        self.start_processing()

        # Run Flask app
        self.app.run(host=host, port=port, debug=debug, threaded=True)

    def stop(self):
        """Stop the bot and cleanup resources."""
        logger.info("Stopping WhatsApp bot...")

        self.running = False

        # Wait for threads to finish
        for thread in self.processing_threads:
            thread.join(timeout=5)

        if self.crisis_thread:
            self.crisis_thread.join(timeout=5)

        # Shutdown thread pool
        self.thread_pool.shutdown(wait=True)

        # Archive all active sessions
        for session in self.user_sessions.values():
            asyncio.run(self._archive_session(session))

        logger.info("WhatsApp bot stopped successfully")

# Factory function for easy deployment
def create_production_whatsapp_bot() -> ProductionWhatsAppBot:
    """Create production WhatsApp bot with environment configuration."""
    webhook_verify_token = os.getenv('WHATSAPP_WEBHOOK_VERIFY_TOKEN')
    access_token = os.getenv('WHATSAPP_ACCESS_TOKEN')
    phone_number_id = os.getenv('WHATSAPP_PHONE_NUMBER_ID')
    redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379')

    if not all([webhook_verify_token, access_token, phone_number_id]):
        raise ValueError("Missing required WhatsApp configuration. Please set WHATSAPP_WEBHOOK_VERIFY_TOKEN, WHATSAPP_ACCESS_TOKEN, and WHATSAPP_PHONE_NUMBER_ID environment variables.")

    return ProductionWhatsAppBot(
        webhook_verify_token=webhook_verify_token,
        access_token=access_token,
        phone_number_id=phone_number_id,
        redis_url=redis_url
    )

if __name__ == '__main__':
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('whatsapp_bot.log'),
            logging.StreamHandler()
        ]
    )

    try:
        # Create and run bot
        bot = create_production_whatsapp_bot()
        bot.run_webhook_server(debug=False)

    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Error running bot: {e}")
        raise
