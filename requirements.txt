# AI Companion System - Production Dependencies

# Core AI and ML
google-generativeai>=0.3.0,<1.0.0
sentence-transformers>=2.2.0,<3.0.0
torch>=2.0.0,<3.0.0
transformers>=4.30.0,<5.0.0

# Web Framework and API
fastapi>=0.100.0,<1.0.0
uvicorn[standard]>=0.23.0,<1.0.0
gradio>=4.0.0,<5.0.0

# Database and Storage
redis>=4.5.0,<6.0.0
sqlalchemy>=2.0.0,<3.0.0
alembic>=1.11.0,<2.0.0

# Data Processing
pandas>=2.0.0,<3.0.0
numpy>=1.24.0,<2.0.0
scikit-learn>=1.3.0,<2.0.0

# Configuration and Environment
python-dotenv>=1.0.0,<2.0.0
pydantic>=2.0.0,<3.0.0
pydantic-settings>=2.0.0,<3.0.0

# HTTP and Networking
httpx>=0.24.0,<1.0.0
requests>=2.31.0,<3.0.0

# System Monitoring
psutil>=5.9.0,<6.0.0

# Security and Encryption
cryptography>=41.0.0,<42.0.0
python-multipart>=0.0.6,<1.0.0

# Utilities
python-dateutil>=2.8.0,<3.0.0
pytz>=2023.3

# Optional: WhatsApp Integration
# twilio>=8.10.0,<9.0.0  # Uncomment if using WhatsApp

# Optional: Advanced NLP
# nltk>=3.8.0,<4.0.0  # Uncomment if using advanced NLP features