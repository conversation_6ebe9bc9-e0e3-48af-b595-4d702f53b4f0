"""
High-Performance Gradio Interface for AI Companion System.
Optimized for fast response times and smooth user experience.
"""

import gradio as gr
import asyncio
import time
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from optimized_conversation_service import OptimizedConversationService
from optimized_memory_service import HighPerformanceMemoryService
from gemini_service import OptimizedGeminiService
from config import settings

logger = logging.getLogger(__name__)

class OptimizedAICompanionInterface:
    """High-performance Gradio interface with async operations."""
    
    def __init__(self):
        """Initialize the optimized interface."""
        self.conversation_service = OptimizedConversationService()
        self.memory_service = HighPerformanceMemoryService()
        
        # Session management
        self.active_sessions = {}
        self.session_counter = 0
        
        # Performance tracking
        self.response_times = []
        self.total_interactions = 0
        
        # Create interface
        self.interface = self._create_interface()
    
    def _create_interface(self) -> gr.Blocks:
        """Create optimized Gradio interface."""
        
        with gr.Blocks(
            title="AI Companion - High Performance",
            theme=gr.themes.Soft(),
            css="""
            .performance-stats {
                background: linear-gradient(45deg, #f0f8ff, #e6f3ff);
                padding: 10px;
                border-radius: 8px;
                margin: 10px 0;
            }
            .fast-response {
                animation: fadeIn 0.3s ease-in;
            }
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }
            """
        ) as interface:
            
            gr.Markdown("""
            # 🚀 AI Companion - Ultra-Fast Edition
            
            Experience lightning-fast conversations with advanced emotional intelligence and memory.
            """)
            
            with gr.Row():
                with gr.Column(scale=3):
                    # Main chat interface
                    chatbot = gr.Chatbot(
                        label="AI Companion Chat",
                        height=500,
                        show_label=True,
                        elem_classes=["fast-response"]
                    )
                    
                    with gr.Row():
                        msg_input = gr.Textbox(
                            placeholder="Type your message here...",
                            label="Message",
                            lines=2,
                            max_lines=5,
                            scale=4
                        )
                        send_btn = gr.Button("Send", variant="primary", scale=1)
                    
                    # Quick action buttons
                    with gr.Row():
                        mood_btn = gr.Button("😊 Share Mood", size="sm")
                        support_btn = gr.Button("🤗 Need Support", size="sm")
                        celebrate_btn = gr.Button("🎉 Celebrate", size="sm")
                        reflect_btn = gr.Button("🤔 Reflect", size="sm")
                
                with gr.Column(scale=1):
                    # Performance dashboard
                    gr.Markdown("### ⚡ Performance Dashboard")
                    
                    performance_display = gr.JSON(
                        label="Real-time Stats",
                        elem_classes=["performance-stats"]
                    )
                    
                    # Emotion indicator
                    emotion_display = gr.Textbox(
                        label="Current Emotion",
                        interactive=False,
                        placeholder="Neutral"
                    )
                    
                    # Response time indicator
                    response_time_display = gr.Textbox(
                        label="Last Response Time",
                        interactive=False,
                        placeholder="0ms"
                    )
                    
                    # Memory stats
                    memory_stats_display = gr.JSON(
                        label="Memory Stats",
                        elem_classes=["performance-stats"]
                    )
            
            # Hidden state components
            session_id = gr.State(value=None)
            user_id = gr.State(value="demo_user")
            
            # Event handlers
            def start_session():
                """Start a new conversation session."""
                session_id_val = f"session_{int(time.time())}_{self.session_counter}"
                self.session_counter += 1
                
                # Start conversation asynchronously
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                conversation_id = loop.run_until_complete(
                    self.conversation_service.start_conversation_fast("demo_user", "Demo User")
                )
                loop.close()
                
                self.active_sessions[session_id_val] = conversation_id
                return session_id_val
            
            def process_message_wrapper(message: str, history: List, session_id_val: str, user_id_val: str):
                """Wrapper for async message processing."""
                if not session_id_val:
                    session_id_val = start_session()
                
                if not message.strip():
                    return history, "", session_id_val, {}, "Neutral", "0ms", {}
                
                start_time = time.time()
                
                try:
                    # Get conversation ID
                    conversation_id = self.active_sessions.get(session_id_val)
                    if not conversation_id:
                        conversation_id = asyncio.run(
                            self.conversation_service.start_conversation_fast(user_id_val, "Demo User")
                        )
                        self.active_sessions[session_id_val] = conversation_id
                    
                    # Process message asynchronously
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    result = loop.run_until_complete(
                        self.conversation_service.process_message_fast(
                            conversation_id, message, user_id_val
                        )
                    )
                    loop.close()
                    
                    # Update history
                    history.append([message, result["response"]])
                    
                    # Update performance stats
                    processing_time = time.time() - start_time
                    self.response_times.append(processing_time)
                    self.total_interactions += 1
                    
                    # Keep only last 100 response times
                    if len(self.response_times) > 100:
                        self.response_times = self.response_times[-100:]
                    
                    # Performance stats
                    avg_response_time = sum(self.response_times) / len(self.response_times)
                    performance_stats = {
                        "total_interactions": self.total_interactions,
                        "avg_response_time_ms": round(avg_response_time * 1000, 2),
                        "last_response_time_ms": round(processing_time * 1000, 2),
                        "cached_response": result.get("cached", False),
                        **self.conversation_service.get_performance_stats()
                    }
                    
                    # Memory stats
                    memory_stats = self.memory_service.get_cache_stats()
                    
                    return (
                        history,
                        "",  # Clear input
                        session_id_val,
                        performance_stats,
                        result.get("emotion", "Neutral").title(),
                        f"{round(processing_time * 1000, 1)}ms",
                        memory_stats
                    )
                    
                except Exception as e:
                    logger.error(f"Error processing message: {e}")
                    processing_time = time.time() - start_time
                    history.append([message, f"Sorry, I encountered an error: {str(e)}"])
                    
                    return (
                        history,
                        "",
                        session_id_val,
                        {"error": str(e), "processing_time_ms": round(processing_time * 1000, 2)},
                        "Error",
                        f"{round(processing_time * 1000, 1)}ms",
                        {}
                    )
            
            def quick_action(action_type: str, history: List, session_id_val: str, user_id_val: str):
                """Handle quick action buttons."""
                quick_messages = {
                    "mood": "I'd like to share how I'm feeling right now.",
                    "support": "I'm going through a tough time and could use some support.",
                    "celebrate": "I have some great news to share!",
                    "reflect": "I've been thinking about some things and would like to reflect."
                }
                
                message = quick_messages.get(action_type, "Hello!")
                return process_message_wrapper(message, history, session_id_val, user_id_val)
            
            # Wire up events
            send_btn.click(
                fn=process_message_wrapper,
                inputs=[msg_input, chatbot, session_id, user_id],
                outputs=[chatbot, msg_input, session_id, performance_display, emotion_display, response_time_display, memory_stats_display]
            )
            
            msg_input.submit(
                fn=process_message_wrapper,
                inputs=[msg_input, chatbot, session_id, user_id],
                outputs=[chatbot, msg_input, session_id, performance_display, emotion_display, response_time_display, memory_stats_display]
            )
            
            # Quick action buttons
            mood_btn.click(
                fn=lambda h, s, u: quick_action("mood", h, s, u),
                inputs=[chatbot, session_id, user_id],
                outputs=[chatbot, msg_input, session_id, performance_display, emotion_display, response_time_display, memory_stats_display]
            )
            
            support_btn.click(
                fn=lambda h, s, u: quick_action("support", h, s, u),
                inputs=[chatbot, session_id, user_id],
                outputs=[chatbot, msg_input, session_id, performance_display, emotion_display, response_time_display, memory_stats_display]
            )
            
            celebrate_btn.click(
                fn=lambda h, s, u: quick_action("celebrate", h, s, u),
                inputs=[chatbot, session_id, user_id],
                outputs=[chatbot, msg_input, session_id, performance_display, emotion_display, response_time_display, memory_stats_display]
            )
            
            reflect_btn.click(
                fn=lambda h, s, u: quick_action("reflect", h, s, u),
                inputs=[chatbot, session_id, user_id],
                outputs=[chatbot, msg_input, session_id, performance_display, emotion_display, response_time_display, memory_stats_display]
            )
            
            # Initialize session on load
            interface.load(
                fn=start_session,
                outputs=[session_id]
            )
        
        return interface
    
    def launch(self, **kwargs):
        """Launch the optimized interface."""
        default_kwargs = {
            "server_name": "0.0.0.0",
            "server_port": settings.gradio_port,
            "share": False,
            "debug": settings.debug_mode,
            "show_error": True,
            "quiet": False
        }
        default_kwargs.update(kwargs)
        
        logger.info(f"Launching optimized AI Companion interface on port {settings.gradio_port}")
        self.interface.launch(**default_kwargs)

# For backward compatibility
AICompanionInterface = OptimizedAICompanionInterface
