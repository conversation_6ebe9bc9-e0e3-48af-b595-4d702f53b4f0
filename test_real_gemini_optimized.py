"""
Test the optimized Gemini service with real API calls.
This demonstrates the actual Gemini integration with performance optimizations.
"""

import asyncio
import time
import logging
from typing import Dict, Any

from gemini_service import OptimizedGeminiService
from models import EmotionalState, EmotionType
from config import settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealGeminiTest:
    """Test the real optimized Gemini service."""
    
    def __init__(self):
        """Initialize with real Gemini service."""
        try:
            self.gemini_service = OptimizedGeminiService()
            logger.info("✅ Optimized Gemini service initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Gemini service: {e}")
            self.gemini_service = None
    
    async def test_basic_response(self) -> Dict[str, Any]:
        """Test basic response generation."""
        if not self.gemini_service:
            return {"error": "Gemini service not available"}
        
        logger.info("🧪 Testing basic Gemini response...")
        
        prompt = """You are a caring AI companion. Respond empathetically to this message:

User message: "I'm feeling anxious about my job interview tomorrow."

Respond in 1-2 sentences with empathy and support."""
        
        start_time = time.time()
        try:
            response = await self.gemini_service.generate_response_async(prompt)
            response_time = time.time() - start_time
            
            return {
                "success": True,
                "response": response,
                "response_time": response_time,
                "prompt_length": len(prompt),
                "response_length": len(response)
            }
        except Exception as e:
            response_time = time.time() - start_time
            return {
                "success": False,
                "error": str(e),
                "response_time": response_time
            }
    
    async def test_caching_performance(self) -> Dict[str, Any]:
        """Test response caching performance."""
        if not self.gemini_service:
            return {"error": "Gemini service not available"}
        
        logger.info("💾 Testing Gemini caching performance...")
        
        prompt = "You are a helpful AI. Respond to: 'Hello, how are you?'"
        
        # First call (should hit API)
        start_time = time.time()
        response1 = await self.gemini_service.generate_response_async(prompt)
        first_call_time = time.time() - start_time
        
        # Second call (should hit cache)
        start_time = time.time()
        response2 = await self.gemini_service.generate_response_async(prompt)
        second_call_time = time.time() - start_time
        
        # Third call (should hit cache)
        start_time = time.time()
        response3 = await self.gemini_service.generate_response_async(prompt)
        third_call_time = time.time() - start_time
        
        return {
            "first_call_time": first_call_time,
            "second_call_time": second_call_time,
            "third_call_time": third_call_time,
            "cache_speedup": first_call_time / second_call_time if second_call_time > 0 else 0,
            "responses_identical": response1 == response2 == response3,
            "cache_hits": self.gemini_service.cache_hits,
            "cache_misses": self.gemini_service.cache_misses
        }
    
    async def test_concurrent_requests(self, num_requests: int = 5) -> Dict[str, Any]:
        """Test concurrent request handling."""
        if not self.gemini_service:
            return {"error": "Gemini service not available"}
        
        logger.info(f"⚡ Testing {num_requests} concurrent Gemini requests...")
        
        prompts = [
            "Respond empathetically: 'I'm excited about my new job!'",
            "Respond supportively: 'I'm worried about my health.'",
            "Respond encouragingly: 'I'm learning to code.'",
            "Respond warmly: 'I had a great day today!'",
            "Respond helpfully: 'I need advice on relationships.'"
        ]
        
        # Create concurrent tasks
        tasks = []
        for i in range(num_requests):
            prompt = prompts[i % len(prompts)]
            task = self.gemini_service.generate_response_async(prompt)
            tasks.append(task)
        
        # Execute concurrently
        start_time = time.time()
        try:
            responses = await asyncio.gather(*tasks)
            total_time = time.time() - start_time
            
            return {
                "success": True,
                "num_requests": num_requests,
                "total_time": total_time,
                "avg_time_per_request": total_time / num_requests,
                "requests_per_second": num_requests / total_time,
                "all_responses_received": len(responses) == num_requests,
                "response_lengths": [len(r) for r in responses]
            }
        except Exception as e:
            total_time = time.time() - start_time
            return {
                "success": False,
                "error": str(e),
                "total_time": total_time
            }
    
    async def test_emotional_responses(self) -> Dict[str, Any]:
        """Test emotionally intelligent responses."""
        if not self.gemini_service:
            return {"error": "Gemini service not available"}
        
        logger.info("😊 Testing emotional intelligence with Gemini...")
        
        emotional_scenarios = [
            {
                "emotion": "anxiety",
                "message": "I'm really nervous about my presentation tomorrow. What if I mess up?",
                "expected_tone": "supportive"
            },
            {
                "emotion": "joy", 
                "message": "I just got accepted into my dream university! I can't believe it!",
                "expected_tone": "celebratory"
            },
            {
                "emotion": "sadness",
                "message": "I'm feeling really down lately. Everything seems difficult.",
                "expected_tone": "empathetic"
            }
        ]
        
        results = []
        total_time = 0
        
        for scenario in emotional_scenarios:
            prompt = f"""You are a caring AI companion. Respond {scenario['expected_tone']}ly to this message:

User message: "{scenario['message']}"
Detected emotion: {scenario['emotion']}

Respond in 1-2 sentences with appropriate emotional intelligence."""
            
            start_time = time.time()
            try:
                response = await self.gemini_service.generate_response_async(prompt)
                response_time = time.time() - start_time
                total_time += response_time
                
                results.append({
                    "emotion": scenario["emotion"],
                    "message": scenario["message"],
                    "response": response,
                    "response_time": response_time,
                    "expected_tone": scenario["expected_tone"]
                })
            except Exception as e:
                response_time = time.time() - start_time
                total_time += response_time
                results.append({
                    "emotion": scenario["emotion"],
                    "error": str(e),
                    "response_time": response_time
                })
        
        return {
            "scenarios_tested": len(emotional_scenarios),
            "total_time": total_time,
            "avg_time_per_scenario": total_time / len(emotional_scenarios),
            "results": results
        }
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all Gemini optimization tests."""
        logger.info("🏁 Starting optimized Gemini service tests...")
        
        if not self.gemini_service:
            return {"error": "Gemini service not available - check API key"}
        
        results = {}
        
        try:
            # Test basic response
            results["basic_response"] = await self.test_basic_response()
            
            # Test caching
            results["caching"] = await self.test_caching_performance()
            
            # Test concurrent requests
            results["concurrent"] = await self.test_concurrent_requests(3)
            
            # Test emotional responses
            results["emotional"] = await self.test_emotional_responses()
            
            logger.info("✅ All Gemini tests completed!")
            
        except Exception as e:
            logger.error(f"❌ Test failed: {e}")
            results["error"] = str(e)
        
        return results
    
    def print_test_summary(self, results: Dict[str, Any]):
        """Print test results summary."""
        print("\n" + "="*60)
        print("🤖 OPTIMIZED GEMINI SERVICE TEST RESULTS")
        print("="*60)
        
        if "error" in results:
            print(f"❌ Error: {results['error']}")
            print("\n💡 Make sure your GEMINI_API_KEY is set in the .env file")
            return
        
        if "basic_response" in results:
            basic = results["basic_response"]
            if basic.get("success"):
                print(f"Basic Response Test:")
                print(f"  ✅ Response time: {basic['response_time']:.3f}s")
                print(f"  ✅ Response length: {basic['response_length']} chars")
                print(f"  ✅ Response: {basic['response'][:100]}...")
            else:
                print(f"  ❌ Basic response failed: {basic.get('error', 'Unknown error')}")
        
        if "caching" in results:
            cache = results["caching"]
            print(f"Caching Performance:")
            print(f"  ✅ First call: {cache['first_call_time']:.3f}s")
            print(f"  ✅ Cached call: {cache['second_call_time']:.3f}s")
            print(f"  ✅ Cache speedup: {cache['cache_speedup']:.1f}x faster")
            print(f"  ✅ Cache hits: {cache['cache_hits']}")
        
        if "concurrent" in results:
            conc = results["concurrent"]
            if conc.get("success"):
                print(f"Concurrent Requests:")
                print(f"  ✅ {conc['num_requests']} requests in {conc['total_time']:.3f}s")
                print(f"  ✅ Rate: {conc['requests_per_second']:.1f} requests/second")
                print(f"  ✅ Avg time: {conc['avg_time_per_request']:.3f}s")
        
        if "emotional" in results:
            emo = results["emotional"]
            print(f"Emotional Intelligence:")
            print(f"  ✅ {emo['scenarios_tested']} scenarios tested")
            print(f"  ✅ Avg response time: {emo['avg_time_per_scenario']:.3f}s")
            for result in emo['results']:
                if 'response' in result:
                    print(f"  ✅ {result['emotion'].title()}: {result['response'][:60]}...")
        
        print("\n🚀 Optimized Gemini service is working perfectly!")
        print("💡 Your system uses real Gemini with performance optimizations!")

async def main():
    """Run the real Gemini optimization test."""
    test = RealGeminiTest()
    results = await test.run_all_tests()
    test.print_test_summary(results)

if __name__ == "__main__":
    asyncio.run(main())
