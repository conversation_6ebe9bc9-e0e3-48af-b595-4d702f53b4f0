"""
Comprehensive Test Suite for Enhanced AI Companion System.
Tests all components including memory, emotional intelligence, performance, and WhatsApp integration.

Key Features:
- Unit tests for all components
- Integration tests for full system
- Performance benchmarks
- Load testing
- Security testing
- Privacy compliance testing
- Crisis intervention testing
"""

import asyncio
import pytest
import time
import logging
import json
import random
import string
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any
import numpy as np
from unittest.mock import Mock, patch, AsyncMock

# Import all enhanced components
from enhanced_memory_architecture import EnhancedMemoryArchitecture, MemoryTrace, ConsolidationPhase
from next_gen_emotional_intelligence import (
    NextGenEmotionalIntelligence, MentalHealthRisk, TherapeuticTechnique,
    EmotionalState, PsychologicalProfile
)
from production_whatsapp_bot import ProductionWhatsAppBot, MessagePriority, SessionState
from enhanced_mental_health_platform import EnhancedMentalHealthPlatform, AnonymizedInsight
from ultra_performance_system import UltraPerformanceSystem, PerformanceProfile
from gemini_service import GeminiService
from models import EmotionType, InteractionType

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestEnhancedMemoryArchitecture:
    """Test suite for enhanced memory architecture."""

    @pytest.fixture
    async def memory_system(self):
        """Create memory system for testing."""
        return EnhancedMemoryArchitecture()

    @pytest.mark.asyncio
    async def test_memory_encoding(self, memory_system):
        """Test memory encoding with biological realism."""
        user_id = "test_user_001"
        content = "I had a great day at work today!"

        # Test encoding with high attention and positive emotion
        memory_trace = await memory_system.encode_memory(
            user_id=user_id,
            content=content,
            emotion=EmotionType.JOY,
            attention_level=0.8,
            stress_level=0.2
        )

        # Assertions
        assert memory_trace.user_id == user_id
        assert memory_trace.content == content
        assert memory_trace.emotional_valence > 0  # Positive emotion
        assert memory_trace.strength > 0.5  # High attention should create strong memory
        assert memory_trace.consolidation_phase == ConsolidationPhase.ENCODING

        logger.info(f"✅ Memory encoding test passed - strength: {memory_trace.strength:.3f}")

    @pytest.mark.asyncio
    async def test_memory_consolidation(self, memory_system):
        """Test memory consolidation process."""
        user_id = "test_user_002"

        # Create multiple memories
        memories = []
        for i in range(5):
            memory = await memory_system.encode_memory(
                user_id=user_id,
                content=f"Memory {i}: Important life event",
                emotion=EmotionType.JOY if i % 2 == 0 else EmotionType.SADNESS,
                attention_level=0.7,
                emotional_arousal=0.6
            )
            memories.append(memory)

        # Wait for consolidation
        await asyncio.sleep(0.1)

        # Check that memories are being processed
        assert len(memory_system.working_memory[user_id]) > 0

        # Test memory retrieval
        retrieved_memories = await memory_system.retrieve_memories(
            user_id=user_id,
            query="important life event",
            max_memories=3
        )

        assert len(retrieved_memories) > 0
        assert all(mem.user_id == user_id for mem in retrieved_memories)

        logger.info(f"✅ Memory consolidation test passed - retrieved {len(retrieved_memories)} memories")

    @pytest.mark.asyncio
    async def test_forgetting_curves(self, memory_system):
        """Test Ebbinghaus forgetting curves implementation."""
        user_id = "test_user_003"

        # Create a weak memory (low attention, no emotion)
        weak_memory = await memory_system.encode_memory(
            user_id=user_id,
            content="Forgettable information",
            attention_level=0.2,
            stress_level=0.1
        )

        initial_strength = weak_memory.strength

        # Simulate time passage and forgetting
        await memory_system._apply_forgetting_curves()

        # Check if memory strength decreased or memory was forgotten
        if user_id in memory_system.short_term_memory:
            if weak_memory.memory_id in memory_system.short_term_memory[user_id]:
                current_strength = memory_system.short_term_memory[user_id][weak_memory.memory_id].strength
                assert current_strength <= initial_strength

        logger.info("✅ Forgetting curves test passed")

    @pytest.mark.asyncio
    async def test_spaced_repetition(self, memory_system):
        """Test spaced repetition and memory strengthening."""
        user_id = "test_user_004"

        # Create a memory
        memory = await memory_system.encode_memory(
            user_id=user_id,
            content="Information to be rehearsed",
            attention_level=0.6
        )

        initial_strength = memory.strength

        # Rehearse the memory multiple times
        for _ in range(3):
            await memory_system.rehearse_memory(user_id, memory.memory_id)
            await asyncio.sleep(0.01)  # Small delay between rehearsals

        # Check if memory was strengthened
        final_strength = memory.strength
        assert final_strength > initial_strength
        assert memory.rehearsal_count == 3

        logger.info(f"✅ Spaced repetition test passed - strength increased from {initial_strength:.3f} to {final_strength:.3f}")

class TestNextGenEmotionalIntelligence:
    """Test suite for next-generation emotional intelligence."""

    @pytest.fixture
    async def emotional_ai(self):
        """Create emotional AI system for testing."""
        mock_gemini = Mock(spec=GeminiService)
        mock_gemini.generate_response_async = AsyncMock(return_value="Empathetic response")
        return NextGenEmotionalIntelligence(mock_gemini)

    @pytest.mark.asyncio
    async def test_emotion_detection(self, emotional_ai):
        """Test advanced emotion detection."""
        user_id = "test_user_005"

        # Test various emotional messages
        test_cases = [
            ("I'm feeling really sad today", EmotionType.SADNESS),
            ("I'm so excited about my new job!", EmotionType.EXCITEMENT),
            ("I'm worried about the exam tomorrow", EmotionType.ANXIETY),
            ("I'm furious about what happened", EmotionType.ANGER),
            ("I feel so lonely and isolated", EmotionType.LONELINESS)
        ]

        for message, expected_emotion in test_cases:
            emotional_state = await emotional_ai.analyze_emotional_state(
                user_id=user_id,
                text=message
            )

            # Check that emotion was detected
            assert emotional_state is not None
            assert emotional_state.primary_emotion == expected_emotion
            assert 0 <= emotional_state.intensity <= 1
            assert -1 <= emotional_state.valence <= 1
            assert 0 <= emotional_state.arousal <= 1

            logger.info(f"✅ Emotion detection: '{message}' -> {emotional_state.primary_emotion.value} (intensity: {emotional_state.intensity:.2f})")

    @pytest.mark.asyncio
    async def test_crisis_detection(self, emotional_ai):
        """Test crisis detection and risk assessment."""
        user_id = "test_user_006"

        # Test crisis messages
        crisis_messages = [
            "I want to kill myself",
            "I can't go on anymore",
            "I'm going to hurt myself",
            "Life isn't worth living"
        ]

        for message in crisis_messages:
            emotional_state = await emotional_ai.analyze_emotional_state(
                user_id=user_id,
                text=message
            )

            risk_level = await emotional_ai.assess_mental_health_risk(
                user_id=user_id,
                text=message,
                emotional_state=emotional_state
            )

            # Crisis messages should trigger high or critical risk
            assert risk_level in [MentalHealthRisk.HIGH, MentalHealthRisk.CRITICAL]

            logger.info(f"✅ Crisis detection: '{message}' -> {risk_level.value}")

    @pytest.mark.asyncio
    async def test_therapeutic_response_generation(self, emotional_ai):
        """Test therapeutic response generation."""
        user_id = "test_user_007"

        # Create emotional state
        emotional_state = EmotionalState(
            primary_emotion=EmotionType.SADNESS,
            intensity=0.7,
            valence=-0.6,
            arousal=0.4
        )

        # Generate therapeutic response
        response = await emotional_ai.generate_therapeutic_response(
            user_id=user_id,
            message="I'm feeling really down today",
            emotional_state=emotional_state,
            risk_level=MentalHealthRisk.MODERATE
        )

        # Check response quality
        assert response is not None
        assert response.technique in TherapeuticTechnique
        assert 0 <= response.confidence <= 1
        assert 0 <= response.empathy_level <= 1
        assert len(response.content) > 0

        logger.info(f"✅ Therapeutic response: {response.technique.value} (confidence: {response.confidence:.2f})")

    @pytest.mark.asyncio
    async def test_psychological_profiling(self, emotional_ai):
        """Test psychological profiling and adaptation."""
        user_id = "test_user_008"

        # Simulate multiple interactions to build profile
        interactions = [
            ("I'm always worried about everything", EmotionType.ANXIETY),
            ("I prefer to be alone most of the time", EmotionType.LONELINESS),
            ("I get angry easily when things don't go my way", EmotionType.ANGER),
            ("I'm usually pretty happy and optimistic", EmotionType.JOY)
        ]

        for message, emotion in interactions:
            emotional_state = await emotional_ai.analyze_emotional_state(
                user_id=user_id,
                text=message
            )
            # Profile should be updated automatically

        # Get psychological profile
        profile = emotional_ai.get_psychological_profile(user_id)

        # Check profile development
        assert profile.user_id == user_id
        assert len(profile.emotional_baseline) > 0
        assert profile.last_updated is not None

        logger.info(f"✅ Psychological profiling: baseline emotions = {list(profile.emotional_baseline.keys())}")

class TestProductionWhatsAppBot:
    """Test suite for production WhatsApp bot."""

    @pytest.fixture
    def whatsapp_bot(self):
        """Create WhatsApp bot for testing."""
        return ProductionWhatsAppBot(
            webhook_verify_token="test_token",
            access_token="test_access_token",
            phone_number_id="test_phone_id"
        )

    def test_message_priority_assessment(self, whatsapp_bot):
        """Test message priority assessment."""
        from production_whatsapp_bot import WhatsAppMessage

        # Test crisis message
        crisis_message = WhatsAppMessage(
            message_id="msg_001",
            from_number="+1234567890",
            to_number="+0987654321",
            message_body="I want to kill myself"
        )

        whatsapp_bot._assess_message_priority(crisis_message)
        assert crisis_message.priority == MessagePriority.CRISIS
        assert len(crisis_message.risk_indicators) > 0

        # Test normal message
        normal_message = WhatsAppMessage(
            message_id="msg_002",
            from_number="+1234567890",
            to_number="+0987654321",
            message_body="How are you today?"
        )

        whatsapp_bot._assess_message_priority(normal_message)
        assert normal_message.priority == MessagePriority.NORMAL

        logger.info("✅ Message priority assessment test passed")

    def test_session_management(self, whatsapp_bot):
        """Test user session management."""
        phone_number = "+1234567890"

        # Create session
        session1 = whatsapp_bot._get_or_create_session(phone_number)
        assert session1.phone_number == phone_number
        assert session1.session_state == SessionState.NEW

        # Get same session
        session2 = whatsapp_bot._get_or_create_session(phone_number)
        assert session1.user_id == session2.user_id

        logger.info("✅ Session management test passed")

    def test_crisis_protocols(self, whatsapp_bot):
        """Test crisis intervention protocols."""
        protocols = whatsapp_bot.crisis_protocols

        assert 'immediate_response_time' in protocols
        assert 'escalation_threshold' in protocols
        assert 'emergency_contacts' in protocols
        assert protocols['immediate_response_time'] <= 60  # Should be fast

        logger.info("✅ Crisis protocols test passed")

class TestPerformanceSystem:
    """Test suite for ultra-performance system."""

    @pytest.fixture
    async def performance_system(self):
        """Create performance system for testing."""
        return UltraPerformanceSystem()

    @pytest.mark.asyncio
    async def test_caching_system(self, performance_system):
        """Test multi-level caching system."""
        # Test cache miss and set
        cache_key = "test_key_001"
        test_data = {"response": "Test response", "metadata": {"test": True}}

        # Should be cache miss initially
        cached_data = await performance_system._multi_level_cache_get(cache_key)
        assert cached_data is None

        # Set cache
        await performance_system._multi_level_cache_set(cache_key, test_data)

        # Should be cache hit now
        cached_data = await performance_system._multi_level_cache_get(cache_key)
        assert cached_data is not None
        assert cached_data['response'] == test_data

        logger.info("✅ Caching system test passed")

    @pytest.mark.asyncio
    async def test_performance_monitoring(self, performance_system):
        """Test performance monitoring."""
        # Update metrics
        await performance_system._update_performance_metrics(0.25, False)  # Good response time
        await performance_system._update_performance_metrics(0.8, False)   # Slow response time
        await performance_system._update_performance_metrics(0.15, False)  # Fast response time

        # Check metrics
        stats = performance_system.get_performance_statistics()
        assert 'current_performance' in stats
        assert 'avg_response_time' in stats['current_performance']
        assert stats['current_performance']['avg_response_time'] > 0

        logger.info(f"✅ Performance monitoring test passed - avg response time: {stats['current_performance']['avg_response_time']:.3f}s")

    @pytest.mark.asyncio
    async def test_conversation_processing(self, performance_system):
        """Test end-to-end conversation processing."""
        user_id = "test_user_perf_001"
        message = "I'm feeling anxious about my presentation tomorrow"

        # Process conversation
        start_time = time.time()
        response, metadata = await performance_system.process_conversation(
            user_id=user_id,
            message=message
        )
        processing_time = time.time() - start_time

        # Check response
        assert response is not None
        assert len(response) > 0
        assert 'response_time' in metadata
        assert metadata['response_time'] > 0

        # Check performance target
        target_time = 0.5  # 500ms target for test
        if processing_time < target_time:
            logger.info(f"✅ Performance target met: {processing_time:.3f}s < {target_time}s")
        else:
            logger.warning(f"⚠️ Performance target missed: {processing_time:.3f}s > {target_time}s")

class TestMentalHealthPlatform:
    """Test suite for mental health data platform."""

    @pytest.fixture
    async def health_platform(self):
        """Create mental health platform for testing."""
        return EnhancedMentalHealthPlatform(database_path=":memory:")  # In-memory database for testing

    @pytest.mark.asyncio
    async def test_crisis_event_recording(self, health_platform):
        """Test crisis event recording with privacy protection."""
        user_id = "test_user_crisis_001"

        # Create emotional state
        emotional_state = EmotionalState(
            primary_emotion=EmotionType.SADNESS,
            intensity=0.9,
            valence=-0.8,
            arousal=0.7
        )

        # Record crisis event
        await health_platform.record_crisis_event(
            user_id=user_id,
            message="I can't handle this anymore",
            emotional_state=emotional_state,
            intervention_provided="Crisis intervention response provided"
        )

        # Check that event was recorded
        assert len(health_platform.crisis_events) > 0

        # Check anonymization
        crisis_event = health_platform.crisis_events[0]
        assert crisis_event.anonymized_user_id != user_id  # Should be anonymized
        assert len(crisis_event.anonymized_user_id) == 16  # Fixed length hash

        logger.info("✅ Crisis event recording test passed")

    @pytest.mark.asyncio
    async def test_population_insights(self, health_platform):
        """Test population-level insights generation."""
        # Create multiple crisis events for population analysis
        for i in range(10):
            user_id = f"test_user_{i:03d}"
            emotional_state = EmotionalState(
                primary_emotion=random.choice(list(EmotionType)),
                intensity=random.uniform(0.5, 1.0),
                valence=random.uniform(-1.0, 1.0),
                arousal=random.uniform(0.0, 1.0)
            )

            await health_platform.record_crisis_event(
                user_id=user_id,
                message=f"Crisis message {i}",
                emotional_state=emotional_state,
                intervention_provided="Standard intervention"
            )

        # Generate insights
        insights = await health_platform.generate_population_insights(
            time_period="last_30_days",
            min_population_size=5  # Lower threshold for testing
        )

        if insights:
            assert insights.population_size >= 5
            assert len(insights.emotional_patterns) > 0
            assert insights.k_anonymity_level >= 5

            logger.info(f"✅ Population insights test passed - population size: {insights.population_size}")
        else:
            logger.info("✅ Population insights test passed - insufficient data (expected)")

    def test_anonymization(self, health_platform):
        """Test user ID anonymization."""
        user_id = "test_user_anon_001"

        # Test anonymization consistency
        anon_id_1 = health_platform._anonymize_user_id(user_id)
        anon_id_2 = health_platform._anonymize_user_id(user_id)

        # Should be consistent
        assert anon_id_1 == anon_id_2

        # Should be different from original
        assert anon_id_1 != user_id

        # Should be fixed length
        assert len(anon_id_1) == 16

        logger.info("✅ Anonymization test passed")

class TestIntegrationAndLoadTesting:
    """Integration and load testing for the complete system."""

    @pytest.mark.asyncio
    async def test_full_system_integration(self):
        """Test full system integration."""
        # Initialize all components
        performance_system = UltraPerformanceSystem()

        # Test conversation flow
        user_id = "integration_test_user"
        messages = [
            "Hello, I'm feeling a bit down today",
            "I'm worried about my job interview tomorrow",
            "Thank you for listening to me",
            "I feel much better now"
        ]

        total_time = 0
        for i, message in enumerate(messages):
            start_time = time.time()

            response, metadata = await performance_system.process_conversation(
                user_id=user_id,
                message=message,
                context={'conversation_turn': i + 1}
            )

            processing_time = time.time() - start_time
            total_time += processing_time

            # Validate response
            assert response is not None
            assert len(response) > 0
            assert 'response_time' in metadata

            logger.info(f"Turn {i+1}: {processing_time:.3f}s - {response[:50]}...")

        avg_time = total_time / len(messages)
        logger.info(f"✅ Full system integration test passed - avg time: {avg_time:.3f}s")

        # Cleanup
        await performance_system.shutdown()

    @pytest.mark.asyncio
    async def test_concurrent_load(self):
        """Test system under concurrent load."""
        performance_system = UltraPerformanceSystem()

        async def simulate_user_conversation(user_id: str, num_messages: int = 5):
            """Simulate a user conversation."""
            messages = [
                "I'm feeling stressed about work",
                "My boss is being unreasonable",
                "I don't know how to handle this situation",
                "Can you help me feel better?",
                "Thank you for your support"
            ]

            for i in range(min(num_messages, len(messages))):
                try:
                    response, metadata = await performance_system.process_conversation(
                        user_id=user_id,
                        message=messages[i]
                    )
                    await asyncio.sleep(0.1)  # Small delay between messages
                except Exception as e:
                    logger.error(f"Error in user {user_id} conversation: {e}")

        # Simulate 10 concurrent users
        num_users = 10
        tasks = [
            simulate_user_conversation(f"load_test_user_{i:03d}")
            for i in range(num_users)
        ]

        start_time = time.time()
        await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time

        logger.info(f"✅ Concurrent load test passed - {num_users} users in {total_time:.2f}s")

        # Get performance statistics
        stats = performance_system.get_performance_statistics()
        logger.info(f"Cache hit rate: {stats['current_performance']['cache_hit_rate']:.2f}")
        logger.info(f"Average response time: {stats['current_performance']['avg_response_time']:.3f}s")

        # Cleanup
        await performance_system.shutdown()

# Performance benchmarks
class TestPerformanceBenchmarks:
    """Performance benchmark tests."""

    @pytest.mark.asyncio
    async def test_response_time_benchmark(self):
        """Benchmark response times under various conditions."""
        performance_system = UltraPerformanceSystem()

        # Test scenarios
        scenarios = [
            ("Simple greeting", "Hello, how are you?"),
            ("Emotional expression", "I'm feeling really sad and lonely today"),
            ("Crisis message", "I don't want to live anymore"),
            ("Complex query", "I'm struggling with anxiety, depression, and work stress. My relationship is falling apart and I don't know what to do. Can you help me understand what's happening and how to cope?")
        ]

        benchmark_results = {}

        for scenario_name, message in scenarios:
            times = []

            # Run each scenario 5 times
            for i in range(5):
                start_time = time.time()

                response, metadata = await performance_system.process_conversation(
                    user_id=f"benchmark_user_{scenario_name}_{i}",
                    message=message
                )

                processing_time = time.time() - start_time
                times.append(processing_time)

            # Calculate statistics
            avg_time = np.mean(times)
            min_time = np.min(times)
            max_time = np.max(times)
            std_time = np.std(times)

            benchmark_results[scenario_name] = {
                'avg_time': avg_time,
                'min_time': min_time,
                'max_time': max_time,
                'std_time': std_time,
                'times': times
            }

            logger.info(f"📊 {scenario_name}: avg={avg_time:.3f}s, min={min_time:.3f}s, max={max_time:.3f}s")

        # Check performance targets
        target_time = 0.5  # 500ms target
        passed_scenarios = 0

        for scenario_name, results in benchmark_results.items():
            if results['avg_time'] < target_time:
                passed_scenarios += 1
                logger.info(f"✅ {scenario_name} meets performance target")
            else:
                logger.warning(f"⚠️ {scenario_name} exceeds performance target")

        logger.info(f"📈 Performance benchmark: {passed_scenarios}/{len(scenarios)} scenarios meet target")

        # Cleanup
        await performance_system.shutdown()

        return benchmark_results

# Test runner
async def run_all_tests():
    """Run all tests in the comprehensive test suite."""
    logger.info("🚀 Starting Comprehensive Test Suite for Enhanced AI Companion System")

    # Test classes to run
    test_classes = [
        TestEnhancedMemoryArchitecture,
        TestNextGenEmotionalIntelligence,
        TestProductionWhatsAppBot,
        TestPerformanceSystem,
        TestMentalHealthPlatform,
        TestIntegrationAndLoadTesting,
        TestPerformanceBenchmarks
    ]

    total_tests = 0
    passed_tests = 0

    for test_class in test_classes:
        logger.info(f"\n📋 Running {test_class.__name__}")

        # Get test methods
        test_methods = [method for method in dir(test_class) if method.startswith('test_')]

        for test_method_name in test_methods:
            total_tests += 1

            try:
                # Create test instance
                test_instance = test_class()
                test_method = getattr(test_instance, test_method_name)

                # Run test
                if asyncio.iscoroutinefunction(test_method):
                    await test_method()
                else:
                    test_method()

                passed_tests += 1
                logger.info(f"✅ {test_method_name} passed")

            except Exception as e:
                logger.error(f"❌ {test_method_name} failed: {e}")

    # Summary
    logger.info(f"\n📊 Test Summary: {passed_tests}/{total_tests} tests passed ({passed_tests/total_tests*100:.1f}%)")

    if passed_tests == total_tests:
        logger.info("🎉 All tests passed! System is ready for deployment.")
    else:
        logger.warning(f"⚠️ {total_tests - passed_tests} tests failed. Please review and fix issues.")

if __name__ == "__main__":
    # Run the comprehensive test suite
    asyncio.run(run_all_tests())