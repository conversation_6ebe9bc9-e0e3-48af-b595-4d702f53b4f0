"""
Simple performance test for the optimized AI Companion System.
Tests basic functionality without complex dependencies.
"""

import asyncio
import time
import logging
from typing import Dict, Any

# Test the models first
from models import (
    MemoryEntry, MemoryType, InteractionType, EmotionType,
    UserProfile, EmotionalState
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimplePerformanceTest:
    """Simple test suite for basic functionality."""
    
    def __init__(self):
        """Initialize test suite."""
        self.test_results = {}
    
    def test_model_creation_performance(self) -> Dict[str, Any]:
        """Test model creation performance."""
        logger.info("🧪 Testing model creation performance...")
        
        # Test MemoryEntry creation
        start_time = time.time()
        memories = []
        
        for i in range(1000):
            memory = MemoryEntry(
                id=f"test_memory_{i}",
                user_id="test_user",
                memory_type=MemoryType.PERSONAL,
                interaction_type=InteractionType.CONVERSATION,
                content=f"This is test memory content number {i}. It contains meaningful information about user preferences and experiences.",
                emotion=EmotionType.JOY if i % 2 == 0 else EmotionType.NEUTRAL
            )
            memories.append(memory)
        
        creation_time = time.time() - start_time
        
        # Test hash computation performance
        start_time = time.time()
        hashes = []
        for memory in memories[:100]:  # Test first 100
            hash_val = memory.get_content_hash()
            hashes.append(hash_val)
        
        hash_time = time.time() - start_time
        
        # Test keyword extraction performance
        start_time = time.time()
        keywords_list = []
        for memory in memories[:100]:  # Test first 100
            keywords = memory.get_search_keywords()
            keywords_list.append(keywords)
        
        keyword_time = time.time() - start_time
        
        return {
            "memories_created": len(memories),
            "creation_time": creation_time,
            "avg_creation_time": creation_time / len(memories),
            "hash_computation_time": hash_time,
            "keyword_extraction_time": keyword_time,
            "memories_per_second": len(memories) / creation_time
        }
    
    def test_emotional_state_performance(self) -> Dict[str, Any]:
        """Test emotional state processing performance."""
        logger.info("😊 Testing emotional state performance...")
        
        start_time = time.time()
        emotional_states = []
        
        emotions = [EmotionType.JOY, EmotionType.SADNESS, EmotionType.ANGER, 
                   EmotionType.ANXIETY, EmotionType.NEUTRAL]
        
        for i in range(1000):
            emotion = emotions[i % len(emotions)]
            state = EmotionalState(
                primary_emotion=emotion,
                intensity=0.5 + (i % 5) * 0.1,
                confidence=0.7 + (i % 3) * 0.1,
                valence=0.5 if emotion == EmotionType.JOY else -0.3,
                arousal=0.6 if emotion == EmotionType.ANXIETY else 0.3
            )
            emotional_states.append(state)
        
        creation_time = time.time() - start_time
        
        # Test dominant emotion detection
        start_time = time.time()
        dominant_emotions = []
        for state in emotional_states[:100]:
            dominant = state.get_dominant_emotions(threshold=0.3)
            dominant_emotions.append(dominant)
        
        detection_time = time.time() - start_time
        
        return {
            "states_created": len(emotional_states),
            "creation_time": creation_time,
            "avg_creation_time": creation_time / len(emotional_states),
            "detection_time": detection_time,
            "states_per_second": len(emotional_states) / creation_time
        }
    
    def test_user_profile_performance(self) -> Dict[str, Any]:
        """Test user profile creation and manipulation."""
        logger.info("👤 Testing user profile performance...")
        
        start_time = time.time()
        profiles = []
        
        for i in range(100):
            profile = UserProfile(
                user_id=f"user_{i}",
                name=f"Test User {i}",
                preferences={
                    "communication_style": "friendly",
                    "topics_of_interest": ["technology", "science", "art"],
                    "preferred_time": "evening"
                },
                interests=["reading", "hiking", "cooking", "music"],
                emotional_patterns={
                    EmotionType.JOY: 0.7,
                    EmotionType.ANXIETY: 0.3,
                    EmotionType.NEUTRAL: 0.5
                },
                goals=["learn new skills", "improve health", "build relationships"]
            )
            profiles.append(profile)
        
        creation_time = time.time() - start_time
        
        # Test profile serialization
        start_time = time.time()
        serialized = []
        for profile in profiles:
            json_data = profile.model_dump_json()
            serialized.append(json_data)
        
        serialization_time = time.time() - start_time
        
        return {
            "profiles_created": len(profiles),
            "creation_time": creation_time,
            "avg_creation_time": creation_time / len(profiles),
            "serialization_time": serialization_time,
            "profiles_per_second": len(profiles) / creation_time
        }
    
    async def test_async_operations(self) -> Dict[str, Any]:
        """Test async operation performance."""
        logger.info("⚡ Testing async operations...")
        
        async def async_task(task_id: int) -> Dict[str, Any]:
            """Simulate an async task."""
            await asyncio.sleep(0.01)  # Simulate some async work
            
            # Create a memory entry
            memory = MemoryEntry(
                id=f"async_memory_{task_id}",
                user_id=f"async_user_{task_id % 10}",
                memory_type=MemoryType.PERSONAL,
                interaction_type=InteractionType.CONVERSATION,
                content=f"Async task {task_id} completed successfully",
                emotion=EmotionType.JOY
            )
            
            return {
                "task_id": task_id,
                "memory_id": memory.id,
                "content_hash": memory.get_content_hash()
            }
        
        # Test concurrent execution
        start_time = time.time()
        tasks = [async_task(i) for i in range(50)]
        results = await asyncio.gather(*tasks)
        execution_time = time.time() - start_time
        
        return {
            "tasks_completed": len(results),
            "execution_time": execution_time,
            "tasks_per_second": len(results) / execution_time,
            "avg_task_time": execution_time / len(results)
        }
    
    def test_memory_efficiency(self) -> Dict[str, Any]:
        """Test memory usage efficiency."""
        logger.info("💾 Testing memory efficiency...")
        
        import sys
        
        # Test memory usage of different objects
        memory_entry = MemoryEntry(
            id="test_memory",
            user_id="test_user",
            memory_type=MemoryType.PERSONAL,
            interaction_type=InteractionType.CONVERSATION,
            content="Test content for memory efficiency testing",
            emotion=EmotionType.NEUTRAL
        )
        
        emotional_state = EmotionalState(
            primary_emotion=EmotionType.JOY,
            intensity=0.7,
            confidence=0.8
        )
        
        user_profile = UserProfile(
            user_id="test_user",
            name="Test User",
            preferences={"style": "casual"},
            interests=["tech", "science"]
        )
        
        # Get approximate object sizes
        memory_size = sys.getsizeof(memory_entry)
        emotion_size = sys.getsizeof(emotional_state)
        profile_size = sys.getsizeof(user_profile)
        
        return {
            "memory_entry_size": memory_size,
            "emotional_state_size": emotion_size,
            "user_profile_size": profile_size,
            "total_size": memory_size + emotion_size + profile_size
        }
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all performance tests."""
        logger.info("🏁 Starting simple performance tests...")
        
        results = {}
        
        try:
            # Test model creation
            results["model_creation"] = self.test_model_creation_performance()
            
            # Test emotional state processing
            results["emotional_state"] = self.test_emotional_state_performance()
            
            # Test user profile operations
            results["user_profile"] = self.test_user_profile_performance()
            
            # Test async operations
            results["async_operations"] = await self.test_async_operations()
            
            # Test memory efficiency
            results["memory_efficiency"] = self.test_memory_efficiency()
            
            logger.info("✅ All simple tests completed successfully!")
            
        except Exception as e:
            logger.error(f"❌ Test failed: {e}")
            results["error"] = str(e)
        
        return results
    
    def print_test_summary(self, results: Dict[str, Any]):
        """Print a summary of test results."""
        print("\n" + "="*60)
        print("🏆 SIMPLE PERFORMANCE TEST RESULTS")
        print("="*60)
        
        if "model_creation" in results:
            model = results["model_creation"]
            print(f"Model Creation Performance:")
            print(f"  - Created {model['memories_created']} memories in {model['creation_time']:.3f}s")
            print(f"  - Rate: {model['memories_per_second']:.1f} memories/second")
            print(f"  - Avg creation time: {model['avg_creation_time']*1000:.2f}ms")
        
        if "emotional_state" in results:
            emotion = results["emotional_state"]
            print(f"Emotional State Performance:")
            print(f"  - Created {emotion['states_created']} states in {emotion['creation_time']:.3f}s")
            print(f"  - Rate: {emotion['states_per_second']:.1f} states/second")
        
        if "user_profile" in results:
            profile = results["user_profile"]
            print(f"User Profile Performance:")
            print(f"  - Created {profile['profiles_created']} profiles in {profile['creation_time']:.3f}s")
            print(f"  - Rate: {profile['profiles_per_second']:.1f} profiles/second")
        
        if "async_operations" in results:
            async_ops = results["async_operations"]
            print(f"Async Operations Performance:")
            print(f"  - Completed {async_ops['tasks_completed']} tasks in {async_ops['execution_time']:.3f}s")
            print(f"  - Rate: {async_ops['tasks_per_second']:.1f} tasks/second")
        
        if "memory_efficiency" in results:
            memory = results["memory_efficiency"]
            print(f"Memory Efficiency:")
            print(f"  - Memory entry: {memory['memory_entry_size']} bytes")
            print(f"  - Emotional state: {memory['emotional_state_size']} bytes")
            print(f"  - User profile: {memory['user_profile_size']} bytes")
        
        if "error" in results:
            print(f"❌ Error: {results['error']}")
        else:
            print("\n✅ All optimizations working correctly!")
            print("🚀 System ready for high-performance operation!")

async def main():
    """Run the simple performance test."""
    test_suite = SimplePerformanceTest()
    results = await test_suite.run_all_tests()
    test_suite.print_test_summary(results)

if __name__ == "__main__":
    asyncio.run(main())
