# API Documentation

The AI Companion System provides a RESTful API for integration with other applications.

## Base URL

```
http://localhost:8000
```

## Authentication

Currently, the API uses simple user identification. In production, implement proper authentication.

## Endpoints

### Health Check

Check system health and status.

```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "version": "1.0.0",
  "uptime": 3600.5
}
```

### System Metrics

Get performance and system metrics.

```http
GET /metrics
```

**Response:**
```json
{
  "response_time": {
    "average": 0.25,
    "target": 0.3,
    "meets_target": true
  },
  "cache": {
    "hit_rate": 0.85,
    "target": 0.8,
    "meets_target": true
  },
  "system": {
    "memory_usage": 45.2,
    "cpu_usage": 12.5,
    "uptime": 3600.5
  }
}
```

### Chat Conversation

Send a message and receive an AI response.

```http
POST /chat
```

**Request Body:**
```json
{
  "message": "I'm feeling anxious about work",
  "user_id": "user123",
  "context": {
    "session_id": "session456",
    "timestamp": "2024-01-01T00:00:00Z"
  }
}
```

**Response:**
```json
{
  "response": "I understand that work can be a source of anxiety. Would you like to talk about what specifically is making you feel this way?",
  "emotion_detected": "anxiety",
  "confidence": 0.85,
  "risk_level": "low",
  "response_time": 0.23,
  "conversation_id": "conv789"
}
```

### User Profile

Get or update user profile information.

```http
GET /users/{user_id}/profile
```

**Response:**
```json
{
  "user_id": "user123",
  "name": "John Doe",
  "preferences": {
    "communication_style": "supportive",
    "topics_of_interest": ["work", "relationships"]
  },
  "emotional_patterns": {
    "anxiety": 0.3,
    "stress": 0.4,
    "joy": 0.6
  },
  "interaction_count": 45,
  "last_interaction": "2024-01-01T00:00:00Z"
}
```

### Memory Retrieval

Retrieve relevant memories for a user.

```http
GET /users/{user_id}/memories?query=work&limit=5
```

**Response:**
```json
{
  "memories": [
    {
      "id": "mem123",
      "content": "User mentioned stress about upcoming presentation",
      "emotion": "anxiety",
      "importance": 0.8,
      "created_at": "2024-01-01T00:00:00Z",
      "relevance_score": 0.95
    }
  ],
  "total_count": 1
}
```

### Crisis Assessment

Assess mental health risk for a message.

```http
POST /crisis/assess
```

**Request Body:**
```json
{
  "message": "I can't handle this anymore",
  "user_id": "user123",
  "context": {
    "recent_messages": ["I'm so tired", "Nothing is working"]
  }
}
```

**Response:**
```json
{
  "risk_level": "high",
  "confidence": 0.92,
  "risk_factors": ["hopelessness", "exhaustion"],
  "recommended_actions": ["immediate_support", "professional_referral"],
  "crisis_response": "I'm really concerned about how you're feeling right now. You're not alone, and there are people who want to help."
}
```

## Error Responses

All endpoints return appropriate HTTP status codes and error messages:

```json
{
  "error": "Invalid user ID format",
  "code": "INVALID_USER_ID",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Common Error Codes

- `400 Bad Request`: Invalid request format or parameters
- `401 Unauthorized`: Authentication required
- `404 Not Found`: Resource not found
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Default**: 100 requests per minute per user
- **Headers**: Rate limit information is included in response headers

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## WebSocket Support

For real-time conversations, the API supports WebSocket connections:

```javascript
const ws = new WebSocket('ws://localhost:8000/ws/chat/user123');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('AI Response:', data.response);
};

ws.send(JSON.stringify({
    message: "Hello!",
    timestamp: new Date().toISOString()
}));
```

## SDK and Client Libraries

### Python Client

```python
from ai_companion_client import AICompanionClient

client = AICompanionClient(base_url="http://localhost:8000")

response = client.chat(
    message="I'm feeling stressed",
    user_id="user123"
)

print(response.message)
print(f"Emotion detected: {response.emotion}")
```

### JavaScript Client

```javascript
import { AICompanionClient } from 'ai-companion-client';

const client = new AICompanionClient('http://localhost:8000');

const response = await client.chat({
    message: "I'm feeling stressed",
    userId: "user123"
});

console.log(response.message);
console.log(`Emotion detected: ${response.emotion}`);
```

## Interactive API Documentation

When the system is running, visit http://localhost:8000/docs for interactive API documentation powered by Swagger UI.
