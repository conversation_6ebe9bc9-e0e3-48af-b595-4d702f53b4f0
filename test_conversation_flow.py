"""
Test conversation flow with the optimized system.
Tests the actual conversation experience without complex dependencies.
"""

import asyncio
import time
import logging
from typing import Dict, Any, List

# Import the basic services we can test
from models import (
    MemoryEntry, MemoryType, InteractionType, EmotionType,
    UserProfile, EmotionalState, EmpathyModel
)
from config import settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockGeminiService:
    """Mock Gemini service for testing without API calls."""
    
    def __init__(self):
        self.response_cache = {}
        self.call_count = 0
    
    async def generate_response_async(self, prompt: str) -> str:
        """Generate a mock response based on the prompt."""
        self.call_count += 1
        
        # Simple response generation based on keywords
        prompt_lower = prompt.lower()
        
        if "anxious" in prompt_lower or "worried" in prompt_lower:
            return "I understand you're feeling anxious. It's completely normal to feel this way sometimes. Take a deep breath - you're not alone in this."
        elif "happy" in prompt_lower or "excited" in prompt_lower:
            return "That's wonderful to hear! I love seeing your enthusiasm. What's making you feel so positive today?"
        elif "sad" in prompt_lower or "down" in prompt_lower:
            return "I can hear that you're going through a difficult time. I'm here to listen and support you through this."
        elif "angry" in prompt_lower or "frustrated" in prompt_lower:
            return "It sounds like you're feeling really frustrated. Those feelings are valid. Would you like to talk about what's bothering you?"
        elif "hello" in prompt_lower or "hi" in prompt_lower:
            return "Hello! It's great to connect with you. How are you feeling today?"
        else:
            return "I'm here to listen and chat with you. What's on your mind today?"

class SimpleConversationTest:
    """Test conversation flow with optimized components."""
    
    def __init__(self):
        """Initialize the test."""
        self.gemini_service = MockGeminiService()
        self.conversation_history = []
        self.user_profile = None
        self.current_emotional_state = EmotionalState(primary_emotion=EmotionType.NEUTRAL)
        
    def detect_emotion_fast(self, message: str) -> EmotionalState:
        """Fast emotion detection for testing."""
        emotion_keywords = {
            EmotionType.JOY: ["happy", "excited", "great", "wonderful", "amazing", "love"],
            EmotionType.SADNESS: ["sad", "depressed", "down", "upset", "crying", "hurt"],
            EmotionType.ANGER: ["angry", "mad", "furious", "annoyed", "frustrated"],
            EmotionType.ANXIETY: ["anxious", "worried", "nervous", "stressed", "panic"],
            EmotionType.FEAR: ["scared", "afraid", "terrified", "frightened"],
        }
        
        message_lower = message.lower()
        detected_emotions = {}
        
        for emotion, keywords in emotion_keywords.items():
            score = sum(1 for keyword in keywords if keyword in message_lower)
            if score > 0:
                detected_emotions[emotion] = score / len(keywords)
        
        if detected_emotions:
            primary_emotion = max(detected_emotions, key=detected_emotions.get)
            confidence = detected_emotions[primary_emotion]
        else:
            primary_emotion = EmotionType.NEUTRAL
            confidence = 0.5
        
        return EmotionalState(
            primary_emotion=primary_emotion,
            confidence=confidence,
            intensity=min(confidence * 1.5, 1.0),
            valence=0.5 if primary_emotion == EmotionType.JOY else -0.3 if primary_emotion in [EmotionType.SADNESS, EmotionType.ANGER] else 0.0,
            arousal=confidence if primary_emotion in [EmotionType.ANGER, EmotionType.ANXIETY] else 0.3
        )
    
    def create_empathy_model(self, emotional_state: EmotionalState) -> EmpathyModel:
        """Create empathy model based on emotional state."""
        empathy_level = 0.8 if emotional_state.valence < 0 else 0.6
        
        response_strategy = "supportive"
        if emotional_state.primary_emotion in [EmotionType.SADNESS, EmotionType.ANXIETY]:
            response_strategy = "validating"
        elif emotional_state.primary_emotion == EmotionType.ANGER:
            response_strategy = "calming"
        elif emotional_state.primary_emotion == EmotionType.JOY:
            response_strategy = "encouraging"
        
        return EmpathyModel(
            user_emotional_state=emotional_state,
            empathy_level=empathy_level,
            response_strategy=response_strategy,
            emotional_mirroring=0.3
        )
    
    async def process_message_fast(self, user_message: str) -> Dict[str, Any]:
        """Process a message with optimized flow."""
        start_time = time.time()
        
        try:
            # Step 1: Emotion detection (fast)
            emotional_state = self.detect_emotion_fast(user_message)
            self.current_emotional_state = emotional_state
            
            # Step 2: Create empathy model
            empathy_model = self.create_empathy_model(emotional_state)
            
            # Step 3: Build context for response
            recent_context = []
            for msg in self.conversation_history[-3:]:  # Last 3 messages
                recent_context.append(f"{msg['role']}: {msg['content']}")
            
            context_str = "\n".join(recent_context) if recent_context else ""
            
            # Step 4: Create prompt
            prompt = self.create_optimized_prompt(user_message, emotional_state, context_str)
            
            # Step 5: Generate response
            response = await self.gemini_service.generate_response_async(prompt)
            
            # Step 6: Update conversation history
            self.conversation_history.append({
                "role": "user",
                "content": user_message,
                "emotion": emotional_state.primary_emotion.value,
                "timestamp": time.time()
            })
            
            self.conversation_history.append({
                "role": "assistant",
                "content": response,
                "timestamp": time.time()
            })
            
            # Keep history manageable
            if len(self.conversation_history) > 20:
                self.conversation_history = self.conversation_history[-20:]
            
            processing_time = time.time() - start_time
            
            return {
                "response": response,
                "emotion": emotional_state.primary_emotion.value,
                "confidence": emotional_state.confidence,
                "intensity": emotional_state.intensity,
                "valence": emotional_state.valence,
                "empathy_level": empathy_model.empathy_level,
                "response_strategy": empathy_model.response_strategy,
                "processing_time": processing_time,
                "needs_support": empathy_model.should_provide_support()
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Error processing message: {e}")
            return {
                "error": str(e),
                "response": "I'm experiencing some technical difficulties. Please try again.",
                "processing_time": processing_time
            }
    
    def create_optimized_prompt(self, user_message: str, emotional_state: EmotionalState, context: str) -> str:
        """Create optimized prompt for response generation."""
        emotion_guidance = {
            EmotionType.SADNESS: "Be empathetic and supportive. Acknowledge their feelings and offer comfort.",
            EmotionType.ANXIETY: "Be calming and reassuring. Help them feel safe and supported.",
            EmotionType.ANGER: "Be understanding and help them process their feelings constructively.",
            EmotionType.JOY: "Share in their happiness and be enthusiastic while staying grounded.",
            EmotionType.NEUTRAL: "Be friendly, engaging, and helpful."
        }
        
        guidance = emotion_guidance.get(emotional_state.primary_emotion, "Be helpful and friendly.")
        
        prompt = f"""You are a caring AI companion. {guidance}

Recent conversation:
{context}

User's current message: {user_message}
Detected emotion: {emotional_state.primary_emotion.value} (confidence: {emotional_state.confidence:.2f})
Emotional intensity: {emotional_state.intensity:.2f}
Emotional valence: {emotional_state.valence:.2f}

Respond naturally and empathetically in 1-2 sentences. Be conversational and human-like."""
        
        return prompt
    
    async def test_conversation_scenarios(self) -> Dict[str, Any]:
        """Test various conversation scenarios."""
        logger.info("💬 Testing conversation scenarios...")
        
        test_scenarios = [
            {
                "name": "Greeting",
                "message": "Hello, how are you today?",
                "expected_emotion": EmotionType.NEUTRAL
            },
            {
                "name": "Anxiety",
                "message": "I'm feeling really anxious about my job interview tomorrow.",
                "expected_emotion": EmotionType.ANXIETY
            },
            {
                "name": "Joy",
                "message": "I just got promoted at work! I'm so excited!",
                "expected_emotion": EmotionType.JOY
            },
            {
                "name": "Sadness",
                "message": "I'm feeling really down today. Everything seems difficult.",
                "expected_emotion": EmotionType.SADNESS
            },
            {
                "name": "Anger",
                "message": "I'm so frustrated with my situation. Nothing is going right!",
                "expected_emotion": EmotionType.ANGER
            },
            {
                "name": "Follow-up",
                "message": "Thank you for listening. That really helped.",
                "expected_emotion": EmotionType.NEUTRAL
            }
        ]
        
        results = []
        total_time = 0
        
        for scenario in test_scenarios:
            start_time = time.time()
            result = await self.process_message_fast(scenario["message"])
            scenario_time = time.time() - start_time
            total_time += scenario_time
            
            # Check if emotion detection worked
            detected_emotion = EmotionType(result.get("emotion", "neutral"))
            emotion_correct = detected_emotion == scenario["expected_emotion"]
            
            scenario_result = {
                "scenario": scenario["name"],
                "message": scenario["message"],
                "expected_emotion": scenario["expected_emotion"].value,
                "detected_emotion": result.get("emotion", "unknown"),
                "emotion_correct": emotion_correct,
                "response": result.get("response", ""),
                "processing_time": result.get("processing_time", 0),
                "needs_support": result.get("needs_support", False),
                "empathy_level": result.get("empathy_level", 0),
                "response_strategy": result.get("response_strategy", "")
            }
            
            results.append(scenario_result)
            logger.info(f"  {scenario['name']}: {scenario_time:.3f}s - {detected_emotion.value}")
        
        return {
            "scenarios": results,
            "total_scenarios": len(test_scenarios),
            "total_time": total_time,
            "avg_time_per_scenario": total_time / len(test_scenarios),
            "emotion_accuracy": sum(1 for r in results if r["emotion_correct"]) / len(results),
            "gemini_calls": self.gemini_service.call_count
        }
    
    def print_conversation_results(self, results: Dict[str, Any]):
        """Print conversation test results."""
        print("\n" + "="*60)
        print("💬 CONVERSATION FLOW TEST RESULTS")
        print("="*60)
        
        print(f"Total Scenarios: {results['total_scenarios']}")
        print(f"Total Time: {results['total_time']:.3f}s")
        print(f"Avg Time per Scenario: {results['avg_time_per_scenario']:.3f}s")
        print(f"Emotion Detection Accuracy: {results['emotion_accuracy']:.1%}")
        print(f"Mock Gemini Calls: {results['gemini_calls']}")
        
        print("\nScenario Details:")
        for scenario in results['scenarios']:
            status = "✅" if scenario['emotion_correct'] else "❌"
            print(f"  {status} {scenario['scenario']}: {scenario['processing_time']:.3f}s")
            print(f"     Emotion: {scenario['detected_emotion']} (expected: {scenario['expected_emotion']})")
            print(f"     Strategy: {scenario['response_strategy']}")
            print(f"     Response: {scenario['response'][:80]}...")
            print()
        
        print("✅ Conversation flow test completed!")

async def main():
    """Run the conversation flow test."""
    test = SimpleConversationTest()
    results = await test.test_conversation_scenarios()
    test.print_conversation_results(results)

if __name__ == "__main__":
    asyncio.run(main())
