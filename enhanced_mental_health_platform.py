"""
Enhanced Mental Health Data Platform for AI Companion System.
Implements privacy-first analytics, anonymization pipelines, and professional insights.

Key Features:
- HIPAA-compliant data handling
- Advanced anonymization techniques
- Real-time mental health analytics
- Professional dashboard for researchers
- Crisis pattern detection
- Population-level insights
- Ethical AI monitoring
"""

import asyncio
import logging
import hashlib
import json
import numpy as np
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
import uuid
from collections import defaultdict, Counter
import pandas as pd
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
import sqlite3
import redis

from models import EmotionType, InteractionType, utc_now
from next_gen_emotional_intelligence import MentalHealthRisk, EmotionalState

logger = logging.getLogger(__name__)

class DataPrivacyLevel(Enum):
    """Data privacy levels for different types of information."""
    PUBLIC = "public"           # Fully anonymized, safe for public research
    RESEARCH = "research"       # Anonymized for academic research
    CLINICAL = "clinical"       # De-identified for clinical use
    INTERNAL = "internal"       # Internal analytics only
    RESTRICTED = "restricted"   # Highest privacy, minimal processing

class AnonymizationTechnique(Enum):
    """Anonymization techniques for data protection."""
    HASHING = "hashing"
    DIFFERENTIAL_PRIVACY = "differential_privacy"
    K_ANONYMITY = "k_anonymity"
    L_DIVERSITY = "l_diversity"
    T_CLOSENESS = "t_closeness"
    SYNTHETIC_DATA = "synthetic_data"

@dataclass
class AnonymizedInsight:
    """Anonymized mental health insight for research."""
    insight_id: str
    insight_type: str
    
    # Aggregated data (no individual identifiers)
    population_size: int
    time_period: str
    geographic_region: Optional[str] = None
    
    # Mental health metrics
    emotional_patterns: Dict[str, float] = field(default_factory=dict)
    risk_distributions: Dict[str, int] = field(default_factory=dict)
    intervention_effectiveness: Dict[str, float] = field(default_factory=dict)
    
    # Demographic insights (anonymized)
    age_groups: Dict[str, int] = field(default_factory=dict)
    session_patterns: Dict[str, float] = field(default_factory=dict)
    
    # Privacy metadata
    privacy_level: DataPrivacyLevel = DataPrivacyLevel.RESEARCH
    anonymization_techniques: List[AnonymizationTechnique] = field(default_factory=list)
    k_anonymity_level: int = 5
    
    # Temporal data
    created_at: datetime = field(default_factory=utc_now)
    data_retention_until: datetime = field(default_factory=lambda: utc_now() + timedelta(days=365))

@dataclass
class CrisisEvent:
    """Anonymized crisis event for pattern analysis."""
    event_id: str
    anonymized_user_id: str  # One-way hashed
    
    # Crisis details
    crisis_type: str
    emotional_state: Dict[str, Any]
    risk_level: MentalHealthRisk
    intervention_provided: str
    
    # Outcome tracking
    immediate_response_effective: Optional[bool] = None
    follow_up_required: bool = False
    professional_escalation: bool = False
    
    # Temporal and contextual data
    time_of_day: str
    day_of_week: str
    session_duration_before_crisis: float  # minutes
    
    # Privacy protection
    timestamp: datetime = field(default_factory=utc_now)
    geographic_hash: Optional[str] = None  # Coarse geographic region hash

class EnhancedMentalHealthPlatform:
    """
    Enhanced mental health data platform with privacy-first design.
    
    Features:
    - Advanced anonymization and privacy protection
    - Real-time mental health analytics
    - Crisis pattern detection
    - Professional research dashboard
    - Ethical AI monitoring
    """
    
    def __init__(self, database_path: str = "mental_health_platform.db"):
        """Initialize the enhanced mental health platform."""
        self.database_path = database_path
        self.redis_client = redis.Redis(host='localhost', port=6379, db=1)
        
        # Privacy and anonymization
        self.anonymization_salt = self._generate_anonymization_salt()
        self.k_anonymity_threshold = 5
        self.differential_privacy_epsilon = 1.0
        
        # Analytics and insights
        self.insight_cache: Dict[str, AnonymizedInsight] = {}
        self.crisis_events: List[CrisisEvent] = []
        self.population_metrics = defaultdict(list)
        
        # Professional dashboard data
        self.research_insights: List[Dict[str, Any]] = []
        self.clinical_patterns: Dict[str, Any] = {}
        
        # Initialize database
        self._initialize_database()
        
        # Start background analytics
        asyncio.create_task(self._background_analytics())
        
        logger.info("Enhanced Mental Health Platform initialized")
    
    def _generate_anonymization_salt(self) -> str:
        """Generate cryptographic salt for anonymization."""
        return hashlib.sha256(str(uuid.uuid4()).encode()).hexdigest()
    
    def _initialize_database(self):
        """Initialize SQLite database for analytics storage."""
        with sqlite3.connect(self.database_path) as conn:
            cursor = conn.cursor()
            
            # Anonymized insights table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS anonymized_insights (
                    insight_id TEXT PRIMARY KEY,
                    insight_type TEXT NOT NULL,
                    population_size INTEGER NOT NULL,
                    time_period TEXT NOT NULL,
                    emotional_patterns TEXT,
                    risk_distributions TEXT,
                    intervention_effectiveness TEXT,
                    privacy_level TEXT NOT NULL,
                    k_anonymity_level INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Crisis events table (anonymized)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS crisis_events (
                    event_id TEXT PRIMARY KEY,
                    anonymized_user_id TEXT NOT NULL,
                    crisis_type TEXT NOT NULL,
                    risk_level TEXT NOT NULL,
                    intervention_provided TEXT,
                    time_of_day TEXT,
                    day_of_week TEXT,
                    professional_escalation BOOLEAN,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Population metrics table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS population_metrics (
                    metric_id TEXT PRIMARY KEY,
                    metric_type TEXT NOT NULL,
                    metric_value REAL NOT NULL,
                    population_size INTEGER NOT NULL,
                    time_period TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
    
    async def record_crisis_event(
        self,
        user_id: str,
        message: str,
        emotional_state: EmotionalState,
        intervention_provided: str
    ):
        """Record a crisis event with privacy protection."""
        try:
            # Create anonymized user ID
            anonymized_user_id = self._anonymize_user_id(user_id)
            
            # Create crisis event
            crisis_event = CrisisEvent(
                event_id=str(uuid.uuid4()),
                anonymized_user_id=anonymized_user_id,
                crisis_type=self._classify_crisis_type(message),
                emotional_state={
                    'primary_emotion': emotional_state.primary_emotion.value,
                    'intensity': round(emotional_state.intensity, 2),
                    'valence': round(emotional_state.valence, 2),
                    'arousal': round(emotional_state.arousal, 2)
                },
                risk_level=MentalHealthRisk.CRITICAL,  # Crisis events are high risk
                intervention_provided=intervention_provided,
                time_of_day=self._get_time_category(),
                day_of_week=datetime.now().strftime('%A')
            )
            
            # Store in memory for real-time analytics
            self.crisis_events.append(crisis_event)
            
            # Store in database
            await self._store_crisis_event(crisis_event)
            
            # Trigger real-time alerts if needed
            await self._check_crisis_patterns(crisis_event)
            
            logger.warning(f"Crisis event recorded: {crisis_event.event_id}")
            
        except Exception as e:
            logger.error(f"Error recording crisis event: {e}")
    
    def _anonymize_user_id(self, user_id: str) -> str:
        """Create anonymized user ID using cryptographic hashing."""
        combined = f"{user_id}{self.anonymization_salt}"
        return hashlib.sha256(combined.encode()).hexdigest()[:16]
    
    def _classify_crisis_type(self, message: str) -> str:
        """Classify crisis type from message content."""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ['suicide', 'kill myself', 'end it all']):
            return 'suicide_ideation'
        elif any(word in message_lower for word in ['hurt myself', 'cut myself', 'self harm']):
            return 'self_harm'
        elif any(word in message_lower for word in ['panic', 'can\'t breathe', 'heart racing']):
            return 'panic_attack'
        elif any(word in message_lower for word in ['overdose', 'pills', 'drinking']):
            return 'substance_crisis'
        else:
            return 'general_crisis'
    
    def _get_time_category(self) -> str:
        """Get time category for temporal analysis."""
        hour = datetime.now().hour
        if 6 <= hour < 12:
            return 'morning'
        elif 12 <= hour < 18:
            return 'afternoon'
        elif 18 <= hour < 22:
            return 'evening'
        else:
            return 'night'
    
    async def _store_crisis_event(self, crisis_event: CrisisEvent):
        """Store crisis event in database."""
        with sqlite3.connect(self.database_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO crisis_events (
                    event_id, anonymized_user_id, crisis_type, risk_level,
                    intervention_provided, time_of_day, day_of_week,
                    professional_escalation, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                crisis_event.event_id,
                crisis_event.anonymized_user_id,
                crisis_event.crisis_type,
                crisis_event.risk_level.value,
                crisis_event.intervention_provided,
                crisis_event.time_of_day,
                crisis_event.day_of_week,
                crisis_event.professional_escalation,
                crisis_event.timestamp
            ))
            conn.commit()
    
    async def _check_crisis_patterns(self, crisis_event: CrisisEvent):
        """Check for concerning crisis patterns."""
        # Check for multiple crises from same anonymized user
        recent_crises = [
            event for event in self.crisis_events
            if (event.anonymized_user_id == crisis_event.anonymized_user_id and
                (crisis_event.timestamp - event.timestamp).total_seconds() < 86400)  # 24 hours
        ]
        
        if len(recent_crises) > 2:
            logger.critical(f"Multiple crisis events detected for user {crisis_event.anonymized_user_id}")
            await self._trigger_enhanced_monitoring(crisis_event.anonymized_user_id)
    
    async def _trigger_enhanced_monitoring(self, anonymized_user_id: str):
        """Trigger enhanced monitoring for high-risk users."""
        # This would integrate with professional monitoring systems
        logger.critical(f"Enhanced monitoring triggered for user {anonymized_user_id}")
        
        # Store alert in Redis for real-time dashboard
        alert_data = {
            'type': 'enhanced_monitoring',
            'user_id': anonymized_user_id,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'reason': 'multiple_crisis_events'
        }
        
        self.redis_client.lpush('crisis_alerts', json.dumps(alert_data))
        self.redis_client.expire('crisis_alerts', 86400)  # 24 hour expiry
    
    async def generate_population_insights(
        self,
        time_period: str = "last_30_days",
        min_population_size: int = 100
    ) -> AnonymizedInsight:
        """Generate anonymized population-level insights."""
        try:
            # Collect anonymized data
            population_data = await self._collect_population_data(time_period)
            
            if len(population_data) < min_population_size:
                logger.warning(f"Insufficient data for insights: {len(population_data)} < {min_population_size}")
                return None
            
            # Apply k-anonymity
            anonymized_data = self._apply_k_anonymity(population_data, k=self.k_anonymity_threshold)
            
            # Generate insights
            insight = AnonymizedInsight(
                insight_id=str(uuid.uuid4()),
                insight_type="population_mental_health",
                population_size=len(anonymized_data),
                time_period=time_period,
                emotional_patterns=self._analyze_emotional_patterns(anonymized_data),
                risk_distributions=self._analyze_risk_distributions(anonymized_data),
                intervention_effectiveness=self._analyze_intervention_effectiveness(anonymized_data),
                privacy_level=DataPrivacyLevel.RESEARCH,
                anonymization_techniques=[
                    AnonymizationTechnique.HASHING,
                    AnonymizationTechnique.K_ANONYMITY
                ],
                k_anonymity_level=self.k_anonymity_threshold
            )
            
            # Store insight
            await self._store_insight(insight)
            
            logger.info(f"Generated population insight: {insight.insight_id}")
            return insight
            
        except Exception as e:
            logger.error(f"Error generating population insights: {e}")
            return None
    
    async def _collect_population_data(self, time_period: str) -> List[Dict[str, Any]]:
        """Collect anonymized population data for analysis."""
        # Calculate time range
        end_time = datetime.now(timezone.utc)
        if time_period == "last_7_days":
            start_time = end_time - timedelta(days=7)
        elif time_period == "last_30_days":
            start_time = end_time - timedelta(days=30)
        elif time_period == "last_90_days":
            start_time = end_time - timedelta(days=90)
        else:
            start_time = end_time - timedelta(days=30)  # Default
        
        # Query database for anonymized data
        with sqlite3.connect(self.database_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT anonymized_user_id, crisis_type, risk_level, 
                       time_of_day, day_of_week, professional_escalation,
                       timestamp
                FROM crisis_events
                WHERE timestamp BETWEEN ? AND ?
            """, (start_time, end_time))
            
            rows = cursor.fetchall()
            
            # Convert to list of dictionaries
            columns = ['anonymized_user_id', 'crisis_type', 'risk_level', 
                      'time_of_day', 'day_of_week', 'professional_escalation', 'timestamp']
            
            return [dict(zip(columns, row)) for row in rows]
    
    def _apply_k_anonymity(self, data: List[Dict[str, Any]], k: int) -> List[Dict[str, Any]]:
        """Apply k-anonymity to protect individual privacy."""
        if len(data) < k:
            return []
        
        # Group by quasi-identifiers
        groups = defaultdict(list)
        for record in data:
            # Use time_of_day and day_of_week as quasi-identifiers
            key = (record['time_of_day'], record['day_of_week'])
            groups[key].append(record)
        
        # Keep only groups with at least k members
        anonymized_data = []
        for group in groups.values():
            if len(group) >= k:
                anonymized_data.extend(group)
        
        return anonymized_data
    
    def _analyze_emotional_patterns(self, data: List[Dict[str, Any]]) -> Dict[str, float]:
        """Analyze emotional patterns in population data."""
        crisis_types = [record['crisis_type'] for record in data]
        crisis_counter = Counter(crisis_types)
        total = len(data)
        
        return {
            crisis_type: count / total
            for crisis_type, count in crisis_counter.items()
        }
    
    def _analyze_risk_distributions(self, data: List[Dict[str, Any]]) -> Dict[str, int]:
        """Analyze risk level distributions."""
        risk_levels = [record['risk_level'] for record in data]
        return dict(Counter(risk_levels))
    
    def _analyze_intervention_effectiveness(self, data: List[Dict[str, Any]]) -> Dict[str, float]:
        """Analyze intervention effectiveness metrics."""
        total_interventions = len(data)
        professional_escalations = sum(1 for record in data if record['professional_escalation'])
        
        return {
            'professional_escalation_rate': professional_escalations / total_interventions if total_interventions > 0 else 0,
            'ai_intervention_rate': (total_interventions - professional_escalations) / total_interventions if total_interventions > 0 else 0,
            'total_interventions': total_interventions
        }
    
    async def _store_insight(self, insight: AnonymizedInsight):
        """Store anonymized insight in database."""
        with sqlite3.connect(self.database_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO anonymized_insights (
                    insight_id, insight_type, population_size, time_period,
                    emotional_patterns, risk_distributions, intervention_effectiveness,
                    privacy_level, k_anonymity_level, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                insight.insight_id,
                insight.insight_type,
                insight.population_size,
                insight.time_period,
                json.dumps(insight.emotional_patterns),
                json.dumps(insight.risk_distributions),
                json.dumps(insight.intervention_effectiveness),
                insight.privacy_level.value,
                insight.k_anonymity_level,
                insight.created_at
            ))
            conn.commit()
    
    async def _background_analytics(self):
        """Background process for continuous analytics."""
        while True:
            try:
                # Generate daily insights
                await self.generate_population_insights("last_7_days")
                
                # Clean up old data
                await self._cleanup_old_data()
                
                # Update real-time metrics
                await self._update_real_time_metrics()
                
                # Sleep for 1 hour
                await asyncio.sleep(3600)
                
            except Exception as e:
                logger.error(f"Error in background analytics: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes before retry
    
    async def _cleanup_old_data(self):
        """Clean up old data based on retention policies."""
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=365)  # 1 year retention
        
        with sqlite3.connect(self.database_path) as conn:
            cursor = conn.cursor()
            
            # Clean up old crisis events
            cursor.execute("DELETE FROM crisis_events WHERE timestamp < ?", (cutoff_date,))
            
            # Clean up old insights
            cursor.execute("DELETE FROM anonymized_insights WHERE created_at < ?", (cutoff_date,))
            
            conn.commit()
    
    async def _update_real_time_metrics(self):
        """Update real-time metrics in Redis."""
        # Calculate current metrics
        total_crises = len(self.crisis_events)
        recent_crises = [
            event for event in self.crisis_events
            if (datetime.now(timezone.utc) - event.timestamp).total_seconds() < 86400
        ]
        
        metrics = {
            'total_crisis_events': total_crises,
            'daily_crisis_events': len(recent_crises),
            'crisis_types': dict(Counter(event.crisis_type for event in recent_crises)),
            'time_distribution': dict(Counter(event.time_of_day for event in recent_crises)),
            'last_updated': datetime.now(timezone.utc).isoformat()
        }
        
        # Store in Redis
        self.redis_client.setex('real_time_metrics', 3600, json.dumps(metrics))
    
    async def archive_session_data(self, session_data: Dict[str, Any]):
        """Archive anonymized session data for research."""
        try:
            # Anonymize session data
            anonymized_session = {
                'session_id': session_data['session_id'],  # Already anonymized
                'session_duration': session_data['session_duration'],
                'message_count': session_data['message_count'],
                'emotional_summary': session_data.get('emotional_summary', {}),
                'therapeutic_techniques': session_data.get('therapeutic_techniques_used', []),
                'crisis_occurred': session_data.get('crisis_occurred', False),
                'archived_at': session_data['archived_at']
            }
            
            # Store for population analytics
            metric_id = str(uuid.uuid4())
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO population_metrics (
                        metric_id, metric_type, metric_value, population_size, time_period
                    ) VALUES (?, ?, ?, ?, ?)
                """, (
                    metric_id,
                    'session_duration',
                    session_data['session_duration'],
                    1,  # Single session
                    'daily'
                ))
                conn.commit()
            
            logger.info(f"Archived session data: {session_data['session_id']}")
            
        except Exception as e:
            logger.error(f"Error archiving session data: {e}")
    
    def get_professional_dashboard_data(self) -> Dict[str, Any]:
        """Get data for professional mental health dashboard."""
        try:
            # Get real-time metrics from Redis
            metrics_data = self.redis_client.get('real_time_metrics')
            real_time_metrics = json.loads(metrics_data) if metrics_data else {}
            
            # Get recent insights
            recent_insights = []
            with sqlite3.connect(self.database_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT insight_id, insight_type, population_size, 
                           emotional_patterns, risk_distributions, created_at
                    FROM anonymized_insights
                    WHERE created_at > datetime('now', '-7 days')
                    ORDER BY created_at DESC
                    LIMIT 10
                """)
                
                for row in cursor.fetchall():
                    recent_insights.append({
                        'insight_id': row[0],
                        'insight_type': row[1],
                        'population_size': row[2],
                        'emotional_patterns': json.loads(row[3]) if row[3] else {},
                        'risk_distributions': json.loads(row[4]) if row[4] else {},
                        'created_at': row[5]
                    })
            
            return {
                'real_time_metrics': real_time_metrics,
                'recent_insights': recent_insights,
                'platform_status': 'operational',
                'privacy_compliance': {
                    'k_anonymity_level': self.k_anonymity_threshold,
                    'data_retention_days': 365,
                    'anonymization_techniques': ['hashing', 'k_anonymity']
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting dashboard data: {e}")
            return {'error': str(e)}
    
    def get_research_insights(self, research_type: str = "all") -> List[AnonymizedInsight]:
        """Get anonymized insights for research purposes."""
        insights = []
        
        with sqlite3.connect(self.database_path) as conn:
            cursor = conn.cursor()
            
            if research_type == "all":
                cursor.execute("""
                    SELECT * FROM anonymized_insights
                    WHERE privacy_level IN ('research', 'public')
                    ORDER BY created_at DESC
                """)
            else:
                cursor.execute("""
                    SELECT * FROM anonymized_insights
                    WHERE insight_type = ? AND privacy_level IN ('research', 'public')
                    ORDER BY created_at DESC
                """, (research_type,))
            
            # Convert to AnonymizedInsight objects
            for row in cursor.fetchall():
                insight = AnonymizedInsight(
                    insight_id=row[0],
                    insight_type=row[1],
                    population_size=row[2],
                    time_period=row[3],
                    emotional_patterns=json.loads(row[4]) if row[4] else {},
                    risk_distributions=json.loads(row[5]) if row[5] else {},
                    intervention_effectiveness=json.loads(row[6]) if row[6] else {},
                    privacy_level=DataPrivacyLevel(row[7]),
                    k_anonymity_level=row[8]
                )
                insights.append(insight)
        
        return insights
