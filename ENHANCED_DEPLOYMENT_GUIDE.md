# 🚀 Enhanced AI Companion System - Deployment Guide

## Overview

This guide covers the deployment of the Enhanced AI Companion System v2.0, featuring state-of-the-art conversational AI with advanced emotional intelligence, neural-symbolic memory, and production-grade WhatsApp integration.

## 🏗️ System Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    🌐 Interface Layer                           │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   Gradio UI     │   FastAPI       │   WhatsApp Bot              │
│   (Development) │   (Production)  │   (Messaging)               │
└─────────────────┴─────────────────┴─────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────────┐
│              🧠 Ultra-Performance System                        │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   Multi-Level   │   Concurrent    │   Predictive                │
│   Caching       │   Processing    │   Pre-loading               │
└─────────────────┴─────────────────┴─────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────────┐
│                   🔬 AI Intelligence Layer                      │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   Enhanced      │   Neural-       │   Gemini API                │
│   Emotional AI  │   Symbolic      │   Integration               │
│                 │   Memory        │                             │
└─────────────────┴─────────────────┴─────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────────┐
│                🏥 Mental Health Data Platform                   │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   Privacy-First │   Crisis        │   Professional              │
│   Analytics     │   Detection     │   Dashboard                 │
└─────────────────┴─────────────────┴─────────────────────────────┘
```

## 📋 Prerequisites

### System Requirements
- **Python**: 3.9 or higher
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: 10GB free space
- **Network**: Stable internet connection

### Required Services
- **Redis**: For caching and session management
- **SQLite/PostgreSQL**: For data persistence
- **Google Gemini API**: For AI processing

### Optional Services
- **WhatsApp Business API**: For messaging integration
- **Professional Mental Health Dashboard**: For clinical integration

## 🔧 Installation

### 1. Clone and Setup

```bash
# Clone the repository
git clone <repository-url>
cd enhanced-ai-companion

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Environment Configuration

Create a `.env` file with your configuration:

```env
# Required: Google Gemini API Key
GEMINI_API_KEY=your_gemini_api_key_here

# Database Configuration
REDIS_URL=redis://localhost:6379
DATABASE_URL=sqlite:///./enhanced_ai_companion.db

# Memory Configuration
MEMORY_TTL=2592000
PERSONAL_MEMORY_SIZE=1000
UNIVERSAL_MEMORY_SIZE=10000

# Performance Configuration
MAX_CONTEXT_LENGTH=2000
RESPONSE_TEMPERATURE=0.7
MAX_TOKENS=150

# System Configuration
LOG_LEVEL=INFO
ENVIRONMENT=production
DEBUG_MODE=false

# Security Configuration
SECRET_KEY=your-secure-secret-key-change-in-production
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
CORS_ORIGINS=https://your-domain.com

# Service Ports
GRADIO_PORT=7860
API_PORT=8000

# Optional: WhatsApp Configuration
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id

# Optional: Professional Integration
ENABLE_MENTAL_HEALTH_PLATFORM=true
MIN_COHORT_SIZE=10
ANONYMIZATION_ENABLED=true
```

### 3. Database Setup

```bash
# Initialize database
python -c "from enhanced_mental_health_platform import EnhancedMentalHealthPlatform; platform = EnhancedMentalHealthPlatform(); print('Database initialized')"
```

### 4. Redis Setup

Install and start Redis:

```bash
# Ubuntu/Debian
sudo apt-get install redis-server
sudo systemctl start redis-server

# macOS
brew install redis
brew services start redis

# Windows
# Download and install Redis from https://redis.io/download
```

## 🚀 Deployment Options

### Option 1: Local Development

```bash
# Run the enhanced system
python enhanced_main.py
```

Access the interface at `http://localhost:7860`

### Option 2: Production Deployment

#### Using Docker

Create `Dockerfile`:

```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Expose ports
EXPOSE 7860 8000

# Run the application
CMD ["python", "enhanced_main.py"]
```

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  ai-companion:
    build: .
    ports:
      - "7860:7860"
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    volumes:
      - ./data:/app/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  redis_data:
```

Deploy with Docker:

```bash
# Build and run
docker-compose up -d

# View logs
docker-compose logs -f ai-companion
```

#### Using Render.com (Free Tier)

1. **Create `render.yaml`**:

```yaml
services:
  - type: web
    name: enhanced-ai-companion
    env: python
    buildCommand: pip install -r requirements.txt
    startCommand: python enhanced_main.py
    envVars:
      - key: GEMINI_API_KEY
        sync: false
      - key: ENVIRONMENT
        value: production
      - key: REDIS_URL
        fromService:
          type: redis
          name: ai-companion-redis
          property: connectionString

  - type: redis
    name: ai-companion-redis
    ipAllowList: []
```

2. **Deploy to Render**:
   - Connect your GitHub repository
   - Set environment variables in Render dashboard
   - Deploy automatically on push

#### Using Railway (Free Tier)

1. **Create `railway.toml`**:

```toml
[build]
builder = "nixpacks"

[deploy]
startCommand = "python enhanced_main.py"

[[services]]
name = "redis"
source = "redis:7"
```

2. **Deploy**:
   - Connect GitHub repository
   - Add Redis service
   - Set environment variables
   - Deploy

### Option 3: WhatsApp Bot Deployment

#### Setup WhatsApp Business API

1. **Create Facebook App**:
   - Go to Facebook Developers
   - Create new app with WhatsApp Business API
   - Get access token and phone number ID

2. **Configure Webhook**:
   - Set webhook URL: `https://your-domain.com/webhook`
   - Set verify token
   - Subscribe to message events

3. **Deploy WhatsApp Bot**:

```bash
# Run WhatsApp bot separately
python production_whatsapp_bot.py
```

Or integrate with main system by setting WhatsApp environment variables.

## 🔧 Configuration

### Performance Tuning

```env
# Ultra-Performance Settings
CACHE_SIZE=1000
BATCH_SIZE=5
THREAD_POOL_SIZE=4
MEMORY_THRESHOLD_MB=500
TARGET_RESPONSE_TIME=0.3
TARGET_CACHE_HIT_RATE=0.8
```

### Memory Architecture Settings

```env
# Memory Consolidation
EMOTIONAL_WEIGHT=0.35
RECENCY_WEIGHT=0.25
FREQUENCY_WEIGHT=0.20
IMPORTANCE_WEIGHT=0.20
FORGETTING_RATE=0.693
REHEARSAL_BOOST=1.5
```

### Emotional Intelligence Settings

```env
# Emotional AI
EMOTION_WEIGHT=0.3
LEARNING_RATE=0.1
ADAPTATION_THRESHOLD=5
CRISIS_THRESHOLD=0.7
MODERATE_RISK_THRESHOLD=0.4
```

## 📊 Monitoring and Maintenance

### Health Checks

```bash
# Check system health
curl http://localhost:8000/health

# Get performance metrics
curl http://localhost:8000/metrics

# Get research insights
curl http://localhost:8000/research/insights
```

### Log Monitoring

```bash
# View application logs
tail -f enhanced_ai_companion.log

# View WhatsApp bot logs
tail -f whatsapp_bot.log
```

### Performance Monitoring

The system includes built-in performance monitoring:

- **Response Time**: Target < 300ms
- **Cache Hit Rate**: Target > 80%
- **Memory Usage**: Monitored and optimized
- **Error Rate**: Tracked and alerted

### Database Maintenance

```bash
# Backup database
cp enhanced_ai_companion.db backup_$(date +%Y%m%d).db

# Clean old data (automated, but can be run manually)
python -c "
from enhanced_mental_health_platform import EnhancedMentalHealthPlatform
platform = EnhancedMentalHealthPlatform()
import asyncio
asyncio.run(platform._cleanup_old_data())
"
```

## 🔒 Security and Privacy

### Data Protection

- **Encryption**: All sensitive data encrypted at rest and in transit
- **Anonymization**: User data anonymized for research
- **GDPR Compliance**: Full compliance with privacy regulations
- **Access Control**: Role-based access to sensitive features

### Security Best Practices

1. **Environment Variables**: Never commit secrets to version control
2. **HTTPS**: Always use HTTPS in production
3. **Rate Limiting**: Implement rate limiting for API endpoints
4. **Input Validation**: All inputs validated and sanitized
5. **Regular Updates**: Keep dependencies updated

## 🧪 Testing

### Run Comprehensive Tests

```bash
# Run all tests
python comprehensive_test_suite.py

# Run specific test categories
python -m pytest comprehensive_test_suite.py::TestEnhancedMemoryArchitecture
python -m pytest comprehensive_test_suite.py::TestNextGenEmotionalIntelligence
python -m pytest comprehensive_test_suite.py::TestPerformanceSystem
```

### Performance Benchmarks

```bash
# Run performance benchmarks
python -c "
import asyncio
from comprehensive_test_suite import TestPerformanceBenchmarks
test = TestPerformanceBenchmarks()
asyncio.run(test.test_response_time_benchmark())
"
```

## 🆘 Troubleshooting

### Common Issues

1. **Redis Connection Error**:
   ```bash
   # Check Redis status
   redis-cli ping
   # Should return PONG
   ```

2. **Gemini API Error**:
   - Verify API key is correct
   - Check API quota and billing
   - Ensure network connectivity

3. **Memory Issues**:
   - Monitor memory usage
   - Adjust `MEMORY_THRESHOLD_MB`
   - Enable garbage collection optimization

4. **Slow Response Times**:
   - Check cache hit rates
   - Monitor system resources
   - Optimize database queries

### Debug Mode

Enable debug mode for detailed logging:

```env
DEBUG_MODE=true
LOG_LEVEL=DEBUG
```

## 📈 Scaling

### Horizontal Scaling

1. **Load Balancer**: Use nginx or cloud load balancer
2. **Multiple Instances**: Run multiple app instances
3. **Redis Cluster**: Scale Redis for high availability
4. **Database Scaling**: Use PostgreSQL with read replicas

### Vertical Scaling

1. **Increase Memory**: More RAM for better caching
2. **CPU Optimization**: More cores for concurrent processing
3. **SSD Storage**: Faster disk I/O

## 🔄 Updates and Maintenance

### Regular Maintenance

1. **Weekly**: Review logs and performance metrics
2. **Monthly**: Update dependencies and security patches
3. **Quarterly**: Performance optimization and feature updates

### Backup Strategy

1. **Database**: Daily automated backups
2. **Configuration**: Version control all config files
3. **Logs**: Rotate and archive logs regularly

## 📞 Support

For support and questions:

- **Documentation**: Check comprehensive guides
- **Issues**: Report bugs and feature requests
- **Community**: Join discussions and get help
- **Professional Support**: Contact for enterprise solutions

---

**The Enhanced AI Companion System represents the cutting edge of conversational AI for mental health support, combining advanced technology with ethical data practices and human-centered design.**

🚀 **Ready to deploy your next-generation AI companion? Follow this guide and transform emotional AI support!**
