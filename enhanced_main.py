"""
Enhanced Main Application for Next-Generation AI Companion System.
Integrates all enhanced components for production deployment.

Key Features:
- Ultra-performance conversation processing
- Advanced emotional intelligence
- Enhanced memory architecture
- Production WhatsApp bot
- Mental health data platform
- Comprehensive monitoring
"""

import asyncio
import logging
import time
import sys
import signal
from datetime import datetime, timezone
from typing import Dict, Any, Optional
import gradio as gr
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware

# Import enhanced components
from ultra_performance_system import UltraPerformanceSystem
from production_whatsapp_bot import ProductionWhatsAppBot, create_production_whatsapp_bot
from enhanced_mental_health_platform import EnhancedMentalHealthPlatform
from config import settings, validate_settings

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_ai_companion.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedAICompanionSystem:
    """
    Enhanced AI Companion System with state-of-the-art features.
    
    Integrates:
    - Ultra-performance conversation processing
    - Advanced emotional intelligence
    - Enhanced memory architecture
    - Production WhatsApp bot
    - Mental health data platform
    """
    
    def __init__(self):
        """Initialize the enhanced AI companion system."""
        self.startup_time = time.time()
        self.system_ready = False
        
        # Core systems
        self.performance_system: Optional[UltraPerformanceSystem] = None
        self.whatsapp_bot: Optional[ProductionWhatsAppBot] = None
        self.mental_health_platform: Optional[EnhancedMentalHealthPlatform] = None
        
        # Web interfaces
        self.gradio_interface: Optional[gr.Interface] = None
        self.fastapi_app: Optional[FastAPI] = None
        
        # System metrics
        self.metrics = {
            'total_conversations': 0,
            'crisis_interventions': 0,
            'average_response_time': 0.0,
            'system_uptime': 0.0,
            'active_users': 0
        }
        
        logger.info("Enhanced AI Companion System initialized")
    
    async def initialize(self):
        """Initialize all system components."""
        try:
            logger.info("🚀 Initializing Enhanced AI Companion System...")
            
            # Validate configuration
            validate_settings()
            logger.info("✅ Configuration validated")
            
            # Initialize core performance system
            logger.info("🧠 Initializing ultra-performance system...")
            self.performance_system = UltraPerformanceSystem()
            
            # Initialize mental health platform
            logger.info("🏥 Initializing mental health data platform...")
            self.mental_health_platform = EnhancedMentalHealthPlatform()
            
            # Initialize WhatsApp bot if configured
            if self._whatsapp_configured():
                logger.info("📱 Initializing production WhatsApp bot...")
                self.whatsapp_bot = create_production_whatsapp_bot()
                self.whatsapp_bot.start_processing()
            else:
                logger.info("📱 WhatsApp bot not configured (optional)")
            
            # Create web interfaces
            self._create_gradio_interface()
            self._create_fastapi_app()
            
            # Start monitoring
            asyncio.create_task(self._system_monitor())
            
            self.startup_time = time.time() - self.startup_time
            self.system_ready = True
            
            logger.info(f"🎉 Enhanced AI Companion System ready in {self.startup_time:.2f} seconds!")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize system: {e}")
            raise
    
    def _whatsapp_configured(self) -> bool:
        """Check if WhatsApp configuration is available."""
        import os
        required_vars = [
            'WHATSAPP_WEBHOOK_VERIFY_TOKEN',
            'WHATSAPP_ACCESS_TOKEN',
            'WHATSAPP_PHONE_NUMBER_ID'
        ]
        return all(os.getenv(var) for var in required_vars)
    
    def _create_gradio_interface(self):
        """Create Gradio interface for development and testing."""
        
        async def chat_interface(message: str, history: list) -> tuple:
            """Chat interface for Gradio."""
            if not self.system_ready:
                return "System is still initializing. Please wait...", history
            
            try:
                # Generate user ID for demo
                user_id = "gradio_user_demo"
                
                # Process conversation
                response, metadata = await self.performance_system.process_conversation(
                    user_id=user_id,
                    message=message
                )
                
                # Update metrics
                self.metrics['total_conversations'] += 1
                self.metrics['average_response_time'] = metadata.get('response_time', 0.0)
                
                # Update history
                history.append([message, response])
                
                return "", history
                
            except Exception as e:
                logger.error(f"Error in chat interface: {e}")
                error_response = "I apologize, but I'm experiencing some technical difficulties. Please try again."
                history.append([message, error_response])
                return "", history
        
        # Create Gradio interface
        with gr.Blocks(
            title="Enhanced AI Companion",
            theme=gr.themes.Soft(),
            css="""
            .gradio-container {
                max-width: 800px !important;
                margin: auto !important;
            }
            """
        ) as interface:
            
            gr.Markdown("""
            # 🧠 Enhanced AI Companion System
            
            **Next-Generation Conversational AI for Emotional Support**
            
            Features:
            - 🚀 Ultra-fast response times (< 300ms)
            - 🧠 Advanced emotional intelligence
            - 💾 Neural-symbolic memory architecture
            - 🏥 Mental health support with crisis detection
            - 🔒 Privacy-first design
            """)
            
            with gr.Row():
                with gr.Column(scale=3):
                    chatbot = gr.Chatbot(
                        label="Conversation",
                        height=400,
                        show_label=True
                    )
                    
                    with gr.Row():
                        msg = gr.Textbox(
                            label="Your message",
                            placeholder="Share what's on your mind...",
                            lines=2,
                            scale=4
                        )
                        send_btn = gr.Button("Send", variant="primary", scale=1)
                
                with gr.Column(scale=1):
                    gr.Markdown("### 📊 System Status")
                    
                    status_display = gr.JSON(
                        label="Metrics",
                        value=self.metrics
                    )
                    
                    refresh_btn = gr.Button("Refresh Stats", variant="secondary")
            
            # Event handlers
            send_btn.click(
                fn=chat_interface,
                inputs=[msg, chatbot],
                outputs=[msg, chatbot]
            )
            
            msg.submit(
                fn=chat_interface,
                inputs=[msg, chatbot],
                outputs=[msg, chatbot]
            )
            
            refresh_btn.click(
                fn=lambda: self._get_system_metrics(),
                outputs=[status_display]
            )
            
            gr.Markdown("""
            ---
            
            ### 🆘 Crisis Support
            
            If you're experiencing a mental health crisis:
            - **National Suicide Prevention Lifeline**: 988
            - **Crisis Text Line**: Text HOME to 741741
            - **Emergency Services**: 911
            
            This AI companion is designed to provide support, but it's not a replacement for professional mental health care.
            """)
        
        self.gradio_interface = interface
    
    def _create_fastapi_app(self):
        """Create FastAPI app for production API."""
        app = FastAPI(
            title="Enhanced AI Companion API",
            description="Next-generation conversational AI for emotional support",
            version="2.0.0"
        )
        
        # Add CORS middleware
        app.add_middleware(
            CORSMiddleware,
            allow_origins=settings.get_cors_origins(),
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        @app.get("/health")
        async def health_check():
            """Health check endpoint."""
            return {
                "status": "healthy" if self.system_ready else "initializing",
                "uptime": time.time() - self.startup_time,
                "metrics": self.metrics,
                "components": {
                    "performance_system": self.performance_system is not None,
                    "whatsapp_bot": self.whatsapp_bot is not None,
                    "mental_health_platform": self.mental_health_platform is not None
                }
            }
        
        @app.post("/chat")
        async def chat_endpoint(request: dict):
            """Chat endpoint for API access."""
            if not self.system_ready:
                raise HTTPException(status_code=503, detail="System not ready")
            
            try:
                user_id = request.get("user_id", "api_user")
                message = request.get("message", "")
                context = request.get("context", {})
                
                if not message:
                    raise HTTPException(status_code=400, detail="Message is required")
                
                # Process conversation
                response, metadata = await self.performance_system.process_conversation(
                    user_id=user_id,
                    message=message,
                    context=context
                )
                
                # Update metrics
                self.metrics['total_conversations'] += 1
                
                return {
                    "response": response,
                    "metadata": metadata,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
                
            except Exception as e:
                logger.error(f"Error in chat endpoint: {e}")
                raise HTTPException(status_code=500, detail="Internal server error")
        
        @app.get("/metrics")
        async def get_metrics():
            """Get system metrics."""
            if not self.system_ready:
                raise HTTPException(status_code=503, detail="System not ready")
            
            return {
                "system_metrics": self._get_system_metrics(),
                "performance_stats": self.performance_system.get_performance_statistics() if self.performance_system else {},
                "mental_health_insights": self.mental_health_platform.get_professional_dashboard_data() if self.mental_health_platform else {}
            }
        
        @app.get("/research/insights")
        async def get_research_insights():
            """Get anonymized research insights."""
            if not self.mental_health_platform:
                raise HTTPException(status_code=404, detail="Mental health platform not available")
            
            insights = self.mental_health_platform.get_research_insights()
            return {
                "insights": [
                    {
                        "insight_id": insight.insight_id,
                        "insight_type": insight.insight_type,
                        "population_size": insight.population_size,
                        "time_period": insight.time_period,
                        "emotional_patterns": insight.emotional_patterns,
                        "privacy_level": insight.privacy_level.value,
                        "created_at": insight.created_at.isoformat()
                    }
                    for insight in insights
                ]
            }
        
        self.fastapi_app = app
    
    def _get_system_metrics(self) -> Dict[str, Any]:
        """Get comprehensive system metrics."""
        uptime = time.time() - (time.time() - self.startup_time)
        
        metrics = {
            **self.metrics,
            "system_uptime": uptime,
            "system_ready": self.system_ready,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        # Add performance metrics if available
        if self.performance_system:
            perf_stats = self.performance_system.get_performance_statistics()
            metrics.update({
                "performance": perf_stats.get("current_performance", {}),
                "cache_stats": perf_stats.get("cache_statistics", {})
            })
        
        return metrics
    
    async def _system_monitor(self):
        """Background system monitoring."""
        while True:
            try:
                # Update system metrics
                self.metrics['system_uptime'] = time.time() - (time.time() - self.startup_time)
                
                # Log system status periodically
                if int(time.time()) % 300 == 0:  # Every 5 minutes
                    logger.info(f"System status: {self.metrics['total_conversations']} conversations, "
                              f"{self.metrics['average_response_time']:.3f}s avg response time")
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"Error in system monitor: {e}")
                await asyncio.sleep(30)
    
    async def run_gradio(self, share: bool = False, port: int = 7860):
        """Run Gradio interface."""
        if not self.gradio_interface:
            raise RuntimeError("Gradio interface not initialized")
        
        logger.info(f"🌐 Starting Gradio interface on port {port}")
        
        # Launch Gradio
        self.gradio_interface.launch(
            server_name="0.0.0.0",
            server_port=port,
            share=share,
            show_error=True,
            quiet=False
        )
    
    async def run_api(self, host: str = "0.0.0.0", port: int = 8000):
        """Run FastAPI server."""
        if not self.fastapi_app:
            raise RuntimeError("FastAPI app not initialized")
        
        logger.info(f"🚀 Starting FastAPI server on {host}:{port}")
        
        config = uvicorn.Config(
            app=self.fastapi_app,
            host=host,
            port=port,
            log_level="info"
        )
        
        server = uvicorn.Server(config)
        await server.serve()
    
    async def shutdown(self):
        """Graceful shutdown of all components."""
        logger.info("🛑 Shutting down Enhanced AI Companion System...")
        
        try:
            # Shutdown performance system
            if self.performance_system:
                await self.performance_system.shutdown()
            
            # Shutdown WhatsApp bot
            if self.whatsapp_bot:
                self.whatsapp_bot.stop()
            
            logger.info("✅ System shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

# Global system instance
enhanced_system = EnhancedAICompanionSystem()

# Signal handlers for graceful shutdown
def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger.info(f"Received signal {signum}, initiating graceful shutdown...")
    asyncio.create_task(enhanced_system.shutdown())
    sys.exit(0)

signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

async def main():
    """Main application entry point."""
    try:
        # Initialize system
        await enhanced_system.initialize()
        
        # Run Gradio interface
        await enhanced_system.run_gradio(
            share=False,
            port=settings.gradio_port
        )
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Error in main: {e}")
        raise
    finally:
        await enhanced_system.shutdown()

if __name__ == "__main__":
    # Print startup banner
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🧠 Enhanced AI Companion System v2.0                 ║
    ║                                                              ║
    ║        Next-Generation Conversational AI                    ║
    ║        for Emotional Support & Mental Health                ║
    ║                                                              ║
    ║        Features:                                             ║
    ║        • Ultra-fast response times (< 300ms)                ║
    ║        • Advanced emotional intelligence                    ║
    ║        • Neural-symbolic memory architecture                ║
    ║        • Crisis detection & intervention                    ║
    ║        • Privacy-first mental health analytics              ║
    ║        • Production WhatsApp integration                    ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    # Run the application
    asyncio.run(main())
