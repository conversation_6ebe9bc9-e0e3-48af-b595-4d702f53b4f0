"""
Complete test suite for the enhanced AI companion system.
Tests all major components including ultra-fast performance, enhanced emotional intelligence, 
mental health data platform, and WhatsApp integration.
"""

import asyncio
import time
import logging
import json
from datetime import datetime, timezone
from typing import Dict, List, Any

from ultra_fast_conversation_service import UltraFastConversationService
from enhanced_emotional_intelligence import EnhancedEmotionalIntelligence, AttachmentStyle, PersonalityTrait
from mental_health_data_platform import MentalHealthDataPlatform, DataPrivacyLevel, InsightCategory
from whatsapp_bot_integration import WhatsAppBotService, WhatsAppMessage, UserSession
from models import EmotionType
from gemini_service import OptimizedGeminiService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CompleteEnhancedSystemTester:
    """Complete tester for the enhanced AI companion system."""
    
    def __init__(self):
        # Initialize services
        self.gemini_service = OptimizedGeminiService()
        self.conversation_service = UltraFastConversationService()
        self.emotional_intelligence = EnhancedEmotionalIntelligence(self.gemini_service)
        self.mental_health_platform = MentalHealthDataPlatform()
        
        # Test results storage
        self.test_results: Dict[str, Any] = {}
    
    async def test_ultra_fast_performance(self) -> Dict[str, Any]:
        """Test ultra-fast conversation performance."""
        print("⚡ Testing Ultra-Fast Performance...")
        
        results = {}
        test_user = "performance_test_user"
        
        try:
            # Single message performance
            print("  Testing single message performance...")
            start_time = time.time()
            response, proc_time, metrics = await self.conversation_service.process_message_ultra_fast(
                user_id=test_user,
                message="I'm feeling anxious about my presentation tomorrow.",
                context={"test": "performance"}
            )
            total_time = time.time() - start_time
            
            results['single_message'] = {
                'processing_time': proc_time,
                'total_time': total_time,
                'response_generated': len(response) > 0,
                'cache_hit': metrics.get('cache_hit', False),
                'sub_second': proc_time < 1.0,
                'success': proc_time < 1.0 and len(response) > 0
            }
            print(f"    Single message: {proc_time:.3f}s (sub-second: {proc_time < 1.0})")
            
            # Concurrent message performance
            print("  Testing concurrent message performance...")
            concurrent_users = 5
            messages_per_user = 3
            
            async def simulate_user(user_id):
                times = []
                for i in range(messages_per_user):
                    start = time.time()
                    response, proc_time, metrics = await self.conversation_service.process_message_ultra_fast(
                        user_id=f"concurrent_{user_id}",
                        message=f"Test message {i} - feeling stressed about work",
                        context={"concurrent_test": True}
                    )
                    times.append(proc_time)
                return times
            
            concurrent_start = time.time()
            tasks = [simulate_user(i) for i in range(concurrent_users)]
            all_times = await asyncio.gather(*tasks)
            concurrent_total = time.time() - concurrent_start
            
            flat_times = [t for user_times in all_times for t in user_times]
            avg_time = sum(flat_times) / len(flat_times)
            max_time = max(flat_times)
            
            results['concurrent_performance'] = {
                'concurrent_users': concurrent_users,
                'total_messages': len(flat_times),
                'avg_response_time': avg_time,
                'max_response_time': max_time,
                'messages_per_second': len(flat_times) / concurrent_total,
                'all_sub_second': max_time < 1.0,
                'success': avg_time < 0.5 and max_time < 1.0
            }
            print(f"    Concurrent: {len(flat_times)} messages, avg {avg_time:.3f}s, {len(flat_times)/concurrent_total:.1f} msg/s")
            
            # Cache effectiveness test
            print("  Testing cache effectiveness...")
            repeated_message = "How are you feeling today?"
            
            # First request (cache miss)
            response1, time1, metrics1 = await self.conversation_service.process_message_ultra_fast(
                user_id="cache_test_user",
                message=repeated_message,
                context={"cache_test": True}
            )
            
            # Second request (should be cache hit)
            response2, time2, metrics2 = await self.conversation_service.process_message_ultra_fast(
                user_id="cache_test_user", 
                message=repeated_message,
                context={"cache_test": True}
            )
            
            results['cache_effectiveness'] = {
                'first_request_time': time1,
                'second_request_time': time2,
                'cache_hit_detected': metrics2.get('cache_hit', False),
                'speedup_ratio': time1 / time2 if time2 > 0 else 0,
                'responses_consistent': response1 == response2,
                'success': time2 < time1 and metrics2.get('cache_hit', False)
            }
            print(f"    Cache speedup: {time1/time2 if time2 > 0 else 0:.1f}x")
            
        except Exception as e:
            logger.error(f"Error in ultra-fast performance test: {e}")
            results['error'] = str(e)
        
        return results
    
    async def test_enhanced_emotional_intelligence(self) -> Dict[str, Any]:
        """Test enhanced emotional intelligence features."""
        print("🧠 Testing Enhanced Emotional Intelligence...")
        
        results = {}
        test_user = "emotional_test_user"
        
        try:
            # Test attachment style analysis
            conversation_history = [
                {"role": "user", "content": "I always worry about what others think of me"},
                {"role": "user", "content": "I need people to like me, and I get anxious when they don't respond"},
                {"role": "user", "content": "I hate being alone. I'd rather be in a bad relationship than no relationship"}
            ]
            
            print("  Testing attachment style analysis...")
            attachment_style = await self.emotional_intelligence.analyze_attachment_style(
                test_user, conversation_history
            )
            
            results['attachment_analysis'] = {
                'style_detected': attachment_style != AttachmentStyle.UNKNOWN,
                'style': attachment_style.value,
                'success': attachment_style != AttachmentStyle.UNKNOWN
            }
            print(f"    Detected attachment style: {attachment_style.value}")
            
            # Test personality trait analysis
            print("  Testing personality trait analysis...")
            personality_traits = await self.emotional_intelligence.analyze_personality_traits(
                test_user, conversation_history
            )
            
            results['personality_analysis'] = {
                'traits_analyzed': len(personality_traits),
                'traits': {trait.value: score for trait, score in personality_traits.items()},
                'success': len(personality_traits) >= 3  # At least 3 traits analyzed
            }
            print(f"    Analyzed {len(personality_traits)} personality traits")
            
            # Test therapeutic relationship building
            print("  Testing therapeutic relationship building...")
            await self.emotional_intelligence.build_therapeutic_relationship(
                test_user, 
                interaction_quality=0.8,
                user_feedback="This conversation was really helpful"
            )
            
            relationship_status = self.emotional_intelligence.get_therapeutic_relationship_status(test_user)
            
            results['therapeutic_relationship'] = {
                'relationship_established': len(relationship_status) > 1,
                'alliance_strength': relationship_status.get('alliance_strength', 0),
                'trust_level': relationship_status.get('trust_level', 0),
                'success': relationship_status.get('alliance_strength', 0) > 0.5
            }
            print(f"    Therapeutic alliance: {relationship_status.get('alliance_strength', 0):.2f}")
            
        except Exception as e:
            logger.error(f"Error in enhanced emotional intelligence test: {e}")
            results['error'] = str(e)
        
        return results
    
    async def test_mental_health_platform(self) -> Dict[str, Any]:
        """Test mental health data platform."""
        print("📊 Testing Mental Health Data Platform...")
        
        results = {}
        
        try:
            # Generate sample data
            print("  Generating anonymized sample data...")
            sample_users = ["user1", "user2", "user3", "user4", "user5"]
            
            for i, user_id in enumerate(sample_users):
                for j in range(10):
                    await self.mental_health_platform.record_emotional_interaction(
                        user_id=user_id,
                        emotion=EmotionType.ANXIETY if j % 2 == 0 else EmotionType.SADNESS,
                        intensity=0.3 + (j * 0.07),
                        message=f"Sample emotional message {j}",
                        context={
                            "age": 25 + i * 5,
                            "location": "US",
                            "context_type": "work"
                        },
                        intervention_type="conversational_support"
                    )
            
            results['data_generation'] = {
                'users_processed': len(sample_users),
                'interactions_recorded': len(sample_users) * 10,
                'success': True
            }
            print(f"    Generated data for {len(sample_users)} users")
            
            # Test insight generation
            print("  Generating population insights...")
            insights = await self.mental_health_platform.generate_population_insights(
                privacy_level=DataPrivacyLevel.RESEARCH
            )
            
            results['insight_generation'] = {
                'insights_generated': len(insights),
                'categories': list(set([insight.category.value for insight in insights])),
                'success': len(insights) > 0
            }
            print(f"    Generated {len(insights)} population insights")
            
            # Test privacy compliance
            stats = self.mental_health_platform.get_anonymized_statistics()
            
            results['privacy_compliance'] = {
                'privacy_compliant': stats['privacy_compliant'],
                'data_retention_configured': stats['data_retention_days'] > 0,
                'anonymization_active': stats['total_users'] > 0,
                'success': stats['privacy_compliant']
            }
            print(f"    Privacy compliance: {stats['privacy_compliant']}")
            
        except Exception as e:
            logger.error(f"Error in mental health platform test: {e}")
            results['error'] = str(e)
        
        return results
    
    async def test_whatsapp_integration(self) -> Dict[str, Any]:
        """Test WhatsApp bot integration."""
        print("📱 Testing WhatsApp Integration...")
        
        results = {}
        
        try:
            # Create mock WhatsApp bot
            bot = WhatsAppBotService(
                webhook_verify_token="test_token",
                access_token="test_access_token",
                phone_number_id="test_phone_id"
            )
            
            # Test crisis detection
            print("  Testing crisis message detection...")
            crisis_messages = [
                "I want to kill myself",
                "I can't go on anymore",
                "There's no point in living"
            ]
            
            crisis_detected = sum(1 for msg in crisis_messages if bot._is_crisis_message(msg))
            
            results['crisis_detection'] = {
                'messages_tested': len(crisis_messages),
                'crisis_detected': crisis_detected,
                'detection_accuracy': crisis_detected / len(crisis_messages),
                'success': crisis_detected == len(crisis_messages)
            }
            print(f"    Crisis detection: {crisis_detected}/{len(crisis_messages)} messages")
            
            # Test user session management
            print("  Testing user session management...")
            test_phone = "+1234567890"
            session = bot._get_or_create_user_session(test_phone)
            
            results['session_management'] = {
                'session_created': session is not None,
                'user_id_format_correct': session.user_id.startswith('whatsapp_'),
                'tracking_enabled': session.message_count > 0,
                'success': session is not None
            }
            print(f"    Session created for {test_phone}")
            
            # Test command handling
            print("  Testing command handling...")
            commands = ['help', 'privacy', 'crisis', 'consent']
            handled_commands = 0
            
            for command in commands:
                response = await bot._handle_command(session, command)
                if response and len(response) > 0:
                    handled_commands += 1
            
            results['command_handling'] = {
                'commands_tested': len(commands),
                'commands_handled': handled_commands,
                'handling_rate': handled_commands / len(commands),
                'success': handled_commands == len(commands)
            }
            print(f"    Commands handled: {handled_commands}/{len(commands)}")
            
        except Exception as e:
            logger.error(f"Error in WhatsApp integration test: {e}")
            results['error'] = str(e)
        
        return results
    
    async def test_system_integration(self) -> Dict[str, Any]:
        """Test complete system integration."""
        print("🔗 Testing System Integration...")
        
        results = {}
        
        try:
            # Test end-to-end conversation flow
            print("  Testing end-to-end conversation flow...")
            test_user = "integration_test_user"
            
            # Simulate a complete conversation
            messages = [
                "Hi, I'm feeling really overwhelmed with work",
                "I can't seem to manage my anxiety",
                "Do you have any advice for dealing with stress?"
            ]
            
            conversation_times = []
            responses = []
            
            for i, message in enumerate(messages):
                start_time = time.time()
                response, proc_time, metrics = await self.conversation_service.process_message_ultra_fast(
                    user_id=test_user,
                    message=message,
                    context={"conversation_turn": i + 1}
                )
                conversation_times.append(proc_time)
                responses.append(response)
            
            results['conversation_flow'] = {
                'messages_processed': len(messages),
                'responses_generated': len([r for r in responses if len(r) > 0]),
                'avg_response_time': sum(conversation_times) / len(conversation_times),
                'max_response_time': max(conversation_times),
                'all_responses_valid': all(len(r) > 0 for r in responses),
                'success': all(len(r) > 0 for r in responses) and max(conversation_times) < 1.0
            }
            print(f"    Conversation flow: {len(responses)} responses, avg {sum(conversation_times)/len(conversation_times):.3f}s")
            
            # Test data consistency across components
            print("  Testing data consistency...")
            
            # Check if data flows between components
            platform_stats = self.mental_health_platform.get_anonymized_statistics()
            relationship_status = self.emotional_intelligence.get_therapeutic_relationship_status(test_user)
            performance_metrics = self.conversation_service.get_performance_metrics()
            
            results['data_consistency'] = {
                'platform_has_data': platform_stats['total_interactions'] > 0,
                'relationships_tracked': len(relationship_status) > 1,
                'performance_tracked': performance_metrics['total_requests'] > 0,
                'components_connected': True,
                'success': True
            }
            print(f"    Data consistency verified across all components")
            
        except Exception as e:
            logger.error(f"Error in system integration test: {e}")
            results['error'] = str(e)
        
        return results
    
    def print_comprehensive_results(self, all_results: Dict[str, Any]):
        """Print comprehensive test results."""
        print("\n" + "="*100)
        print("🎯 ENHANCED AI COMPANION SYSTEM - COMPREHENSIVE TEST RESULTS")
        print("="*100)
        
        total_tests = 0
        passed_tests = 0
        
        for test_category, results in all_results.items():
            print(f"\n📋 {test_category.upper().replace('_', ' ')}:")
            
            if 'error' in results:
                print(f"  ❌ ERROR: {results['error']}")
                continue
            
            for test_name, test_data in results.items():
                if isinstance(test_data, dict) and 'success' in test_data:
                    total_tests += 1
                    if test_data['success']:
                        passed_tests += 1
                        status = "✅ PASS"
                    else:
                        status = "❌ FAIL"
                    
                    # Show key metrics
                    key_metrics = []
                    if 'processing_time' in test_data:
                        key_metrics.append(f"time: {test_data['processing_time']:.3f}s")
                    if 'avg_response_time' in test_data:
                        key_metrics.append(f"avg: {test_data['avg_response_time']:.3f}s")
                    if 'messages_per_second' in test_data:
                        key_metrics.append(f"rate: {test_data['messages_per_second']:.1f} msg/s")
                    
                    metrics_str = f" ({', '.join(key_metrics)})" if key_metrics else ""
                    print(f"  {status} {test_name}{metrics_str}")
        
        print(f"\n🏆 OVERALL RESULTS:")
        print(f"    Tests Passed: {passed_tests}/{total_tests}")
        print(f"    Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests == total_tests:
            print(f"    🎉 ALL TESTS PASSED! Enhanced system is ready for production.")
        else:
            print(f"    ⚠️  Some tests failed. Review results above.")
        
        print("="*100)

async def main():
    """Run complete enhanced system tests."""
    print("🚀 Starting Complete Enhanced AI Companion System Tests...\n")
    
    tester = CompleteEnhancedSystemTester()
    all_results = {}
    
    try:
        # Run all test suites
        all_results['ultra_fast_performance'] = await tester.test_ultra_fast_performance()
        all_results['enhanced_emotional_intelligence'] = await tester.test_enhanced_emotional_intelligence()
        all_results['mental_health_platform'] = await tester.test_mental_health_platform()
        all_results['whatsapp_integration'] = await tester.test_whatsapp_integration()
        all_results['system_integration'] = await tester.test_system_integration()
        
        # Print comprehensive results
        tester.print_comprehensive_results(all_results)
        
        return all_results
        
    except Exception as e:
        logger.error(f"Test suite failed: {e}")
        return None
    finally:
        # Cleanup
        await tester.conversation_service.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
