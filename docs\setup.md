# Setup Guide

This guide will help you set up the AI Companion System for development or production use.

## Prerequisites

- Python 3.9 or higher
- Git
- Google Gemini API key
- Redis (optional, for caching)

## Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd ai-companion-system
```

### 2. Create Virtual Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### 3. Install Dependencies

```bash
# Install production dependencies
pip install -r requirements.txt

# For development (includes testing and code quality tools)
pip install -r requirements-dev.txt
```

## Configuration

### 1. Environment Variables

Copy the environment template:

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

```env
# Required: Google Gemini API Key
GEMINI_API_KEY=your_gemini_api_key_here

# Database Configuration
DATABASE_URL=sqlite:///./data/db/ai_companion.db
REDIS_URL=redis://localhost:6379

# System Configuration
ENVIRONMENT=development
LOG_LEVEL=INFO
DEBUG_MODE=true

# Security (IMPORTANT: Change in production!)
SECRET_KEY=your-secret-key-minimum-32-characters
```

### 2. Get Google Gemini API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key to your `.env` file

### 3. Optional: Redis Setup

For improved performance, install and run Redis:

```bash
# On macOS with Homebrew
brew install redis
brew services start redis

# On Ubuntu/Debian
sudo apt-get install redis-server
sudo systemctl start redis-server

# On Windows
# Download and install from https://redis.io/download
```

## Database Setup

The system will automatically create the SQLite database on first run. For production, you may want to use PostgreSQL:

```env
DATABASE_URL=postgresql://username:password@localhost/ai_companion
```

## Running the System

### Development Mode

```bash
# Start the AI Companion System
python -m src.ai_companion.main
```

The system will be available at:
- Web Interface: http://localhost:7860
- API Documentation: http://localhost:8000/docs
- Health Check: http://localhost:8000/health

### Production Mode

```bash
# Set production environment
export ENVIRONMENT=production
export DEBUG_MODE=false

# Run with production settings
python -m src.ai_companion.main
```

## Verification

### 1. Check System Health

```bash
curl http://localhost:8000/health
```

Should return:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "version": "1.0.0"
}
```

### 2. Test the API

```bash
curl -X POST http://localhost:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, how are you?", "user_id": "test_user"}'
```

### 3. Access Web Interface

Open http://localhost:7860 in your browser and try having a conversation.

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure you're in the virtual environment and all dependencies are installed
2. **API Key Errors**: Verify your Gemini API key is correct and has proper permissions
3. **Port Conflicts**: Change ports in `.env` if 7860 or 8000 are already in use
4. **Database Errors**: Ensure the `data/db/` directory exists and is writable

### Getting Help

- Check the logs in `data/logs/ai_companion.log`
- Review the [troubleshooting section](troubleshooting.md)
- Open an issue on GitHub if you need help

## Next Steps

- Read the [API Documentation](api.md)
- Learn about [Deployment Options](deployment.md)
- Explore the [Architecture Guide](architecture.md)
