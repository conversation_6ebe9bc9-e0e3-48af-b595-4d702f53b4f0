"""
Launch the optimized AI Companion demo with mock services for testing.
This allows you to experience the optimized interface without external dependencies.
"""

import gradio as gr
import asyncio
import time
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

# Import optimized models
from models import (
    MemoryEntry, MemoryType, InteractionType, EmotionType,
    UserProfile, EmotionalState, EmpathyModel
)
from config import settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockOptimizedServices:
    """Mock services for demo purposes."""
    
    def __init__(self):
        """Initialize mock services."""
        self.conversation_history = {}
        self.user_profiles = {}
        self.memory_store = {}
        self.response_cache = {}
        
        # Performance tracking
        self.total_interactions = 0
        self.response_times = []
        self.cache_hits = 0
        self.cache_misses = 0
    
    def detect_emotion_fast(self, message: str) -> EmotionalState:
        """Fast emotion detection."""
        emotion_keywords = {
            EmotionType.JOY: ["happy", "excited", "great", "wonderful", "amazing", "love", "celebrate"],
            EmotionType.SADNESS: ["sad", "depressed", "down", "upset", "crying", "hurt"],
            EmotionType.ANGER: ["angry", "mad", "furious", "annoyed", "frustrated"],
            EmotionType.ANXIETY: ["anxious", "worried", "nervous", "stressed", "panic"],
            EmotionType.FEAR: ["scared", "afraid", "terrified", "frightened"],
        }
        
        message_lower = message.lower()
        detected_emotions = {}
        
        for emotion, keywords in emotion_keywords.items():
            score = sum(1 for keyword in keywords if keyword in message_lower)
            if score > 0:
                detected_emotions[emotion] = score / len(keywords)
        
        if detected_emotions:
            primary_emotion = max(detected_emotions, key=detected_emotions.get)
            confidence = detected_emotions[primary_emotion]
        else:
            primary_emotion = EmotionType.NEUTRAL
            confidence = 0.5
        
        return EmotionalState(
            primary_emotion=primary_emotion,
            confidence=confidence,
            intensity=min(confidence * 1.5, 1.0),
            valence=0.5 if primary_emotion == EmotionType.JOY else -0.3 if primary_emotion in [EmotionType.SADNESS, EmotionType.ANGER] else 0.0,
            arousal=confidence if primary_emotion in [EmotionType.ANGER, EmotionType.ANXIETY] else 0.3
        )
    
    def generate_response(self, message: str, emotional_state: EmotionalState, context: str = "") -> str:
        """Generate contextual response."""
        message_lower = message.lower()

        # Generate cache key
        cache_key = f"{emotional_state.primary_emotion.value}_{hash(message_lower) % 1000}"

        # Check cache first
        if cache_key in self.response_cache:
            self.cache_hits += 1
            return self.response_cache[cache_key]

        self.cache_misses += 1
        
        # Generate response based on emotion and content
        if emotional_state.primary_emotion == EmotionType.ANXIETY:
            if "interview" in message_lower:
                response = "I understand job interviews can feel overwhelming. Remember, they invited you because they're interested in you! Take deep breaths and focus on your strengths."
            elif "stressed" in message_lower:
                response = "It sounds like you're carrying a lot right now. Stress is your body's way of saying you care. What's one small thing that might help you feel more grounded?"
            else:
                response = "I can hear the worry in your message. Anxiety is tough, but you're not alone. What's been on your mind lately?"
        
        elif emotional_state.primary_emotion == EmotionType.JOY:
            if "promoted" in message_lower or "job" in message_lower:
                response = "Congratulations! That's fantastic news! 🎉 You must have worked really hard for this. How are you planning to celebrate?"
            elif "excited" in message_lower:
                response = "Your excitement is contagious! I love hearing such positive energy. What's got you feeling so amazing?"
            else:
                response = "It's wonderful to hear such happiness in your message! Your joy really brightens my day. Tell me more about what's making you feel so good!"
        
        elif emotional_state.primary_emotion == EmotionType.SADNESS:
            if "difficult" in message_lower:
                response = "I can hear that things feel really hard right now. It's okay to feel this way - difficult times are part of being human. I'm here to listen."
            else:
                response = "I'm sorry you're going through a tough time. Your feelings are valid, and it's brave of you to share them. What would feel most helpful right now?"
        
        elif emotional_state.primary_emotion == EmotionType.ANGER:
            response = "It sounds like you're really frustrated, and that's completely understandable. Sometimes things just don't go the way we hope. Want to tell me more about what's bothering you?"
        
        elif "hello" in message_lower or "hi" in message_lower:
            response = "Hello! It's great to connect with you today. I'm here to chat, listen, and support you however I can. How are you feeling?"
        
        elif "thank" in message_lower:
            response = "You're so welcome! I'm really glad I could help. It means a lot to know that our conversation was useful for you. How are you feeling now?"
        
        else:
            response = "I'm here to listen and chat with you. What's on your mind today? I'd love to hear what you're thinking about."
        
        # Cache the response
        self.response_cache[cache_key] = response
        return response
    
    async def process_message_fast(self, conversation_id: str, user_message: str, user_id: str) -> Dict[str, Any]:
        """Process message with optimized flow."""
        start_time = time.time()
        
        try:
            # Initialize conversation history if needed
            if conversation_id not in self.conversation_history:
                self.conversation_history[conversation_id] = []
            
            # Detect emotion
            emotional_state = self.detect_emotion_fast(user_message)
            
            # Build context
            history = self.conversation_history[conversation_id]
            recent_context = []
            for msg in history[-3:]:  # Last 3 messages
                recent_context.append(f"{msg['role']}: {msg['content']}")
            context_str = "\n".join(recent_context)
            
            # Generate response
            response = self.generate_response(user_message, emotional_state, context_str)
            
            # Update history
            history.append({
                "role": "user",
                "content": user_message,
                "emotion": emotional_state.primary_emotion.value,
                "timestamp": time.time()
            })
            
            history.append({
                "role": "assistant",
                "content": response,
                "timestamp": time.time()
            })
            
            # Keep history manageable
            if len(history) > 20:
                self.conversation_history[conversation_id] = history[-20:]
            
            # Update metrics
            processing_time = time.time() - start_time
            self.total_interactions += 1
            self.response_times.append(processing_time)
            
            # Keep only last 100 response times
            if len(self.response_times) > 100:
                self.response_times = self.response_times[-100:]
            
            # Check if response was cached
            was_cached = response in self.response_cache.values()

            return {
                "response": response,
                "emotion": emotional_state.primary_emotion.value,
                "confidence": emotional_state.confidence,
                "intensity": emotional_state.intensity,
                "valence": emotional_state.valence,
                "processing_time": processing_time,
                "cached": was_cached
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Error processing message: {e}")
            return {
                "error": str(e),
                "response": "I'm experiencing some technical difficulties. Please try again.",
                "processing_time": processing_time
            }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        avg_response_time = sum(self.response_times) / len(self.response_times) if self.response_times else 0
        cache_hit_rate = self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0
        
        return {
            "total_interactions": self.total_interactions,
            "avg_response_time_ms": round(avg_response_time * 1000, 2),
            "cache_hit_rate": round(cache_hit_rate, 3),
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "active_conversations": len(self.conversation_history),
            "total_cached_responses": len(self.response_cache)
        }

class OptimizedDemoInterface:
    """Optimized demo interface."""
    
    def __init__(self):
        """Initialize the demo interface."""
        self.services = MockOptimizedServices()
        self.session_counter = 0
        self.active_sessions = {}
    
    def create_interface(self) -> gr.Blocks:
        """Create the optimized demo interface."""
        
        with gr.Blocks(
            title="AI Companion - Optimized Demo",
            theme=gr.themes.Soft(),
            css="""
            .performance-stats {
                background: linear-gradient(45deg, #f0f8ff, #e6f3ff);
                padding: 15px;
                border-radius: 10px;
                margin: 10px 0;
                border: 1px solid #ddd;
            }
            .fast-response {
                animation: fadeIn 0.3s ease-in;
            }
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }
            .emotion-indicator {
                padding: 8px 12px;
                border-radius: 20px;
                font-weight: bold;
                text-align: center;
                margin: 5px 0;
            }
            """
        ) as interface:
            
            gr.Markdown("""
            # 🚀 AI Companion - Ultra-Fast Demo
            
            Experience lightning-fast conversations with advanced emotional intelligence!
            
            **Features Demonstrated:**
            - ⚡ Sub-second response times
            - 🧠 Real-time emotion detection  
            - 💾 Intelligent response caching
            - 📊 Live performance monitoring
            """)
            
            with gr.Row():
                with gr.Column(scale=3):
                    # Main chat interface
                    chatbot = gr.Chatbot(
                        label="💬 AI Companion Chat",
                        height=400,
                        show_label=True,
                        elem_classes=["fast-response"],
                        type="messages"
                    )
                    
                    with gr.Row():
                        msg_input = gr.Textbox(
                            placeholder="Type your message here... Try: 'I'm feeling anxious' or 'I got promoted!'",
                            label="Your Message",
                            lines=2,
                            max_lines=4,
                            scale=4
                        )
                        send_btn = gr.Button("Send", variant="primary", scale=1)
                    
                    # Quick action buttons
                    with gr.Row():
                        anxiety_btn = gr.Button("😰 I'm anxious", size="sm")
                        happy_btn = gr.Button("😊 Great news!", size="sm")
                        sad_btn = gr.Button("😢 Feeling down", size="sm")
                        chat_btn = gr.Button("💭 Just chat", size="sm")
                
                with gr.Column(scale=1):
                    # Performance dashboard
                    gr.Markdown("### ⚡ Performance Dashboard")
                    
                    performance_display = gr.JSON(
                        label="Real-time Stats",
                        elem_classes=["performance-stats"]
                    )
                    
                    # Emotion indicator
                    emotion_display = gr.Textbox(
                        label="🎭 Current Emotion",
                        interactive=False,
                        placeholder="Neutral",
                        elem_classes=["emotion-indicator"]
                    )
                    
                    # Response time indicator
                    response_time_display = gr.Textbox(
                        label="⏱️ Response Time",
                        interactive=False,
                        placeholder="0ms"
                    )
                    
                    # Cache efficiency
                    cache_display = gr.Textbox(
                        label="💾 Cache Efficiency",
                        interactive=False,
                        placeholder="0%"
                    )
            
            # Hidden state
            session_id = gr.State(value=None)
            user_id = gr.State(value="demo_user")
            
            def start_session():
                """Start a new session."""
                session_id_val = f"session_{int(time.time())}_{self.session_counter}"
                self.session_counter += 1
                self.active_sessions[session_id_val] = f"conv_{session_id_val}"
                return session_id_val
            
            def process_message_wrapper(message: str, history: List, session_id_val: str, user_id_val: str):
                """Process message wrapper."""
                if not session_id_val:
                    session_id_val = start_session()
                
                if not message.strip():
                    return history, "", session_id_val, {}, "Neutral", "0ms", "0%"
                
                # Get conversation ID
                conversation_id = self.active_sessions.get(session_id_val, f"conv_{session_id_val}")
                
                # Process message
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                result = loop.run_until_complete(
                    self.services.process_message_fast(conversation_id, message, user_id_val)
                )
                loop.close()
                
                # Update history (using new Gradio message format)
                history.append({"role": "user", "content": message})
                history.append({"role": "assistant", "content": result["response"]})
                
                # Get performance stats
                stats = self.services.get_performance_stats()
                
                return (
                    history,
                    "",  # Clear input
                    session_id_val,
                    stats,
                    result.get("emotion", "Neutral").title(),
                    f"{round(result.get('processing_time', 0) * 1000, 1)}ms",
                    f"{stats.get('cache_hit_rate', 0):.1%}"
                )
            
            def quick_action(action_type: str, history: List, session_id_val: str, user_id_val: str):
                """Handle quick action buttons."""
                quick_messages = {
                    "anxiety": "I'm feeling really anxious about something important coming up.",
                    "happy": "I just got some amazing news and I'm so excited to share it!",
                    "sad": "I'm feeling pretty down today and could use someone to talk to.",
                    "chat": "Hi there! I'd love to have a friendly conversation."
                }
                
                message = quick_messages.get(action_type, "Hello!")
                return process_message_wrapper(message, history, session_id_val, user_id_val)
            
            # Event handlers
            send_btn.click(
                fn=process_message_wrapper,
                inputs=[msg_input, chatbot, session_id, user_id],
                outputs=[chatbot, msg_input, session_id, performance_display, emotion_display, response_time_display, cache_display]
            )
            
            msg_input.submit(
                fn=process_message_wrapper,
                inputs=[msg_input, chatbot, session_id, user_id],
                outputs=[chatbot, msg_input, session_id, performance_display, emotion_display, response_time_display, cache_display]
            )
            
            # Quick action buttons
            anxiety_btn.click(
                fn=lambda h, s, u: quick_action("anxiety", h, s, u),
                inputs=[chatbot, session_id, user_id],
                outputs=[chatbot, msg_input, session_id, performance_display, emotion_display, response_time_display, cache_display]
            )
            
            happy_btn.click(
                fn=lambda h, s, u: quick_action("happy", h, s, u),
                inputs=[chatbot, session_id, user_id],
                outputs=[chatbot, msg_input, session_id, performance_display, emotion_display, response_time_display, cache_display]
            )
            
            sad_btn.click(
                fn=lambda h, s, u: quick_action("sad", h, s, u),
                inputs=[chatbot, session_id, user_id],
                outputs=[chatbot, msg_input, session_id, performance_display, emotion_display, response_time_display, cache_display]
            )
            
            chat_btn.click(
                fn=lambda h, s, u: quick_action("chat", h, s, u),
                inputs=[chatbot, session_id, user_id],
                outputs=[chatbot, msg_input, session_id, performance_display, emotion_display, response_time_display, cache_display]
            )
            
            # Initialize session on load
            interface.load(
                fn=start_session,
                outputs=[session_id]
            )
        
        return interface
    
    def launch(self):
        """Launch the demo interface."""
        interface = self.create_interface()
        
        print("\n" + "="*60)
        print("🚀 LAUNCHING OPTIMIZED AI COMPANION DEMO")
        print("="*60)
        print("✨ Features:")
        print("  - Lightning-fast response times")
        print("  - Real-time emotion detection")
        print("  - Intelligent caching system")
        print("  - Live performance monitoring")
        print(f"🌐 Opening at: http://localhost:{settings.gradio_port}")
        print("="*60)
        
        interface.launch(
            server_name="0.0.0.0",
            server_port=settings.gradio_port,
            share=True,  # Create public link
            debug=False,
            show_error=True
        )

def main():
    """Launch the optimized demo."""
    demo = OptimizedDemoInterface()
    demo.launch()

if __name__ == "__main__":
    main()
