"""
Optimized Gemini Integration Service for the AI Companion System.
High-performance async operations with intelligent caching and batching.
"""

import google.generativeai as genai
import asyncio
import aiohttp
import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from functools import lru_cache
from collections import defaultdict, deque
import json
import re
import hashlib
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

from models import EmotionType, InteractionType, MemoryType
from config import settings

logger = logging.getLogger(__name__)

class OptimizedGeminiService:
    """High-performance Gemini API integration with async operations and caching."""
    
    def __init__(self):
        """Initialize the optimized Gemini service."""
        genai.configure(api_key=settings.gemini_api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        
        # Performance optimizations
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        self.response_cache = {}
        self.batch_queue = deque()
        self.batch_size = 5
        self.batch_timeout = 0.1  # 100ms
        
        # Rate limiting
        self.request_times = deque()
        self.max_requests_per_minute = 60
        
        # Performance metrics
        self.cache_hits = 0
        self.cache_misses = 0
        self.total_requests = 0
        
        # Start batch processor
        asyncio.create_task(self._process_batches())
    
    async def _process_batches(self):
        """Process batched requests for better performance."""
        while True:
            try:
                await asyncio.sleep(self.batch_timeout)
                if self.batch_queue:
                    batch = []
                    while self.batch_queue and len(batch) < self.batch_size:
                        batch.append(self.batch_queue.popleft())
                    
                    if batch:
                        await self._process_batch(batch)
            except Exception as e:
                logger.error(f"Batch processing error: {e}")
    
    async def _process_batch(self, batch: List[Tuple]):
        """Process a batch of requests."""
        # For now, process individually (can be optimized for true batching)
        for request_data in batch:
            try:
                future, prompt = request_data
                response = await self._generate_single_response(prompt)
                future.set_result(response)
            except Exception as e:
                future.set_exception(e)
    
    def _get_cache_key(self, prompt: str) -> str:
        """Generate cache key for prompt."""
        return hashlib.md5(prompt.encode()).hexdigest()
    
    async def generate_response_async(self, prompt: str) -> str:
        """Generate response asynchronously with caching."""
        cache_key = self._get_cache_key(prompt)
        
        # Check cache first
        if cache_key in self.response_cache:
            self.cache_hits += 1
            return self.response_cache[cache_key]
        
        self.cache_misses += 1
        self.total_requests += 1
        
        # Rate limiting check
        await self._check_rate_limit()
        
        # Generate response
        response = await self._generate_single_response(prompt)
        
        # Cache response (with size limit)
        if len(self.response_cache) < 1000:
            self.response_cache[cache_key] = response
        
        return response
    
    async def _check_rate_limit(self):
        """Check and enforce rate limiting."""
        current_time = time.time()
        
        # Remove old requests
        while self.request_times and current_time - self.request_times[0] > 60:
            self.request_times.popleft()
        
        # Check if we're at the limit
        if len(self.request_times) >= self.max_requests_per_minute:
            sleep_time = 60 - (current_time - self.request_times[0])
            if sleep_time > 0:
                await asyncio.sleep(sleep_time)
        
        self.request_times.append(current_time)
    
    async def _generate_single_response(self, prompt: str, retry_count: int = 0) -> str:
        """Generate a single response using thread pool with retry logic."""
        max_retries = 2

        try:
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                self.thread_pool,
                self._generate_sync,
                prompt
            )
            return response
        except Exception as e:
            error_msg = str(e)

            # If it's a rate limit error and we haven't exceeded retries
            if ("429" in error_msg or "rate limit" in error_msg.lower()) and retry_count < max_retries:
                # Exponential backoff: 1s, 2s, 4s
                wait_time = 2 ** retry_count
                logger.info(f"Rate limited, retrying in {wait_time}s (attempt {retry_count + 1}/{max_retries + 1})")
                await asyncio.sleep(wait_time)
                return await self._generate_single_response(prompt, retry_count + 1)
            else:
                # Use fallback response
                return self._get_fallback_response(prompt)
    
    def _generate_sync(self, prompt: str) -> str:
        """Synchronous response generation with intelligent fallbacks."""
        try:
            response = self.model.generate_content(prompt)
            return response.text if response.text else self._get_fallback_response(prompt)
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Gemini generation error: {e}")

            # Handle specific error types
            if "429" in error_msg or "quota" in error_msg.lower():
                return self._get_quota_fallback_response(prompt)
            elif "rate limit" in error_msg.lower():
                return self._get_rate_limit_fallback_response(prompt)
            else:
                return self._get_fallback_response(prompt)

    def _get_fallback_response(self, prompt: str) -> str:
        """Generate intelligent fallback response based on prompt content."""
        prompt_lower = prompt.lower()

        # Emotional support fallbacks
        if any(word in prompt_lower for word in ["sad", "depressed", "anxious", "worried", "stressed"]):
            return "I can sense you're going through a difficult time. I'm here to listen and support you. Would you like to share more about what's on your mind?"

        # Crisis-related fallbacks
        if any(word in prompt_lower for word in ["crisis", "emergency", "help", "can't take", "giving up"]):
            return "I'm concerned about what you're going through. Please know that you're not alone, and there are people who want to help. If you're in immediate danger, please contact emergency services or a crisis helpline."

        # General conversation fallbacks
        if any(word in prompt_lower for word in ["hello", "hi", "how are you", "good morning"]):
            return "Hello! I'm here and ready to chat with you. How are you feeling today?"

        # Default fallback
        return "I'm here to listen and support you. Could you tell me a bit more about what's on your mind?"

    def _get_quota_fallback_response(self, prompt: str) -> str:
        """Fallback for quota exceeded errors."""
        return "I'm experiencing high demand right now, but I'm still here for you. " + self._get_fallback_response(prompt)

    def _get_rate_limit_fallback_response(self, prompt: str) -> str:
        """Fallback for rate limit errors."""
        return "I need to slow down a bit, but I'm still listening. " + self._get_fallback_response(prompt)

# Backward compatibility alias
GeminiService = OptimizedGeminiService
