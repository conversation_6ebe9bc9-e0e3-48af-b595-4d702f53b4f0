"""
Simple AI Companion with Login System for Private Memory.
Each user gets their own private memory and conversation history.
"""

import gradio as gr
import time
import json
import logging
import hashlib
import sqlite3
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import uuid

# Import basic services (avoiding async issues)
from models import (
    MemoryEntry, MemoryType, InteractionType, EmotionType,
    UserProfile, EmotionalState
)
from config import settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleUserAuth:
    """Simple user authentication system."""
    
    def __init__(self):
        """Initialize the auth system."""
        self.db_path = "users.db"
        self.init_database()
        self.active_sessions = {}
    
    def init_database(self):
        """Initialize user database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                user_id TEXT PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_conversations (
                message_id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                role TEXT NOT NULL,
                content TEXT NOT NULL,
                emotion TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_memories (
                memory_id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                content TEXT NOT NULL,
                emotion TEXT,
                importance REAL DEFAULT 0.5,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def hash_password(self, password: str) -> str:
        """Hash password for secure storage."""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def register_user(self, username: str, password: str) -> Tuple[bool, str]:
        """Register a new user."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if username exists
            cursor.execute("SELECT username FROM users WHERE username = ?", (username,))
            if cursor.fetchone():
                return False, "Username already exists"
            
            # Create new user
            user_id = f"user_{int(time.time())}_{hash(username) % 10000}"
            password_hash = self.hash_password(password)
            
            cursor.execute('''
                INSERT INTO users (user_id, username, password_hash)
                VALUES (?, ?, ?)
            ''', (user_id, username, password_hash))
            
            conn.commit()
            conn.close()
            
            return True, f"User {username} registered successfully!"
            
        except Exception as e:
            logger.error(f"Registration error: {e}")
            return False, f"Registration failed: {str(e)}"
    
    def login_user(self, username: str, password: str) -> Tuple[bool, str, Optional[str]]:
        """Login user and create session."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Verify credentials
            password_hash = self.hash_password(password)
            cursor.execute('''
                SELECT user_id FROM users 
                WHERE username = ? AND password_hash = ?
            ''', (username, password_hash))
            
            result = cursor.fetchone()
            if not result:
                return False, "Invalid username or password", None
            
            user_id = result[0]
            
            # Create session
            session_id = f"session_{int(time.time())}_{hash(username) % 10000}"
            
            conn.close()
            
            # Store in active sessions
            self.active_sessions[session_id] = {
                "user_id": user_id,
                "username": username,
                "login_time": time.time()
            }
            
            return True, f"Welcome back, {username}!", session_id
            
        except Exception as e:
            logger.error(f"Login error: {e}")
            return False, f"Login failed: {str(e)}", None
    
    def get_user_from_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get user info from session."""
        return self.active_sessions.get(session_id)
    
    def store_conversation(self, user_id: str, role: str, content: str, emotion: str = None):
        """Store conversation message."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            message_id = str(uuid.uuid4())
            cursor.execute('''
                INSERT INTO user_conversations (message_id, user_id, role, content, emotion)
                VALUES (?, ?, ?, ?, ?)
            ''', (message_id, user_id, role, content, emotion))
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            logger.error(f"Error storing conversation: {e}")
            return False
    
    def get_conversation_history(self, user_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Get conversation history for user."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT role, content, emotion, timestamp
                FROM user_conversations
                WHERE user_id = ?
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (user_id, limit))
            
            messages = []
            for row in cursor.fetchall():
                messages.append({
                    "role": row[0],
                    "content": row[1],
                    "emotion": row[2],
                    "timestamp": row[3]
                })
            
            conn.close()
            return list(reversed(messages))  # Return in chronological order
            
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []
    
    def store_memory(self, user_id: str, content: str, emotion: str = None, importance: float = 0.5):
        """Store a memory for the user."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            memory_id = str(uuid.uuid4())
            cursor.execute('''
                INSERT INTO user_memories (memory_id, user_id, content, emotion, importance)
                VALUES (?, ?, ?, ?, ?)
            ''', (memory_id, user_id, content, emotion, importance))
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            logger.error(f"Error storing memory: {e}")
            return False
    
    def get_user_memories(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get memories for user."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT content, emotion, importance, created_at
                FROM user_memories
                WHERE user_id = ?
                ORDER BY importance DESC, created_at DESC
                LIMIT ?
            ''', (user_id, limit))
            
            memories = []
            for row in cursor.fetchall():
                memories.append({
                    "content": row[0],
                    "emotion": row[1],
                    "importance": row[2],
                    "created_at": row[3]
                })
            
            conn.close()
            return memories
            
        except Exception as e:
            logger.error(f"Error getting memories: {e}")
            return []

class SimpleAICompanion:
    """Simple AI Companion with login and private memory."""
    
    def __init__(self):
        """Initialize the AI companion."""
        self.auth = SimpleUserAuth()
    
    def detect_emotion(self, message: str) -> str:
        """Simple emotion detection."""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["happy", "excited", "great", "wonderful", "amazing"]):
            return "joy"
        elif any(word in message_lower for word in ["sad", "down", "upset", "depressed"]):
            return "sadness"
        elif any(word in message_lower for word in ["angry", "mad", "frustrated", "annoyed"]):
            return "anger"
        elif any(word in message_lower for word in ["anxious", "worried", "nervous", "stressed"]):
            return "anxiety"
        else:
            return "neutral"
    
    def generate_response(self, message: str, emotion: str, user_memories: List[Dict]) -> str:
        """Generate a simple response based on emotion and memories."""
        
        # Check if we have relevant memories
        relevant_memories = [mem for mem in user_memories if any(word in mem["content"].lower() for word in message.lower().split())]
        
        if emotion == "anxiety":
            if "interview" in message.lower():
                response = "I remember you've been preparing for this! Job interviews can feel overwhelming, but remember - they invited you because they're interested in you. Take deep breaths and focus on your strengths."
            else:
                response = "I can hear the worry in your message. Anxiety is tough, but you're not alone. What's been on your mind lately?"
        
        elif emotion == "joy":
            if "promoted" in message.lower() or "job" in message.lower():
                response = "Congratulations! That's fantastic news! 🎉 I'm so happy for you. You must have worked really hard for this achievement!"
            else:
                response = "Your excitement is wonderful to hear! I love seeing you so positive. Tell me more about what's making you feel so good!"
        
        elif emotion == "sadness":
            response = "I'm sorry you're going through a tough time. Your feelings are valid, and it's brave of you to share them with me. I'm here to listen and support you."
        
        elif emotion == "anger":
            response = "It sounds like you're really frustrated, and that's completely understandable. Sometimes things just don't go the way we hope. Want to tell me more about what's bothering you?"
        
        else:
            if relevant_memories:
                response = f"I remember we talked about {relevant_memories[0]['content'][:50]}... How are things going with that?"
            else:
                response = "I'm here to listen and chat with you. What's on your mind today?"
        
        return response
    
    def create_interface(self) -> gr.Blocks:
        """Create the Gradio interface."""
        
        with gr.Blocks(
            title="AI Companion - Private Memory",
            theme=gr.themes.Soft()
        ) as interface:
            
            # State variables
            session_state = gr.State(value=None)
            user_state = gr.State(value=None)
            
            gr.Markdown("""
            # 🤖 AI Companion - Private Memory System
            
            **Your personal AI companion that remembers everything about you!**
            
            Each user gets their own private memory and conversation history.
            """)
            
            # Login/Register Section
            with gr.Column(visible=True) as login_section:
                gr.Markdown("## 🔐 Login or Register")
                
                with gr.Tabs():
                    with gr.Tab("Login"):
                        login_username = gr.Textbox(label="Username", placeholder="Enter your username")
                        login_password = gr.Textbox(label="Password", type="password", placeholder="Enter your password")
                        login_btn = gr.Button("Login", variant="primary")
                        login_message = gr.Textbox(label="Status", interactive=False)
                    
                    with gr.Tab("Register"):
                        reg_username = gr.Textbox(label="Username", placeholder="Choose a username")
                        reg_password = gr.Textbox(label="Password", type="password", placeholder="Choose a password")
                        register_btn = gr.Button("Register", variant="secondary")
                        register_message = gr.Textbox(label="Status", interactive=False)
            
            # Main Chat Interface
            with gr.Column(visible=False) as chat_section:
                
                # User info header
                user_info = gr.Markdown("### Welcome!")
                logout_btn = gr.Button("Logout", size="sm")
                
                # Chat interface
                with gr.Row():
                    with gr.Column(scale=3):
                        chatbot = gr.Chatbot(
                            label="💬 Your Personal AI Companion",
                            height=400,
                            type="messages"
                        )
                        
                        with gr.Row():
                            msg_input = gr.Textbox(
                                placeholder="Share anything with me - I'll remember it all!",
                                label="Your Message",
                                lines=2,
                                scale=4
                            )
                            send_btn = gr.Button("Send", variant="primary", scale=1)
                    
                    with gr.Column(scale=1):
                        gr.Markdown("### 🧠 Your Memories")
                        memory_display = gr.JSON(label="Recent Memories", value={})
                        
                        gr.Markdown("### 📊 Your Stats")
                        stats_display = gr.JSON(label="Stats", value={})
            
            # Event handlers
            def handle_login(username: str, password: str):
                """Handle user login."""
                success, message, session_id = self.auth.login_user(username, password)
                
                if success:
                    user_info_data = self.auth.get_user_from_session(session_id)
                    
                    # Load conversation history
                    history = self.auth.get_conversation_history(user_info_data["user_id"])
                    chat_history = []
                    for msg in history:
                        chat_history.append({"role": msg["role"], "content": msg["content"]})
                    
                    # Load memories
                    memories = self.auth.get_user_memories(user_info_data["user_id"])
                    memory_data = {"recent_memories": memories[:5]}
                    
                    stats = {
                        "total_messages": len(history),
                        "total_memories": len(memories),
                        "username": username
                    }
                    
                    return (
                        message,  # login_message
                        gr.update(visible=False),  # login_section
                        gr.update(visible=True),   # chat_section
                        session_id,  # session_state
                        user_info_data,   # user_state
                        f"### Welcome back, {username}! 🎉",  # user_info
                        chat_history,  # chatbot
                        memory_data,  # memory_display
                        stats  # stats_display
                    )
                else:
                    return (
                        message,  # login_message
                        gr.update(visible=True),   # login_section
                        gr.update(visible=False),  # chat_section
                        None,  # session_state
                        None,  # user_state
                        "### Welcome!",  # user_info
                        [],  # chatbot
                        {},  # memory_display
                        {}   # stats_display
                    )
            
            def handle_register(username: str, password: str):
                """Handle user registration."""
                success, message = self.auth.register_user(username, password)
                return message
            
            def handle_logout(session_id: str):
                """Handle user logout."""
                return (
                    gr.update(visible=True),   # login_section
                    gr.update(visible=False),  # chat_section
                    None,  # session_state
                    None,  # user_state
                    "### Welcome!",  # user_info
                    [],  # chatbot
                    {},  # memory_display
                    {}   # stats_display
                )
            
            def handle_message(message: str, history: List, session_id: str, user_info: Dict):
                """Handle chat message."""
                if not session_id or not user_info:
                    return history, "", {}, {}
                
                user_id = user_info["user_id"]
                
                # Detect emotion
                emotion = self.detect_emotion(message)
                
                # Get user memories
                memories = self.auth.get_user_memories(user_id)
                
                # Generate response
                response = self.generate_response(message, emotion, memories)
                
                # Store conversation
                self.auth.store_conversation(user_id, "user", message, emotion)
                self.auth.store_conversation(user_id, "assistant", response)
                
                # Store as memory if important
                if emotion != "neutral" or len(message) > 50:
                    importance = 0.8 if emotion in ["anxiety", "sadness"] else 0.6
                    self.auth.store_memory(user_id, message, emotion, importance)
                
                # Update history
                history.append({"role": "user", "content": message})
                history.append({"role": "assistant", "content": response})
                
                # Update displays
                updated_memories = self.auth.get_user_memories(user_id)
                memory_data = {"recent_memories": updated_memories[:5]}
                
                stats = {
                    "total_messages": len(history),
                    "total_memories": len(updated_memories),
                    "current_emotion": emotion,
                    "username": user_info["username"]
                }
                
                return history, "", memory_data, stats
            
            # Wire up events
            login_btn.click(
                fn=handle_login,
                inputs=[login_username, login_password],
                outputs=[login_message, login_section, chat_section, session_state, user_state, user_info, chatbot, memory_display, stats_display]
            )
            
            register_btn.click(
                fn=handle_register,
                inputs=[reg_username, reg_password],
                outputs=[register_message]
            )
            
            logout_btn.click(
                fn=handle_logout,
                inputs=[session_state],
                outputs=[login_section, chat_section, session_state, user_state, user_info, chatbot, memory_display, stats_display]
            )
            
            send_btn.click(
                fn=handle_message,
                inputs=[msg_input, chatbot, session_state, user_state],
                outputs=[chatbot, msg_input, memory_display, stats_display]
            )
            
            msg_input.submit(
                fn=handle_message,
                inputs=[msg_input, chatbot, session_state, user_state],
                outputs=[chatbot, msg_input, memory_display, stats_display]
            )
        
        return interface
    
    def launch(self):
        """Launch the AI companion."""
        interface = self.create_interface()
        
        print("\n" + "="*60)
        print("🔐 AI COMPANION WITH PRIVATE MEMORY")
        print("="*60)
        print("✨ Features:")
        print("  - User registration and login")
        print("  - Private memory for each user")
        print("  - Persistent conversation history")
        print("  - Emotion detection and tracking")
        print("  - Personal memory storage")
        print(f"🌐 Access at: http://localhost:7861")
        print("="*60)
        
        interface.launch(
            server_name="0.0.0.0",
            server_port=7861,  # Use different port
            share=True,
            debug=False,
            show_error=True
        )

def main():
    """Launch the simple AI companion with login."""
    app = SimpleAICompanion()
    app.launch()

if __name__ == "__main__":
    main()
