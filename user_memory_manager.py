"""
User Memory Manager for AI Companion System.
Handles user-specific memory storage and retrieval with privacy protection.
"""

import sqlite3
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import uuid

from models import MemoryEntry, MemoryType, InteractionType, EmotionType

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UserMemoryManager:
    """Manages user-specific memories with privacy protection."""
    
    def __init__(self, db_path: str = "user_memories.db"):
        """Initialize the user memory manager."""
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize the user memory database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create user memories table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_memories (
                memory_id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                memory_type TEXT NOT NULL,
                interaction_type TEXT NOT NULL,
                content TEXT NOT NULL,
                context TEXT,
                emotion TEXT,
                confidence REAL DEFAULT 1.0,
                importance REAL DEFAULT 0.5,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                access_count INTEGER DEFAULT 0,
                tags TEXT,
                is_private BOOLEAN DEFAULT 1
            )
        ''')
        
        # Create conversation history table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS conversation_history (
                message_id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                conversation_id TEXT NOT NULL,
                role TEXT NOT NULL,
                content TEXT NOT NULL,
                emotion TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                response_time REAL
            )
        ''')
        
        # Create user preferences table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_preferences (
                user_id TEXT PRIMARY KEY,
                preferences TEXT,
                interests TEXT,
                communication_style TEXT,
                emotional_patterns TEXT,
                privacy_settings TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create indexes for better performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_memories_user_id ON user_memories(user_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_conversation_history_user_id ON conversation_history(user_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_conversation_history_conv_id ON conversation_history(conversation_id)')
        
        conn.commit()
        conn.close()
        logger.info("User memory database initialized")
    
    def store_user_memory(self, user_id: str, memory: MemoryEntry) -> bool:
        """Store a memory for a specific user."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO user_memories (
                    memory_id, user_id, memory_type, interaction_type, content,
                    context, emotion, confidence, importance, tags
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                memory.id,
                user_id,
                memory.memory_type.value,
                memory.interaction_type.value,
                memory.content,
                json.dumps(memory.context),
                memory.emotion.value if memory.emotion else None,
                memory.confidence,
                memory.importance,
                json.dumps(memory.get_search_keywords())
            ))
            
            conn.commit()
            conn.close()
            logger.info(f"Stored memory for user {user_id}: {memory.id}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing user memory: {e}")
            return False
    
    def get_user_memories(self, user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get all memories for a specific user."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT memory_id, memory_type, interaction_type, content, context,
                       emotion, confidence, importance, created_at, access_count, tags
                FROM user_memories
                WHERE user_id = ? AND is_private = 1
                ORDER BY importance DESC, created_at DESC
                LIMIT ?
            ''', (user_id, limit))
            
            memories = []
            for row in cursor.fetchall():
                memory_data = {
                    "memory_id": row[0],
                    "memory_type": row[1],
                    "interaction_type": row[2],
                    "content": row[3],
                    "context": json.loads(row[4]) if row[4] else {},
                    "emotion": row[5],
                    "confidence": row[6],
                    "importance": row[7],
                    "created_at": row[8],
                    "access_count": row[9],
                    "tags": json.loads(row[10]) if row[10] else []
                }
                memories.append(memory_data)
            
            conn.close()
            return memories
            
        except Exception as e:
            logger.error(f"Error getting user memories: {e}")
            return []
    
    def search_user_memories(self, user_id: str, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search memories for a specific user."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Simple text search (can be enhanced with FTS)
            search_query = f"%{query.lower()}%"
            cursor.execute('''
                SELECT memory_id, memory_type, interaction_type, content, context,
                       emotion, confidence, importance, created_at, access_count, tags
                FROM user_memories
                WHERE user_id = ? AND is_private = 1
                AND (LOWER(content) LIKE ? OR LOWER(tags) LIKE ?)
                ORDER BY importance DESC, created_at DESC
                LIMIT ?
            ''', (user_id, search_query, search_query, limit))
            
            memories = []
            for row in cursor.fetchall():
                memory_data = {
                    "memory_id": row[0],
                    "memory_type": row[1],
                    "interaction_type": row[2],
                    "content": row[3],
                    "context": json.loads(row[4]) if row[4] else {},
                    "emotion": row[5],
                    "confidence": row[6],
                    "importance": row[7],
                    "created_at": row[8],
                    "access_count": row[9],
                    "tags": json.loads(row[10]) if row[10] else []
                }
                memories.append(memory_data)
                
                # Update access count
                cursor.execute('''
                    UPDATE user_memories 
                    SET access_count = access_count + 1, last_accessed = CURRENT_TIMESTAMP
                    WHERE memory_id = ?
                ''', (row[0],))
            
            conn.commit()
            conn.close()
            return memories
            
        except Exception as e:
            logger.error(f"Error searching user memories: {e}")
            return []
    
    def store_conversation_message(self, user_id: str, conversation_id: str, 
                                 role: str, content: str, emotion: str = None, 
                                 response_time: float = None) -> bool:
        """Store a conversation message."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            message_id = str(uuid.uuid4())
            cursor.execute('''
                INSERT INTO conversation_history (
                    message_id, user_id, conversation_id, role, content, emotion, response_time
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (message_id, user_id, conversation_id, role, content, emotion, response_time))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"Error storing conversation message: {e}")
            return False
    
    def get_conversation_history(self, user_id: str, conversation_id: str = None, 
                               limit: int = 100) -> List[Dict[str, Any]]:
        """Get conversation history for a user."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if conversation_id:
                cursor.execute('''
                    SELECT message_id, role, content, emotion, timestamp, response_time
                    FROM conversation_history
                    WHERE user_id = ? AND conversation_id = ?
                    ORDER BY timestamp ASC
                    LIMIT ?
                ''', (user_id, conversation_id, limit))
            else:
                cursor.execute('''
                    SELECT message_id, conversation_id, role, content, emotion, timestamp, response_time
                    FROM conversation_history
                    WHERE user_id = ?
                    ORDER BY timestamp DESC
                    LIMIT ?
                ''', (user_id, limit))
            
            messages = []
            for row in cursor.fetchall():
                if conversation_id:
                    message_data = {
                        "message_id": row[0],
                        "role": row[1],
                        "content": row[2],
                        "emotion": row[3],
                        "timestamp": row[4],
                        "response_time": row[5]
                    }
                else:
                    message_data = {
                        "message_id": row[0],
                        "conversation_id": row[1],
                        "role": row[2],
                        "content": row[3],
                        "emotion": row[4],
                        "timestamp": row[5],
                        "response_time": row[6]
                    }
                messages.append(message_data)
            
            conn.close()
            return messages
            
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []
    
    def get_user_stats(self, user_id: str) -> Dict[str, Any]:
        """Get statistics for a user."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get memory count
            cursor.execute('SELECT COUNT(*) FROM user_memories WHERE user_id = ?', (user_id,))
            memory_count = cursor.fetchone()[0]
            
            # Get conversation count
            cursor.execute('SELECT COUNT(DISTINCT conversation_id) FROM conversation_history WHERE user_id = ?', (user_id,))
            conversation_count = cursor.fetchone()[0]
            
            # Get message count
            cursor.execute('SELECT COUNT(*) FROM conversation_history WHERE user_id = ?', (user_id,))
            message_count = cursor.fetchone()[0]
            
            # Get most common emotions
            cursor.execute('''
                SELECT emotion, COUNT(*) as count
                FROM user_memories
                WHERE user_id = ? AND emotion IS NOT NULL
                GROUP BY emotion
                ORDER BY count DESC
                LIMIT 5
            ''', (user_id,))
            
            emotions = [{"emotion": row[0], "count": row[1]} for row in cursor.fetchall()]
            
            # Get recent activity
            cursor.execute('''
                SELECT DATE(timestamp) as date, COUNT(*) as messages
                FROM conversation_history
                WHERE user_id = ? AND timestamp >= datetime('now', '-7 days')
                GROUP BY DATE(timestamp)
                ORDER BY date DESC
            ''', (user_id,))
            
            recent_activity = [{"date": row[0], "messages": row[1]} for row in cursor.fetchall()]
            
            conn.close()
            
            return {
                "memory_count": memory_count,
                "conversation_count": conversation_count,
                "message_count": message_count,
                "top_emotions": emotions,
                "recent_activity": recent_activity
            }
            
        except Exception as e:
            logger.error(f"Error getting user stats: {e}")
            return {}
    
    def delete_user_data(self, user_id: str) -> bool:
        """Delete all data for a user (GDPR compliance)."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Delete user memories
            cursor.execute('DELETE FROM user_memories WHERE user_id = ?', (user_id,))
            
            # Delete conversation history
            cursor.execute('DELETE FROM conversation_history WHERE user_id = ?', (user_id,))
            
            # Delete user preferences
            cursor.execute('DELETE FROM user_preferences WHERE user_id = ?', (user_id,))
            
            conn.commit()
            conn.close()
            
            logger.info(f"Deleted all data for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting user data: {e}")
            return False
