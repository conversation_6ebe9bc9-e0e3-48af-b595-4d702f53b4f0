"""
Verification test script for the fixes applied to the AI Companion System.
Tests the specific issues that were failing in the previous test run.
"""

import asyncio
import logging
import time
from datetime import datetime, timezone
from typing import Dict, Any, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FixesVerificationTest:
    """Test suite to verify all fixes are working correctly."""
    
    def __init__(self):
        """Initialize test environment."""
        self.test_results = {}
        
    async def test_conversation_context_fix(self):
        """Test that ConversationContext initialization is fixed."""
        logger.info("🔧 Testing ConversationContext Fix...")
        
        try:
            from ultra_fast_conversation_service import UltraFastConversationService
            
            # Initialize service
            service = UltraFastConversationService()
            
            # Test message processing (this should not crash now)
            test_user = "fix_test_user"
            test_message = "Hello, this is a test message"
            
            response, processing_time, metrics = await service.process_message_ultra_fast(
                user_id=test_user,
                message=test_message
            )
            
            success = (
                response is not None and 
                processing_time > 0 and 
                isinstance(metrics, dict)
            )
            
            self.test_results['conversation_context_fix'] = {
                'success': success,
                'response_received': response is not None,
                'processing_time': processing_time,
                'metrics_available': isinstance(metrics, dict),
                'error': None
            }
            
            logger.info(f"✅ ConversationContext fix: {'PASSED' if success else 'FAILED'}")
            return success
            
        except Exception as e:
            logger.error(f"❌ ConversationContext fix failed: {e}")
            self.test_results['conversation_context_fix'] = {
                'success': False,
                'error': str(e)
            }
            return False
    
    async def test_json_parsing_fix(self):
        """Test that _parse_json_response method is working."""
        logger.info("🔧 Testing JSON Parsing Fix...")
        
        try:
            from enhanced_emotional_intelligence import EnhancedEmotionalIntelligence
            from gemini_service import GeminiService
            
            # Initialize services
            gemini_service = GeminiService()
            ei_service = EnhancedEmotionalIntelligence(gemini_service)
            
            # Test the _parse_json_response method directly
            test_json_responses = [
                '{"test": "value", "number": 42}',
                '```json\n{"wrapped": "in_markdown"}\n```',
                '```\n{"generic_wrapper": true}\n```',
                'invalid json string',
                ''
            ]
            
            parsing_results = []
            for test_response in test_json_responses:
                try:
                    result = ei_service._parse_json_response(test_response)
                    parsing_results.append({
                        'input': test_response,
                        'output': result,
                        'success': isinstance(result, dict)
                    })
                except Exception as e:
                    parsing_results.append({
                        'input': test_response,
                        'output': None,
                        'success': False,
                        'error': str(e)
                    })
            
            # Check if method exists and handles various inputs
            method_exists = hasattr(ei_service, '_parse_json_response')
            successful_parses = sum(1 for r in parsing_results if r['success'])
            
            success = method_exists and successful_parses >= 3  # At least 3 should parse successfully
            
            self.test_results['json_parsing_fix'] = {
                'success': success,
                'method_exists': method_exists,
                'successful_parses': successful_parses,
                'total_tests': len(test_json_responses),
                'parsing_results': parsing_results
            }
            
            logger.info(f"✅ JSON parsing fix: {'PASSED' if success else 'FAILED'}")
            logger.info(f"   Method exists: {method_exists}, Successful parses: {successful_parses}/{len(test_json_responses)}")
            return success
            
        except Exception as e:
            logger.error(f"❌ JSON parsing fix failed: {e}")
            self.test_results['json_parsing_fix'] = {
                'success': False,
                'error': str(e)
            }
            return False
    
    def test_crisis_detection_fix(self):
        """Test that crisis detection is improved."""
        logger.info("🔧 Testing Crisis Detection Fix...")
        
        try:
            from whatsapp_bot_integration import WhatsAppBotService
            
            # Initialize bot service
            bot = WhatsAppBotService(
                webhook_verify_token="test_token",
                access_token="test_access_token", 
                phone_number_id="test_phone_id"
            )
            
            # Test the exact messages from the failing test
            test_messages = [
                "I want to kill myself",
                "I can't go on anymore", 
                "There's no point in living"  # This was the failing one
            ]
            
            detection_results = []
            for message in test_messages:
                detected = bot._is_crisis_message(message)
                detection_results.append({
                    'message': message,
                    'detected': detected
                })
                logger.info(f"   '{message}' -> {'DETECTED' if detected else 'NOT DETECTED'}")
            
            # All messages should be detected now
            all_detected = all(r['detected'] for r in detection_results)
            detected_count = sum(1 for r in detection_results if r['detected'])
            
            self.test_results['crisis_detection_fix'] = {
                'success': all_detected,
                'detected_count': detected_count,
                'total_messages': len(test_messages),
                'detection_results': detection_results,
                'keywords_count': len(bot.crisis_keywords)
            }
            
            logger.info(f"✅ Crisis detection fix: {'PASSED' if all_detected else 'FAILED'}")
            logger.info(f"   Detected: {detected_count}/{len(test_messages)} messages")
            logger.info(f"   Keywords available: {len(bot.crisis_keywords)}")
            return all_detected
            
        except Exception as e:
            logger.error(f"❌ Crisis detection fix failed: {e}")
            self.test_results['crisis_detection_fix'] = {
                'success': False,
                'error': str(e)
            }
            return False
    
    async def test_cache_effectiveness(self):
        """Test that cache effectiveness is working after ConversationContext fix."""
        logger.info("🔧 Testing Cache Effectiveness...")
        
        try:
            from ultra_fast_conversation_service import UltraFastConversationService
            
            service = UltraFastConversationService()
            test_user = "cache_test_user"
            test_message = "How are you feeling today?"
            
            # Clear caches
            service.clear_caches()
            
            # First request (should be slower)
            start_time = time.time()
            response1, time1, metrics1 = await service.process_message_ultra_fast(
                user_id=test_user,
                message=test_message
            )
            first_request_time = time.time() - start_time
            
            # Second request (should be faster due to caching)
            start_time = time.time()
            response2, time2, metrics2 = await service.process_message_ultra_fast(
                user_id=test_user,
                message=test_message
            )
            second_request_time = time.time() - start_time
            
            # Calculate speedup
            speedup = first_request_time / second_request_time if second_request_time > 0 else 0
            cache_working = speedup > 1.0  # Second request should be faster
            
            self.test_results['cache_effectiveness'] = {
                'success': cache_working,
                'first_request_time': first_request_time,
                'second_request_time': second_request_time,
                'speedup': speedup,
                'cache_hit_second': metrics2.get('cache_hit', False),
                'responses_match': response1 == response2
            }
            
            logger.info(f"✅ Cache effectiveness: {'PASSED' if cache_working else 'FAILED'}")
            logger.info(f"   Speedup: {speedup:.2f}x")
            logger.info(f"   First: {first_request_time:.3f}s, Second: {second_request_time:.3f}s")
            return cache_working
            
        except Exception as e:
            logger.error(f"❌ Cache effectiveness test failed: {e}")
            self.test_results['cache_effectiveness'] = {
                'success': False,
                'error': str(e)
            }
            return False
    
    async def run_all_tests(self):
        """Run all verification tests."""
        logger.info("🚀 Starting Fixes Verification Tests...")
        
        test_functions = [
            ('ConversationContext Fix', self.test_conversation_context_fix),
            ('JSON Parsing Fix', self.test_json_parsing_fix),
            ('Crisis Detection Fix', self.test_crisis_detection_fix),
            ('Cache Effectiveness', self.test_cache_effectiveness)
        ]
        
        results = {}
        for test_name, test_func in test_functions:
            try:
                if asyncio.iscoroutinefunction(test_func):
                    success = await test_func()
                else:
                    success = test_func()
                results[test_name] = success
            except Exception as e:
                logger.error(f"❌ {test_name} failed with exception: {e}")
                results[test_name] = False
        
        # Generate summary
        passed_tests = sum(1 for success in results.values() if success)
        total_tests = len(results)
        success_rate = (passed_tests / total_tests) * 100
        
        logger.info("\n" + "="*80)
        logger.info("📋 FIXES VERIFICATION SUMMARY")
        logger.info("="*80)
        
        for test_name, success in results.items():
            status = "✅ PASS" if success else "❌ FAIL"
            logger.info(f"  {status} {test_name}")
        
        logger.info(f"\n🏆 OVERALL RESULTS:")
        logger.info(f"    Tests Passed: {passed_tests}/{total_tests}")
        logger.info(f"    Success Rate: {success_rate:.1f}%")
        
        if success_rate == 100:
            logger.info("    🎉 All fixes verified successfully!")
        else:
            logger.info("    ⚠️  Some fixes need additional work.")
        
        logger.info("="*80)
        
        return results

async def main():
    """Main entry point for verification tests."""
    tester = FixesVerificationTest()
    results = await tester.run_all_tests()
    
    # Return exit code based on results
    all_passed = all(results.values())
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
