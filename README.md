# 🧠 AI Companion System

**A Conversational AI for Emotional Support and Mental Health**

A production-ready AI companion system featuring advanced emotional intelligence, memory architecture, crisis detection, and therapeutic conversation capabilities. Built with Google Gemini API and designed for scalable deployment.

## ✨ Key Features

### 🧠 Advanced AI Capabilities
- **Emotional Intelligence**: Real-time emotion detection and empathetic responses
- **Memory Architecture**: Dual-memory system (personal + universal) with intelligent retrieval
- **Crisis Detection**: Automated mental health risk assessment and intervention
- **Therapeutic Techniques**: Evidence-based conversation strategies and support

### 🚀 Production-Ready
- **High Performance**: Sub-second response times with intelligent caching
- **Scalable Architecture**: Async processing for concurrent users
- **Comprehensive Monitoring**: Real-time metrics and health checks
- **Security First**: Data encryption, privacy protection, and secure storage

### 🌐 Multiple Interfaces
- **Web Interface**: Gradio-based UI for development and testing
- **REST API**: FastAPI-based production API
- **WhatsApp Bot**: Optional integration for 24/7 support
- **CLI Tools**: Command-line interface for administration

### 🏥 Mental Health Focus
- **Privacy-First**: GDPR/HIPAA compliant data handling
- **Crisis Intervention**: Immediate support for high-risk situations
- **Analytics Platform**: Anonymized insights for research
- **Professional Integration**: Tools for therapists and researchers

## 🏗️ System Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    🌐 Interface Layer                       │
├─────────────────┬─────────────────┬─────────────────────────┤
│   Gradio UI     │   FastAPI       │   WhatsApp Bot          │
│   (Development) │   (Production)  │   (Optional)            │
└─────────────────┴─────────────────┴─────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   🧠 Core AI Services                       │
├─────────────────┬─────────────────┬─────────────────────────┤
│   Conversation  │   Memory        │   Emotional             │
│   Service       │   Service       │   Intelligence          │
└─────────────────┴─────────────────┴─────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                🏥 Mental Health Platform                    │
├─────────────────┬─────────────────┬─────────────────────────┤
│   Crisis        │   Analytics     │   Privacy               │
│   Detection     │   Platform      │   Protection            │
└─────────────────┴─────────────────┴─────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   💾 Data Layer                             │
├─────────────────┬─────────────────┬─────────────────────────┤
│   SQLite/       │   Redis         │   File                  │
│   PostgreSQL    │   Cache         │   Storage               │
└─────────────────┴─────────────────┴─────────────────────────┘
```

## 🚀 Quick Start

### 1. Prerequisites

- Python 3.9 or higher
- Google Gemini API key
- Redis (optional, for caching)

### 2. Installation

```bash
# Clone the repository
git clone <repository-url>
cd ai-companion-system

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 3. Configuration

Copy the environment template and configure:

```bash
cp .env.example .env
```

Edit `.env` with your settings:

```env
# Required: Google Gemini API Key
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: Database (defaults to SQLite)
DATABASE_URL=sqlite:///./data/db/ai_companion.db

# Optional: Redis for caching
REDIS_URL=redis://localhost:6379

# System Configuration
ENVIRONMENT=development
LOG_LEVEL=INFO
DEBUG_MODE=true
```

### 4. Run the System

```bash
# Start the AI Companion System
python -m src.ai_companion.main

# Or use the CLI
ai-companion
```

### 5. Access the Interface

- **Web Interface**: http://localhost:7860
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## 🧪 Testing

### Run Tests

```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Run all tests
pytest

# Run with coverage
pytest --cov=src/ai_companion

# Run specific test types
pytest tests/unit/          # Unit tests
pytest tests/integration/   # Integration tests
pytest tests/performance/   # Performance tests
```

### Test Categories

- **Unit Tests**: Individual component testing
- **Integration Tests**: Service interaction testing
- **Performance Tests**: Response time and load testing
- **Security Tests**: Data protection and privacy testing

### Performance Targets

- **Response Time**: < 300ms average
- **Memory Usage**: < 500MB under normal load
- **Cache Hit Rate**: > 80% for repeated interactions
- **Concurrent Users**: 100+ simultaneous conversations

## ⚙️ Configuration

### Environment Variables

All configuration is done through environment variables. See `.env.example` for all available options.

#### Core Settings
```env
# AI Configuration
GEMINI_API_KEY=your_api_key
RESPONSE_TEMPERATURE=0.7
MAX_TOKENS=150

# Memory Configuration
MEMORY_TTL=2592000              # 30 days
PERSONAL_MEMORY_SIZE=1000
UNIVERSAL_MEMORY_SIZE=10000

# Performance Settings
CACHE_SIZE=1000
TARGET_RESPONSE_TIME=0.3
TARGET_CACHE_HIT_RATE=0.8
```

#### Mental Health Settings
```env
CRISIS_THRESHOLD=0.7
MODERATE_RISK_THRESHOLD=0.4
ENABLE_MENTAL_HEALTH_PLATFORM=true
ANONYMIZATION_ENABLED=true
```

#### Security Settings
```env
SECRET_KEY=your-secret-key-32-chars-minimum
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ORIGINS=http://localhost:3000
```

## 🧠 Memory Architecture

The AI Companion uses a sophisticated dual-memory system inspired by cognitive science:

### Memory Types
- **Personal Memory**: User-specific memories, strictly isolated per user
- **Universal Memory**: Shared knowledge across all users (anonymized)
- **Working Memory**: Short-term context for active conversations
- **Episodic Memory**: Specific conversation events with rich context

### Memory Features
- **Intelligent Retrieval**: Context-aware memory search and ranking
- **Emotional Weighting**: Emotionally significant memories are prioritized
- **Automatic Cleanup**: Old, unused memories are gradually forgotten
- **Privacy Protection**: Personal memories are never shared between users

### Example Usage
```python
from ai_companion.core.memory import MemoryService

# Store a memory
memory = await memory_service.store_memory(
    user_id="user123",
    content="User mentioned feeling anxious about work presentation",
    interaction_type="emotion",
    emotion="anxiety"
)

# Retrieve relevant memories
memories = await memory_service.retrieve_memories(
    user_id="user123",
    query="work stress",
    limit=5
)
```

## 💝 Emotional Intelligence

The system provides advanced emotional understanding and support:

### Emotion Detection
- **Real-time Analysis**: Detects emotions from text with high accuracy
- **Multi-dimensional**: Analyzes valence, arousal, and emotional intensity
- **Context-aware**: Considers conversation history and user patterns
- **Confidence Scoring**: Provides reliability metrics for detections

### Therapeutic Techniques
- **Active Listening**: Reflective and empathetic responses
- **Validation**: Acknowledging and normalizing user emotions
- **Cognitive Reframing**: Helping users see situations differently
- **Mindfulness**: Grounding techniques for anxiety and stress
- **Crisis Intervention**: Immediate support for high-risk situations

### Crisis Detection
```python
from ai_companion.mental_health.crisis_detection import CrisisDetectionService

# Analyze mental health risk
risk_assessment = await crisis_detection.assess_risk(
    user_id="user123",
    message="I can't handle this anymore",
    emotional_state=emotional_state
)

if risk_assessment.risk_level == "critical":
    # Trigger immediate intervention
    response = await crisis_detection.generate_crisis_response(
        risk_assessment
    )
```

## 🔒 Privacy & Security

### Data Protection
- **Encryption**: All data encrypted in transit and at rest
- **User Isolation**: Personal memories strictly separated between users
- **Anonymization**: Research data is anonymized and aggregated
- **Secure Storage**: Industry-standard security practices
- **GDPR Compliance**: Full compliance with privacy regulations

### Mental Health Ethics
- **Professional Disclaimer**: Not a replacement for professional therapy
- **Crisis Protocols**: Clear escalation procedures for high-risk situations
- **Informed Consent**: Transparent data usage policies
- **Research Ethics**: Anonymized insights for mental health research

### Security Features
- **Rate Limiting**: Protection against abuse and spam
- **Input Validation**: Sanitization of all user inputs
- **Audit Logging**: Comprehensive security event logging
- **Access Controls**: Role-based permissions and authentication

## 🎯 Use Cases

### Individual Users
- **Emotional Support**: 24/7 availability for conversation and support
- **Mental Health Monitoring**: Track emotional patterns and well-being
- **Crisis Support**: Immediate intervention during difficult times
- **Personal Growth**: Develop emotional intelligence and coping skills

### Healthcare Professionals
- **Patient Monitoring**: Track patient emotional states between sessions
- **Crisis Alerts**: Immediate notification of high-risk situations
- **Research Insights**: Anonymized data for mental health research
- **Treatment Support**: Complement traditional therapy approaches

### Organizations
- **Employee Wellness**: Corporate mental health support programs
- **Educational Support**: Student emotional well-being monitoring
- **Community Health**: Scalable mental health resources
- **Research Platforms**: Population-level mental health insights

## 🚀 Deployment

### Local Development
```bash
# Quick start for development
python -m src.ai_companion.main
```

### Docker Deployment
```bash
# Build and run with Docker
docker build -t ai-companion .
docker run -p 7860:7860 -p 8000:8000 ai-companion
```

### Production Deployment
```bash
# Install production dependencies
pip install -r requirements.txt

# Set production environment
export ENVIRONMENT=production
export DEBUG_MODE=false

# Run with production settings
python -m src.ai_companion.main
```

### Cloud Platforms
- **Render**: One-click deployment from GitHub
- **Railway**: Automatic deployment with Redis
- **Heroku**: Scalable cloud deployment
- **Google Cloud Run**: Serverless container deployment

## 📊 Monitoring & Health

### Health Checks
```bash
# Check system health
curl http://localhost:8000/health

# Get performance metrics
curl http://localhost:8000/metrics
```

### Performance Monitoring
- **Response Time**: Average and 95th percentile tracking
- **Memory Usage**: Real-time memory consumption monitoring
- **Cache Performance**: Hit rates and efficiency metrics
- **Error Tracking**: Comprehensive error logging and alerting

### Logging
- **Application Logs**: `data/logs/ai_companion.log`
- **Performance Logs**: `data/logs/performance.log`
- **Security Logs**: `data/logs/security.log`
- **Error Logs**: Integrated with application logs

### Metrics Dashboard
Access real-time metrics at:
- **Health Status**: `/health`
- **Performance Metrics**: `/metrics`
- **System Info**: `/info`

## 🛠️ Development

### Project Structure
```
src/ai_companion/
├── core/                 # Core business logic
│   ├── conversation.py   # Main conversation service
│   ├── memory.py        # Memory management
│   ├── emotions.py      # Emotional intelligence
│   └── models.py        # Data models
├── services/            # External service integrations
│   ├── gemini.py       # Gemini API service
│   ├── storage.py      # Database/Redis
│   └── whatsapp.py     # WhatsApp integration
├── interfaces/          # User interfaces
│   ├── gradio_app.py   # Web interface
│   ├── api.py          # REST API
│   └── cli.py          # Command line
├── mental_health/       # Mental health features
│   ├── crisis_detection.py
│   ├── analytics.py
│   └── privacy.py
└── utils/              # Utilities
    ├── logging.py
    ├── monitoring.py
    └── helpers.py
```

### Development Setup
```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Run code formatting
black src/ tests/
isort src/ tests/

# Run linting
flake8 src/ tests/
mypy src/

# Run tests with coverage
pytest --cov=src/ai_companion
```

### Contributing Guidelines
1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Write** comprehensive tests for new functionality
4. **Ensure** all tests pass and code is properly formatted
5. **Update** documentation as needed
6. **Submit** a pull request with detailed description

### Code Quality Standards
- **Type Hints**: All functions should have proper type annotations
- **Documentation**: Docstrings for all public functions and classes
- **Testing**: Minimum 80% test coverage for new code
- **Formatting**: Use Black and isort for consistent code style
- **Linting**: Code must pass flake8 and mypy checks

## 📈 Roadmap

### Version 1.1 (Next Release)
- **Enhanced WhatsApp Integration**: Full production WhatsApp bot
- **Advanced Analytics Dashboard**: Real-time insights and reporting
- **Multi-language Support**: Support for Spanish, French, German
- **Voice Integration**: Speech-to-text and text-to-speech capabilities

### Version 1.2 (Future)
- **Mobile Applications**: Native iOS and Android apps
- **Professional Dashboard**: Tools for therapists and researchers
- **Group Therapy Features**: Multi-user therapeutic sessions
- **Advanced AI Models**: Integration with latest language models

### Long-term Vision
- **Healthcare Integration**: EHR and clinical system integrations
- **Research Platform**: Federated learning for privacy-preserving research
- **Global Deployment**: Multi-region, multi-language support
- **AI Advancement**: Cutting-edge emotional AI and therapeutic techniques

## 🆘 Crisis Support Resources

### Immediate Help
- **National Suicide Prevention Lifeline**: 988 (US)
- **Crisis Text Line**: Text HOME to 741741 (US)
- **International Association for Suicide Prevention**: https://www.iasp.info/resources/Crisis_Centres/
- **Emergency Services**: 911 (US), 999 (UK), 112 (EU)

### Important Disclaimer
This AI companion is designed to provide emotional support and early intervention, but it is **not a replacement** for professional mental health care. Always consult with qualified mental health professionals for serious concerns.

## 📞 Support & Community

### Getting Help
- **Documentation**: See the `docs/` directory for detailed guides
- **Issues**: Report bugs and request features on GitHub
- **Discussions**: Join community discussions for help and feedback

### Contributing
We welcome contributions! Please see our contributing guidelines above and feel free to:
- Report bugs and suggest features
- Submit pull requests for improvements
- Help with documentation and testing
- Share your use cases and feedback

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

### Privacy & Compliance
- **Data Minimization**: Only necessary data is collected
- **User Control**: Users own and control their data
- **Transparency**: Clear data usage and privacy policies
- **Compliance**: Designed with GDPR and privacy regulations in mind

---

## 🎉 Getting Started

Ready to deploy your AI Companion System? Follow the [Quick Start](#-quick-start) guide above, and you'll have a production-ready emotional AI system running in minutes.

For questions, issues, or contributions, please visit our [GitHub repository](https://github.com/ai-companion/ai-companion-system).

---

*Built with ❤️ for human emotional wellbeing and mental health support.*