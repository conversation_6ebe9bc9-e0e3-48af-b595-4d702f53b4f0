"""
Simple test script for the Next-Generation AI Companion System.
Quick verification that all components are working.
"""

import asyncio
import logging
import time
from datetime import datetime, timezone

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_basic_functionality():
    """Test basic functionality of the next-gen system."""
    print("🧪 Testing Next-Generation AI Companion System...")
    
    try:
        # Test imports
        print("📦 Testing imports...")
        from next_gen_conversation_service import NextGenConversationService
        from advanced_emotional_intelligence import AdvancedEmotionalIntelligence
        from memory_service import AdvancedMemoryService
        from gemini_service import GeminiService
        from models import EmotionType
        print("✅ All imports successful")
        
        # Initialize services
        print("🚀 Initializing services...")
        gemini_service = GeminiService()
        memory_service = AdvancedMemoryService()
        emotional_intelligence = AdvancedEmotionalIntelligence(gemini_service)
        conversation_service = NextGenConversationService()
        print("✅ Services initialized")
        
        # Test Gemini service
        print("🤖 Testing Gemini service...")
        test_response = await gemini_service.generate_response_async("Hello, this is a test.")
        assert test_response and len(test_response) > 0
        print(f"✅ Gemini response: {test_response[:50]}...")
        
        # Test memory service
        print("🧠 Testing memory service...")
        test_memory = await memory_service.create_episodic_memory(
            user_id="test_user",
            title="System Test",
            description="Testing the memory system functionality",
            emotional_context={'primary_emotion': EmotionType.NEUTRAL, 'intensity': 0.5}
        )
        assert test_memory is not None
        print(f"✅ Created episodic memory: {test_memory.title}")
        
        # Test emotional intelligence
        print("💝 Testing emotional intelligence...")
        emotional_insight = await emotional_intelligence.analyze_emotional_state(
            "test_user", "I'm feeling good today!", {}
        )
        assert emotional_insight is not None
        print(f"✅ Emotional analysis: {emotional_insight.primary_emotion.value} (intensity: {emotional_insight.emotional_intensity:.2f})")
        
        # Test conversation service
        print("💬 Testing conversation service...")
        start_time = time.time()
        response_data = await conversation_service.process_message(
            user_id="test_user",
            message="Hello! How are you today?",
            context={"test": True}
        )
        processing_time = time.time() - start_time
        
        assert "response" in response_data
        assert len(response_data["response"]) > 0
        print(f"✅ Conversation response ({processing_time:.2f}s): {response_data['response'][:50]}...")
        
        # Test performance
        print("⚡ Testing performance...")
        performance = conversation_service.get_system_performance()
        assert "active_conversations" in performance
        print(f"✅ Performance metrics: {performance['active_conversations']} active conversations")
        
        # Test memory insights
        print("📊 Testing memory insights...")
        insights = await memory_service.get_memory_insights("test_user")
        assert "total_episodes" in insights
        print(f"✅ Memory insights: {insights['total_episodes']} episodes")
        
        print("\n🎉 All basic tests passed! The Next-Generation AI Companion is working correctly!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_conversation_flow():
    """Test a complete conversation flow."""
    print("\n💬 Testing conversation flow...")
    
    try:
        from next_gen_conversation_service import NextGenConversationService
        conversation_service = NextGenConversationService()
        
        test_user = "flow_test_user"
        
        # Simulate a conversation
        messages = [
            "Hi there! I'm feeling a bit stressed about work today.",
            "I have a big presentation tomorrow and I'm worried I'll mess it up.",
            "Thank you for listening. That actually makes me feel better.",
            "Do you have any tips for managing presentation anxiety?"
        ]
        
        conversation_results = []
        
        for i, message in enumerate(messages):
            print(f"  User: {message}")
            
            start_time = time.time()
            response_data = await conversation_service.process_message(
                user_id=test_user,
                message=message,
                context={"conversation_turn": i + 1}
            )
            processing_time = time.time() - start_time
            
            ai_response = response_data.get("response", "No response")
            emotional_insight = response_data.get("emotional_insight", {})
            
            print(f"  AI: {ai_response}")
            print(f"  Emotion: {emotional_insight.get('primary_emotion', 'unknown')} (intensity: {emotional_insight.get('intensity', 0):.2f})")
            print(f"  Processing time: {processing_time:.2f}s")
            print()
            
            conversation_results.append({
                "turn": i + 1,
                "processing_time": processing_time,
                "emotion": emotional_insight.get('primary_emotion', 'unknown'),
                "response_length": len(ai_response)
            })
        
        # Get conversation insights
        insights = conversation_service.get_conversation_insights(test_user)
        print(f"Conversation insights: {insights['total_interactions']} interactions")
        print(f"Emotional patterns: {insights['emotional_patterns']}")
        print(f"Therapeutic alliance: {insights['therapeutic_alliance_strength']:.2f}")
        
        avg_time = sum(r["processing_time"] for r in conversation_results) / len(conversation_results)
        print(f"\n✅ Conversation flow test completed! Average response time: {avg_time:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Conversation flow test failed: {e}")
        return False

async def main():
    """Main test function."""
    print("🚀 Starting Next-Generation AI Companion Tests\n")
    
    # Run basic functionality tests
    basic_success = await test_basic_functionality()
    
    if basic_success:
        # Run conversation flow test
        flow_success = await test_conversation_flow()
        
        if flow_success:
            print("\n🎉 ALL TESTS PASSED! 🎉")
            print("The Next-Generation AI Companion is ready for use!")
            return 0
        else:
            print("\n❌ Conversation flow test failed")
            return 1
    else:
        print("\n❌ Basic functionality test failed")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
