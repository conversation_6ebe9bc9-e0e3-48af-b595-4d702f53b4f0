"""
High-Performance Memory Service for AI Companion System.
Optimized for speed and efficiency with advanced caching and async operations.
"""

import asyncio
import json
import hashlib
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple, Set
from collections import defaultdict, OrderedDict
from functools import lru_cache
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import redis.asyncio as aioredis
import threading
from concurrent.futures import ThreadPoolExecutor

from models import (
    MemoryEntry, PersonalMemory, UniversalMemory, MemoryType,
    InteractionType, EmotionType, UserProfile, ContextualMemory
)
from config import settings
from storage_service import PersistentStorageService

logger = logging.getLogger(__name__)

class HighPerformanceMemoryService:
    """Ultra-fast memory service with advanced caching and optimization."""
    
    def __init__(self, max_cache_size: int = 10000):
        """Initialize optimized memory service."""
        self.storage_service = PersistentStorageService()
        self.max_cache_size = max_cache_size
        
        # High-performance caches
        self.memory_cache = OrderedDict()  # LRU cache for memories
        self.embedding_cache = OrderedDict()  # LRU cache for embeddings
        self.user_profile_cache = OrderedDict()  # LRU cache for user profiles
        self.search_index = defaultdict(set)  # Inverted index for fast search
        
        # Async Redis client
        self.redis_client = None
        self._redis_pool = None
        
        # Thread pool for CPU-intensive operations
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        
        # Embedding model (loaded once)
        self._embedding_model = None
        self._model_lock = threading.Lock()
        
        # Performance metrics
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Initialize async components
        asyncio.create_task(self._initialize_async())
    
    async def _initialize_async(self):
        """Initialize async components."""
        try:
            self._redis_pool = aioredis.ConnectionPool.from_url(
                settings.redis_url, 
                max_connections=20,
                retry_on_timeout=True
            )
            self.redis_client = aioredis.Redis(connection_pool=self._redis_pool)
            await self.redis_client.ping()
            logger.info("Async Redis connection established")
        except Exception as e:
            logger.warning(f"Redis not available: {e}")
            self.redis_client = None
    
    @property
    def embedding_model(self):
        """Thread-safe lazy loading of embedding model."""
        if self._embedding_model is None:
            with self._model_lock:
                if self._embedding_model is None:
                    self._embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
        return self._embedding_model
    
    def _manage_cache_size(self, cache: OrderedDict):
        """Manage cache size using LRU eviction."""
        while len(cache) > self.max_cache_size:
            cache.popitem(last=False)  # Remove oldest item
    
    @lru_cache(maxsize=1000)
    def _get_embedding_sync(self, text: str) -> np.ndarray:
        """Synchronous embedding computation with caching."""
        return self.embedding_model.encode([text])[0]
    
    async def get_embedding(self, text: str) -> np.ndarray:
        """Get embedding with async optimization."""
        text_hash = hashlib.md5(text.encode()).hexdigest()
        
        # Check memory cache first
        if text_hash in self.embedding_cache:
            self.cache_hits += 1
            # Move to end (most recently used)
            embedding = self.embedding_cache.pop(text_hash)
            self.embedding_cache[text_hash] = embedding
            return embedding
        
        # Check Redis cache
        if self.redis_client:
            try:
                cached = await self.redis_client.get(f"embedding:{text_hash}")
                if cached:
                    self.cache_hits += 1
                    embedding = np.frombuffer(cached, dtype=np.float32)
                    self.embedding_cache[text_hash] = embedding
                    self._manage_cache_size(self.embedding_cache)
                    return embedding
            except Exception as e:
                logger.warning(f"Redis embedding cache error: {e}")
        
        # Compute embedding in thread pool
        self.cache_misses += 1
        loop = asyncio.get_event_loop()
        embedding = await loop.run_in_executor(
            self.thread_pool, 
            self._get_embedding_sync, 
            text
        )
        
        # Cache the result
        self.embedding_cache[text_hash] = embedding
        self._manage_cache_size(self.embedding_cache)
        
        # Cache in Redis asynchronously
        if self.redis_client:
            try:
                await self.redis_client.setex(
                    f"embedding:{text_hash}",
                    3600,  # 1 hour TTL
                    embedding.tobytes()
                )
            except Exception as e:
                logger.warning(f"Redis embedding cache write error: {e}")
        
        return embedding
    
    async def store_memory_fast(self, memory: MemoryEntry) -> bool:
        """Ultra-fast memory storage with async operations."""
        try:
            # Generate content hash for deduplication
            content_hash = memory.get_content_hash()
            
            # Check for duplicates in cache first
            cache_key = f"{memory.user_id}:{content_hash}"
            if cache_key in self.memory_cache:
                logger.debug(f"Duplicate memory detected: {cache_key}")
                return False
            
            # Store in memory cache
            self.memory_cache[memory.id] = memory
            self._manage_cache_size(self.memory_cache)
            
            # Update search index
            keywords = memory.get_search_keywords()
            for keyword in keywords:
                self.search_index[keyword].add(memory.id)
            
            # Async database storage
            asyncio.create_task(self._store_memory_db(memory))
            
            # Async Redis storage
            if self.redis_client:
                asyncio.create_task(self._store_memory_redis(memory))
            
            return True
            
        except Exception as e:
            logger.error(f"Error storing memory: {e}")
            return False
    
    async def _store_memory_db(self, memory: MemoryEntry):
        """Store memory in database asynchronously."""
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                self.thread_pool,
                self._store_memory_db_sync,
                memory
            )
        except Exception as e:
            logger.error(f"Database storage error: {e}")
    
    def _store_memory_db_sync(self, memory: MemoryEntry):
        """Synchronous database storage."""
        with self.storage_service.get_db_session() as db:
            # Convert to database model and store
            # Implementation depends on your ORM setup
            pass
    
    async def _store_memory_redis(self, memory: MemoryEntry):
        """Store memory in Redis asynchronously."""
        try:
            memory_data = memory.model_dump_json()
            await self.redis_client.setex(
                f"memory:{memory.id}",
                settings.memory_ttl,
                memory_data
            )
        except Exception as e:
            logger.error(f"Redis storage error: {e}")
    
    async def search_memories_fast(
        self, 
        user_id: str, 
        query: str, 
        limit: int = 10,
        memory_type: Optional[MemoryType] = None
    ) -> List[MemoryEntry]:
        """Ultra-fast memory search with multiple optimization strategies."""
        
        # Strategy 1: Keyword-based search (fastest)
        keyword_results = await self._search_by_keywords(user_id, query, limit, memory_type)
        
        if len(keyword_results) >= limit:
            return keyword_results[:limit]
        
        # Strategy 2: Semantic search for remaining slots
        remaining_limit = limit - len(keyword_results)
        semantic_results = await self._search_semantic(user_id, query, remaining_limit, memory_type)
        
        # Combine and deduplicate
        all_results = keyword_results + semantic_results
        seen_ids = set()
        unique_results = []
        
        for memory in all_results:
            if memory.id not in seen_ids:
                seen_ids.add(memory.id)
                unique_results.append(memory)
                if len(unique_results) >= limit:
                    break
        
        return unique_results
    
    async def _search_by_keywords(
        self, 
        user_id: str, 
        query: str, 
        limit: int,
        memory_type: Optional[MemoryType]
    ) -> List[MemoryEntry]:
        """Fast keyword-based search using inverted index."""
        query_keywords = [w.lower() for w in query.split() if len(w) > 3]
        
        # Find memory IDs that match keywords
        matching_ids = set()
        for keyword in query_keywords:
            if keyword in self.search_index:
                matching_ids.update(self.search_index[keyword])
        
        # Retrieve memories from cache
        results = []
        for memory_id in matching_ids:
            if memory_id in self.memory_cache:
                memory = self.memory_cache[memory_id]
                if (memory.user_id == user_id or memory.memory_type == MemoryType.UNIVERSAL):
                    if not memory_type or memory.memory_type == memory_type:
                        results.append(memory)
                        if len(results) >= limit:
                            break
        
        return results
    
    async def _search_semantic(
        self, 
        user_id: str, 
        query: str, 
        limit: int,
        memory_type: Optional[MemoryType]
    ) -> List[MemoryEntry]:
        """Semantic search using embeddings."""
        try:
            query_embedding = await self.get_embedding(query)
            
            # Get candidate memories (from cache first)
            candidates = []
            for memory in self.memory_cache.values():
                if (memory.user_id == user_id or memory.memory_type == MemoryType.UNIVERSAL):
                    if not memory_type or memory.memory_type == memory_type:
                        candidates.append(memory)
            
            if not candidates:
                return []
            
            # Compute similarities in thread pool
            loop = asyncio.get_event_loop()
            similarities = await loop.run_in_executor(
                self.thread_pool,
                self._compute_similarities,
                query_embedding,
                candidates
            )
            
            # Sort by similarity and return top results
            sorted_results = sorted(
                zip(candidates, similarities),
                key=lambda x: x[1],
                reverse=True
            )
            
            return [memory for memory, _ in sorted_results[:limit]]
            
        except Exception as e:
            logger.error(f"Semantic search error: {e}")
            return []
    
    def _compute_similarities(self, query_embedding: np.ndarray, memories: List[MemoryEntry]) -> List[float]:
        """Compute cosine similarities in batch."""
        # This would need memory embeddings to be pre-computed
        # For now, return dummy similarities
        return [0.5] * len(memories)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = self.cache_hits / total_requests if total_requests > 0 else 0
        
        return {
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "hit_rate": hit_rate,
            "memory_cache_size": len(self.memory_cache),
            "embedding_cache_size": len(self.embedding_cache),
            "search_index_size": len(self.search_index)
        }
