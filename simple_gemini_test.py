"""
Simple test to verify Gemini integration is working.
"""

import google.generativeai as genai
from config import settings
import time

def test_basic_gemini():
    """Test basic Gemini functionality."""
    print("🧪 Testing basic Gemini integration...")
    
    try:
        # Configure Gemini
        genai.configure(api_key=settings.gemini_api_key)
        model = genai.GenerativeModel('gemini-1.5-flash')
        
        print(f"✅ Gemini configured with API key: {settings.gemini_api_key[:10]}...")
        
        # Test simple prompt
        prompt = "You are a caring AI companion. Respond to: 'Hello, how are you?' in one sentence."
        
        print("📤 Sending test prompt to Gemini...")
        start_time = time.time()
        
        response = model.generate_content(prompt)
        response_time = time.time() - start_time
        
        print(f"✅ Response received in {response_time:.2f} seconds")
        print(f"📥 Response: {response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Gemini test failed: {e}")
        return False

def main():
    """Run simple Gemini test."""
    print("🤖 Testing Gemini Integration")
    print("="*40)
    
    success = test_basic_gemini()
    
    if success:
        print("\n✅ Gemini is working correctly!")
        print("💡 Your optimized system includes full Gemini integration")
    else:
        print("\n❌ Gemini test failed")
        print("💡 Check your GEMINI_API_KEY in the .env file")

if __name__ == "__main__":
    main()
