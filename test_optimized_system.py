"""
Quick test script for the optimized AI Companion System.
Verifies that all optimizations work correctly.
"""

import asyncio
import time
import logging
from typing import Dict, Any

# Import optimized services
from optimized_conversation_service import OptimizedConversationService
from optimized_memory_service import HighPerformanceMemoryService
from models import MemoryEntry, MemoryType, InteractionType, EmotionType

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OptimizedSystemTest:
    """Test suite for optimized AI companion system."""
    
    def __init__(self):
        """Initialize test suite."""
        self.conversation_service = None
        self.memory_service = None
        self.test_results = {}
    
    async def setup_services(self):
        """Setup optimized services."""
        logger.info("🔧 Setting up optimized services...")
        
        self.memory_service = HighPerformanceMemoryService()
        self.conversation_service = OptimizedConversationService()
        
        # Allow time for async initialization
        await asyncio.sleep(0.5)
        
        logger.info("✅ Services ready")
    
    async def test_memory_service_performance(self) -> Dict[str, Any]:
        """Test memory service performance."""
        logger.info("🧠 Testing memory service performance...")
        
        # Test memory storage
        test_memories = []
        storage_times = []
        
        for i in range(10):
            memory = MemoryEntry(
                id=f"test_memory_{i}",
                user_id="test_user",
                memory_type=MemoryType.PERSONAL,
                interaction_type=InteractionType.CONVERSATION,
                content=f"This is test memory content number {i}. It contains some meaningful information about the user's preferences and experiences.",
                emotion=EmotionType.JOY if i % 2 == 0 else EmotionType.NEUTRAL
            )
            
            start_time = time.time()
            success = await self.memory_service.store_memory_fast(memory)
            end_time = time.time()
            
            storage_times.append(end_time - start_time)
            test_memories.append(memory)
            
            if not success:
                logger.warning(f"Failed to store memory {i}")
        
        # Test memory search
        search_times = []
        search_queries = ["test", "meaningful", "preferences", "experiences", "user"]
        
        for query in search_queries:
            start_time = time.time()
            results = await self.memory_service.search_memories_fast("test_user", query, limit=5)
            end_time = time.time()
            
            search_times.append(end_time - start_time)
            logger.info(f"Search for '{query}' returned {len(results)} results")
        
        # Get cache statistics
        cache_stats = self.memory_service.get_cache_stats()
        
        return {
            "storage_times": storage_times,
            "search_times": search_times,
            "avg_storage_time": sum(storage_times) / len(storage_times),
            "avg_search_time": sum(search_times) / len(search_times),
            "cache_stats": cache_stats
        }
    
    async def test_conversation_service_performance(self) -> Dict[str, Any]:
        """Test conversation service performance."""
        logger.info("💬 Testing conversation service performance...")
        
        # Start a conversation
        start_time = time.time()
        conversation_id = await self.conversation_service.start_conversation_fast("test_user", "Test User")
        conversation_start_time = time.time() - start_time
        
        logger.info(f"Conversation started in {conversation_start_time:.3f}s")
        
        # Test message processing
        test_messages = [
            "Hello, how are you today?",
            "I'm feeling a bit anxious about my upcoming presentation.",
            "Can you help me feel more confident?",
            "Thank you for your support!",
            "I'm excited about my new project at work."
        ]
        
        response_times = []
        responses = []
        
        for i, message in enumerate(test_messages):
            start_time = time.time()
            result = await self.conversation_service.process_message_fast(conversation_id, message)
            end_time = time.time()
            
            response_time = end_time - start_time
            response_times.append(response_time)
            responses.append(result)
            
            logger.info(f"Message {i+1}: {response_time:.3f}s - {result.get('emotion', 'Unknown')} - Cached: {result.get('cached', False)}")
        
        # Test caching by repeating a message
        cached_start_time = time.time()
        cached_result = await self.conversation_service.process_message_fast(conversation_id, test_messages[0])
        cached_response_time = time.time() - cached_start_time
        
        # Get performance statistics
        performance_stats = self.conversation_service.get_performance_stats()
        
        return {
            "conversation_start_time": conversation_start_time,
            "response_times": response_times,
            "avg_response_time": sum(response_times) / len(response_times),
            "cached_response_time": cached_response_time,
            "responses": responses,
            "performance_stats": performance_stats
        }
    
    async def test_concurrent_conversations(self, num_users: int = 5) -> Dict[str, Any]:
        """Test concurrent conversation handling."""
        logger.info(f"⚡ Testing concurrent conversations with {num_users} users...")
        
        async def simulate_user_conversation(user_id: str) -> Dict[str, Any]:
            """Simulate a single user conversation."""
            # Start conversation
            conv_id = await self.conversation_service.start_conversation_fast(user_id, f"User {user_id}")
            
            # Send messages
            messages = [
                f"Hello, I'm {user_id}",
                "How are you today?",
                "I'm feeling great!"
            ]
            
            times = []
            for message in messages:
                start_time = time.time()
                result = await self.conversation_service.process_message_fast(conv_id, message)
                end_time = time.time()
                times.append(end_time - start_time)
            
            return {
                "user_id": user_id,
                "response_times": times,
                "avg_time": sum(times) / len(times)
            }
        
        # Run concurrent conversations
        start_time = time.time()
        tasks = [simulate_user_conversation(f"user_{i}") for i in range(num_users)]
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        # Calculate statistics
        all_times = []
        for result in results:
            all_times.extend(result["response_times"])
        
        return {
            "total_time": total_time,
            "num_users": num_users,
            "total_messages": len(all_times),
            "messages_per_second": len(all_times) / total_time,
            "avg_response_time": sum(all_times) / len(all_times),
            "user_results": results
        }
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all performance tests."""
        logger.info("🏁 Starting optimized system tests...")
        
        # Setup services
        await self.setup_services()
        
        # Run tests
        results = {}
        
        try:
            # Test memory service
            results["memory"] = await self.test_memory_service_performance()
            
            # Test conversation service
            results["conversation"] = await self.test_conversation_service_performance()
            
            # Test concurrent handling
            results["concurrent"] = await self.test_concurrent_conversations(3)
            
            logger.info("✅ All tests completed successfully!")
            
        except Exception as e:
            logger.error(f"❌ Test failed: {e}")
            results["error"] = str(e)
        
        return results
    
    def print_test_summary(self, results: Dict[str, Any]):
        """Print a summary of test results."""
        print("\n" + "="*60)
        print("🏆 OPTIMIZED SYSTEM TEST RESULTS")
        print("="*60)
        
        if "memory" in results:
            memory = results["memory"]
            print(f"Memory Service:")
            print(f"  - Avg Storage Time: {memory['avg_storage_time']:.4f}s")
            print(f"  - Avg Search Time: {memory['avg_search_time']:.4f}s")
            print(f"  - Cache Hit Rate: {memory['cache_stats'].get('hit_rate', 0):.2%}")
        
        if "conversation" in results:
            conv = results["conversation"]
            print(f"Conversation Service:")
            print(f"  - Conversation Start: {conv['conversation_start_time']:.4f}s")
            print(f"  - Avg Response Time: {conv['avg_response_time']:.4f}s")
            print(f"  - Cached Response Time: {conv['cached_response_time']:.4f}s")
            print(f"  - Cache Hit Rate: {conv['performance_stats'].get('cache_hit_rate', 0):.2%}")
        
        if "concurrent" in results:
            conc = results["concurrent"]
            print(f"Concurrent Performance:")
            print(f"  - Users: {conc['num_users']}")
            print(f"  - Messages/Second: {conc['messages_per_second']:.1f}")
            print(f"  - Avg Response Time: {conc['avg_response_time']:.4f}s")
        
        if "error" in results:
            print(f"❌ Error: {results['error']}")
        else:
            print("\n✅ All systems operating at optimal performance!")

async def main():
    """Run the optimized system test."""
    test_suite = OptimizedSystemTest()
    results = await test_suite.run_all_tests()
    test_suite.print_test_summary(results)

if __name__ == "__main__":
    asyncio.run(main())
