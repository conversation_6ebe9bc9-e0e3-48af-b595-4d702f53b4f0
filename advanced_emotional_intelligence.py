"""
Advanced Emotional Intelligence Service for AI Companion System.
Implements state-of-the-art psychological models, trauma-aware responses,
and proactive mental health monitoring based on latest research.
"""

import json
import re
import asyncio
import logging
import numpy as np
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque

from models import (
    EmotionType, InteractionType, EmotionalState, EmpathyModel, 
    EmotionalMemory, MemoryEntry, utc_now
)
from gemini_service import GeminiService
from config import settings

logger = logging.getLogger(__name__)

class PsychologicalModel(Enum):
    """Advanced psychological models for emotional understanding."""
    COGNITIVE_BEHAVIORAL = "cognitive_behavioral"  # CBT-based analysis
    ATTACHMENT_THEORY = "attachment_theory"        # Attachment patterns
    TRAUMA_INFORMED = "trauma_informed"            # Trauma-aware responses
    POSITIVE_PSYCHOLOGY = "positive_psychology"    # Strengths-based approach
    DIALECTICAL_BEHAVIORAL = "dialectical_behavioral"  # DBT techniques

class MentalHealthRisk(Enum):
    """Mental health risk assessment levels."""
    MINIMAL = 1
    LOW = 2
    MODERATE = 3
    HIGH = 4
    CRITICAL = 5

class TherapeuticTechnique(Enum):
    """Evidence-based therapeutic techniques."""
    ACTIVE_LISTENING = "active_listening"
    COGNITIVE_REFRAMING = "cognitive_reframing"
    MINDFULNESS = "mindfulness"
    VALIDATION = "validation"
    GROUNDING = "grounding"
    BREATHING_EXERCISES = "breathing_exercises"
    PROGRESSIVE_MUSCLE_RELAXATION = "progressive_muscle_relaxation"

@dataclass
class PsychologicalProfile:
    """Comprehensive psychological profile of a user."""
    user_id: str
    attachment_style: Optional[str] = None
    coping_mechanisms: List[str] = field(default_factory=list)
    stress_triggers: List[str] = field(default_factory=list)
    emotional_regulation_patterns: Dict[str, float] = field(default_factory=dict)
    resilience_factors: List[str] = field(default_factory=list)
    support_preferences: List[str] = field(default_factory=list)
    trauma_indicators: List[str] = field(default_factory=list)
    therapeutic_alliance_strength: float = 0.5
    progress_markers: Dict[str, Any] = field(default_factory=dict)
    last_updated: datetime = field(default_factory=lambda: datetime.now(timezone.utc))

@dataclass
class EmotionalInsight:
    """Deep emotional insight from advanced analysis."""
    primary_emotion: EmotionType
    secondary_emotions: List[EmotionType]
    emotional_intensity: float
    emotional_complexity: float  # How many emotions are present
    emotional_stability: float   # How stable the emotional state is
    underlying_needs: List[str]
    cognitive_patterns: List[str]
    behavioral_indicators: List[str]
    risk_assessment: MentalHealthRisk
    recommended_techniques: List[TherapeuticTechnique]
    confidence_score: float

@dataclass
class TherapeuticResponse:
    """Structured therapeutic response."""
    response_text: str
    therapeutic_technique: TherapeuticTechnique
    psychological_model: PsychologicalModel
    empathy_level: float
    validation_elements: List[str]
    growth_opportunities: List[str]
    follow_up_suggestions: List[str]

class AdvancedEmotionalIntelligence:
    """
    Advanced emotional intelligence service implementing cutting-edge
    psychological models and therapeutic techniques.
    """
    
    def __init__(self, gemini_service: GeminiService):
        """Initialize the advanced emotional intelligence service."""
        self.gemini_service = gemini_service
        
        # User psychological profiles
        self.psychological_profiles: Dict[str, PsychologicalProfile] = {}
        
        # Emotional pattern tracking
        self.emotional_histories: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.crisis_indicators: Dict[str, List[str]] = defaultdict(list)
        
        # Therapeutic relationship tracking
        self.therapeutic_alliance: Dict[str, float] = defaultdict(lambda: 0.5)
        self.intervention_history: Dict[str, List[Dict]] = defaultdict(list)
        
        # Advanced prompts for psychological analysis
        self.psychological_analysis_prompt = self._create_psychological_analysis_prompt()
        self.trauma_aware_prompt = self._create_trauma_aware_prompt()
        self.therapeutic_response_prompt = self._create_therapeutic_response_prompt()
        
        # Crisis detection patterns
        self.crisis_patterns = self._initialize_crisis_patterns()
        
        # Therapeutic techniques database
        self.therapeutic_techniques = self._initialize_therapeutic_techniques()
    
    def _create_psychological_analysis_prompt(self) -> str:
        """Create prompt for deep psychological analysis."""
        return """
        As an expert clinical psychologist with expertise in multiple therapeutic modalities,
        analyze the following text for deep emotional and psychological insights:

        Text: "{text}"
        Context: {context}
        User History: {user_history}

        Provide a comprehensive psychological analysis including:

        1. EMOTIONAL ANALYSIS:
        - Primary emotion and intensity (0.0-1.0)
        - Secondary emotions present
        - Emotional complexity and stability
        - Emotional regulation patterns

        2. COGNITIVE PATTERNS:
        - Thought patterns (rational, catastrophic, ruminating, etc.)
        - Cognitive distortions if present
        - Problem-solving approach
        - Self-talk patterns

        3. BEHAVIORAL INDICATORS:
        - Coping mechanisms being used
        - Behavioral patterns
        - Social engagement level
        - Activity patterns

        4. UNDERLYING NEEDS:
        - Core psychological needs (autonomy, competence, relatedness)
        - Safety and security needs
        - Validation and understanding needs
        - Growth and meaning needs

        5. RISK ASSESSMENT:
        - Mental health risk level (1-5 scale)
        - Specific risk factors
        - Protective factors
        - Crisis indicators

        6. THERAPEUTIC RECOMMENDATIONS:
        - Most appropriate therapeutic approach
        - Specific techniques that would help
        - Immediate support needs
        - Long-term therapeutic goals

        Format your response as a structured JSON with clear categories.
        Be empathetic, non-judgmental, and focus on strengths alongside challenges.
        """
    
    def _create_trauma_aware_prompt(self) -> str:
        """Create trauma-informed response prompt."""
        return """
        As a trauma-informed therapist, respond to this person with deep empathy and safety:

        Message: "{message}"
        Emotional State: {emotional_state}
        Trauma Indicators: {trauma_indicators}
        Safety Level: {safety_level}

        TRAUMA-INFORMED PRINCIPLES:
        1. Safety first - ensure emotional and psychological safety
        2. Trustworthiness and transparency
        3. Peer support and mutual self-help
        4. Collaboration and mutuality
        5. Empowerment and choice
        6. Cultural, historical, and gender considerations

        Create a response that:
        - Validates their experience without judgment
        - Emphasizes their strength and resilience
        - Offers choice and control
        - Avoids re-traumatization
        - Provides grounding if needed
        - Suggests appropriate coping strategies
        - Maintains hope and possibility

        Keep the response warm, genuine, and professionally supportive.
        """

    def _create_therapeutic_response_prompt(self) -> str:
        """Create prompt for therapeutic response generation."""
        return """
        As an experienced therapist using {therapeutic_model}, create a therapeutic response:

        Client Message: "{message}"
        Emotional Analysis: {emotional_analysis}
        Therapeutic Goals: {therapeutic_goals}
        Session Context: {session_context}

        Use {therapeutic_technique} to create a response that:
        1. Demonstrates deep empathy and understanding
        2. Validates the client's experience
        3. Gently challenges unhelpful patterns if appropriate
        4. Offers practical coping strategies
        5. Encourages growth and self-discovery
        6. Maintains therapeutic boundaries
        7. Builds hope and resilience

        Make the response feel natural, warm, and professionally supportive.
        Avoid being overly clinical or using too much therapeutic jargon.
        """

    def _initialize_crisis_patterns(self) -> Dict[str, List[str]]:
        """Initialize crisis detection patterns."""
        return {
            "suicidal_ideation": [
                r"(?:want to|going to|thinking about).{0,20}(?:kill myself|end it all|not be here)",
                r"(?:life isn't worth|no point in living|better off dead)",
                r"(?:suicide|suicidal thoughts|ending my life)",
                r"(?:can't go on|can't take it anymore|want to disappear)"
            ],
            "self_harm": [
                r"(?:cut myself|hurt myself|self harm|self-harm)",
                r"(?:cutting|burning|hitting myself)",
                r"(?:deserve pain|need to hurt|punish myself)"
            ],
            "severe_depression": [
                r"(?:completely hopeless|no hope left|nothing matters)",
                r"(?:can't get out of bed|can't function|completely numb)",
                r"(?:worthless|useless|burden to everyone)"
            ],
            "panic_crisis": [
                r"(?:can't breathe|heart racing|going to die)",
                r"(?:panic attack|losing control|going crazy)",
                r"(?:everything is spinning|can't think straight)"
            ],
            "substance_abuse": [
                r"(?:drinking too much|using drugs|can't stop drinking)",
                r"(?:need alcohol|need drugs|addicted to)",
                r"(?:overdose|too many pills|mixing substances)"
            ]
        }

    def _initialize_therapeutic_techniques(self) -> Dict[TherapeuticTechnique, Dict[str, Any]]:
        """Initialize therapeutic techniques database."""
        return {
            TherapeuticTechnique.ACTIVE_LISTENING: {
                "description": "Fully focusing on and understanding the client",
                "phrases": [
                    "I hear that you're feeling...",
                    "It sounds like...",
                    "What I'm understanding is...",
                    "Help me understand..."
                ],
                "when_to_use": ["initial_contact", "emotional_expression", "confusion"]
            },
            TherapeuticTechnique.COGNITIVE_REFRAMING: {
                "description": "Helping client see situations from different perspectives",
                "phrases": [
                    "Another way to look at this might be...",
                    "What evidence supports/challenges this thought?",
                    "How might someone else see this situation?",
                    "What would you tell a friend in this situation?"
                ],
                "when_to_use": ["negative_thinking", "catastrophizing", "self_criticism"]
            },
            TherapeuticTechnique.VALIDATION: {
                "description": "Acknowledging and accepting the client's experience",
                "phrases": [
                    "Your feelings make complete sense given...",
                    "Anyone in your situation would feel...",
                    "It's completely understandable that...",
                    "Your reaction is valid and normal..."
                ],
                "when_to_use": ["emotional_distress", "self_doubt", "trauma_response"]
            },
            TherapeuticTechnique.GROUNDING: {
                "description": "Helping client stay present and connected to reality",
                "phrases": [
                    "Let's focus on what you can see/hear/feel right now...",
                    "Take a moment to notice your feet on the ground...",
                    "Can you name 5 things you can see around you?",
                    "Let's bring your attention back to this moment..."
                ],
                "when_to_use": ["anxiety", "panic", "dissociation", "trauma_response"]
            },
            TherapeuticTechnique.MINDFULNESS: {
                "description": "Encouraging present-moment awareness",
                "phrases": [
                    "Notice what you're experiencing right now...",
                    "Let's observe these thoughts without judgment...",
                    "What do you notice in your body?",
                    "Can you sit with this feeling for a moment?"
                ],
                "when_to_use": ["rumination", "anxiety", "emotional_overwhelm"]
            }
        }

    async def analyze_emotional_state(
        self,
        user_id: str,
        text: str,
        context: Optional[Dict[str, Any]] = None
    ) -> EmotionalInsight:
        """Perform advanced emotional analysis using psychological models."""
        try:
            # Get user's psychological profile
            profile = self.psychological_profiles.get(user_id)

            # Get emotional history for context
            history = list(self.emotional_histories[user_id])[-5:]
            history_text = "\n".join([f"- {h}" for h in history]) if history else "No previous history"

            # Prepare context
            context_str = json.dumps(context or {}, indent=2, default=str)

            # Generate psychological analysis
            prompt = self.psychological_analysis_prompt.format(
                text=text,
                context=context_str,
                user_history=history_text
            )

            response = await self.gemini_service.generate_response_async(prompt)

            # Parse the response
            analysis = self._parse_psychological_analysis(response)

            # Create emotional insight
            insight = EmotionalInsight(
                primary_emotion=analysis.get('primary_emotion', EmotionType.NEUTRAL),
                secondary_emotions=analysis.get('secondary_emotions', []),
                emotional_intensity=analysis.get('emotional_intensity', 0.5),
                emotional_complexity=analysis.get('emotional_complexity', 0.5),
                emotional_stability=analysis.get('emotional_stability', 0.5),
                underlying_needs=analysis.get('underlying_needs', []),
                cognitive_patterns=analysis.get('cognitive_patterns', []),
                behavioral_indicators=analysis.get('behavioral_indicators', []),
                risk_assessment=analysis.get('risk_assessment', MentalHealthRisk.LOW),
                recommended_techniques=analysis.get('recommended_techniques', []),
                confidence_score=analysis.get('confidence_score', 0.7)
            )

            # Update emotional history
            self.emotional_histories[user_id].append(f"{datetime.now().strftime('%Y-%m-%d %H:%M')}: {text[:100]}...")

            # Check for crisis indicators
            await self._check_crisis_indicators(user_id, text, insight)

            # Update psychological profile
            await self._update_psychological_profile(user_id, insight)

            return insight

        except Exception as e:
            logger.error(f"Error in emotional analysis: {e}")
            # Return basic fallback insight
            return EmotionalInsight(
                primary_emotion=EmotionType.NEUTRAL,
                secondary_emotions=[],
                emotional_intensity=0.5,
                emotional_complexity=0.3,
                emotional_stability=0.5,
                underlying_needs=["understanding", "support"],
                cognitive_patterns=["seeking_help"],
                behavioral_indicators=["reaching_out"],
                risk_assessment=MentalHealthRisk.LOW,
                recommended_techniques=[TherapeuticTechnique.ACTIVE_LISTENING],
                confidence_score=0.3
            )

    def _parse_psychological_analysis(self, response: str) -> Dict[str, Any]:
        """Parse the psychological analysis response from Gemini."""
        try:
            # Try to extract JSON from the response
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                analysis = json.loads(json_match.group())
                return self._normalize_analysis(analysis)
        except:
            pass

        # Fallback: parse text response
        return self._parse_text_analysis(response)

    def _normalize_analysis(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize the analysis to expected format."""
        normalized = {}

        # Extract primary emotion
        emotion_text = analysis.get('primary_emotion', 'neutral').lower()
        normalized['primary_emotion'] = self._text_to_emotion(emotion_text)

        # Extract emotional intensity
        normalized['emotional_intensity'] = float(analysis.get('emotional_intensity', 0.5))

        # Extract other fields with defaults
        normalized['secondary_emotions'] = [
            self._text_to_emotion(e) for e in analysis.get('secondary_emotions', [])
        ]
        normalized['emotional_complexity'] = float(analysis.get('emotional_complexity', 0.5))
        normalized['emotional_stability'] = float(analysis.get('emotional_stability', 0.5))
        normalized['underlying_needs'] = analysis.get('underlying_needs', [])
        normalized['cognitive_patterns'] = analysis.get('cognitive_patterns', [])
        normalized['behavioral_indicators'] = analysis.get('behavioral_indicators', [])

        # Extract risk assessment
        risk_level = analysis.get('risk_level', 2)
        normalized['risk_assessment'] = MentalHealthRisk(min(max(int(risk_level), 1), 5))

        # Extract recommended techniques
        techniques = analysis.get('recommended_techniques', ['active_listening'])
        normalized['recommended_techniques'] = [
            self._text_to_technique(t) for t in techniques
        ]

        normalized['confidence_score'] = float(analysis.get('confidence_score', 0.7))

        return normalized

    def _text_to_emotion(self, text: str) -> EmotionType:
        """Convert text to EmotionType."""
        emotion_map = {
            'happy': EmotionType.JOY,
            'joy': EmotionType.JOY,
            'excited': EmotionType.JOY,
            'sad': EmotionType.SADNESS,
            'sadness': EmotionType.SADNESS,
            'depressed': EmotionType.SADNESS,
            'angry': EmotionType.ANGER,
            'anger': EmotionType.ANGER,
            'frustrated': EmotionType.ANGER,
            'fear': EmotionType.FEAR,
            'afraid': EmotionType.FEAR,
            'anxious': EmotionType.FEAR,
            'worried': EmotionType.FEAR,
            'surprise': EmotionType.SURPRISE,
            'surprised': EmotionType.SURPRISE,
            'disgust': EmotionType.DISGUST,
            'disgusted': EmotionType.DISGUST,
            'neutral': EmotionType.NEUTRAL,
            'calm': EmotionType.NEUTRAL
        }
        return emotion_map.get(text.lower(), EmotionType.NEUTRAL)

    def _text_to_technique(self, text: str) -> TherapeuticTechnique:
        """Convert text to TherapeuticTechnique."""
        technique_map = {
            'active_listening': TherapeuticTechnique.ACTIVE_LISTENING,
            'listening': TherapeuticTechnique.ACTIVE_LISTENING,
            'cognitive_reframing': TherapeuticTechnique.COGNITIVE_REFRAMING,
            'reframing': TherapeuticTechnique.COGNITIVE_REFRAMING,
            'validation': TherapeuticTechnique.VALIDATION,
            'validate': TherapeuticTechnique.VALIDATION,
            'grounding': TherapeuticTechnique.GROUNDING,
            'ground': TherapeuticTechnique.GROUNDING,
            'mindfulness': TherapeuticTechnique.MINDFULNESS,
            'mindful': TherapeuticTechnique.MINDFULNESS,
            'breathing': TherapeuticTechnique.BREATHING_EXERCISES,
            'relaxation': TherapeuticTechnique.PROGRESSIVE_MUSCLE_RELAXATION
        }
        return technique_map.get(text.lower(), TherapeuticTechnique.ACTIVE_LISTENING)

    def _parse_text_analysis(self, response: str) -> Dict[str, Any]:
        """Parse text-based analysis response."""
        # Simple text parsing fallback
        analysis = {
            'primary_emotion': EmotionType.NEUTRAL,
            'emotional_intensity': 0.5,
            'secondary_emotions': [],
            'emotional_complexity': 0.5,
            'emotional_stability': 0.5,
            'underlying_needs': ['understanding', 'support'],
            'cognitive_patterns': ['seeking_help'],
            'behavioral_indicators': ['reaching_out'],
            'risk_assessment': MentalHealthRisk.LOW,
            'recommended_techniques': [TherapeuticTechnique.ACTIVE_LISTENING],
            'confidence_score': 0.5
        }

        # Extract emotions from text
        emotion_keywords = {
            'sad': EmotionType.SADNESS,
            'happy': EmotionType.JOY,
            'angry': EmotionType.ANGER,
            'afraid': EmotionType.FEAR,
            'anxious': EmotionType.FEAR,
            'worried': EmotionType.FEAR
        }

        response_lower = response.lower()
        for keyword, emotion in emotion_keywords.items():
            if keyword in response_lower:
                analysis['primary_emotion'] = emotion
                break

        return analysis

    async def _check_crisis_indicators(self, user_id: str, text: str, insight: EmotionalInsight):
        """Check for crisis indicators and take appropriate action."""
        crisis_detected = False
        crisis_types = []

        text_lower = text.lower()

        # Check each crisis pattern
        for crisis_type, patterns in self.crisis_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    crisis_detected = True
                    crisis_types.append(crisis_type)
                    break

        # Also check risk assessment from insight
        if insight.risk_assessment in [MentalHealthRisk.HIGH, MentalHealthRisk.CRITICAL]:
            crisis_detected = True
            crisis_types.append("high_risk_assessment")

        if crisis_detected:
            await self._handle_crisis(user_id, crisis_types, text, insight)

    async def _handle_crisis(
        self,
        user_id: str,
        crisis_types: List[str],
        original_text: str,
        insight: EmotionalInsight
    ):
        """Handle detected crisis situation."""
        logger.warning(f"Crisis detected for user {user_id}: {crisis_types}")

        # Store crisis information
        crisis_info = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'crisis_types': crisis_types,
            'original_text': original_text,
            'risk_level': insight.risk_assessment.value,
            'action_taken': 'crisis_protocol_initiated'
        }

        self.crisis_indicators[user_id].append(crisis_info)

        # Update psychological profile with crisis marker
        profile = self.psychological_profiles.get(user_id)
        if profile:
            profile.trauma_indicators.extend(crisis_types)
            profile.last_updated = datetime.now(timezone.utc)

        # Log for monitoring (in production, this would trigger alerts)
        logger.critical(f"CRISIS ALERT - User: {user_id}, Types: {crisis_types}, Risk: {insight.risk_assessment.value}")

    async def _update_psychological_profile(self, user_id: str, insight: EmotionalInsight):
        """Update the user's psychological profile based on new insights."""
        if user_id not in self.psychological_profiles:
            self.psychological_profiles[user_id] = PsychologicalProfile(user_id=user_id)

        profile = self.psychological_profiles[user_id]

        # Update emotional regulation patterns
        emotion_key = insight.primary_emotion.value
        current_stability = profile.emotional_regulation_patterns.get(emotion_key, 0.5)
        profile.emotional_regulation_patterns[emotion_key] = (
            current_stability * 0.8 + insight.emotional_stability * 0.2
        )

        # Update coping mechanisms
        for pattern in insight.cognitive_patterns:
            if pattern not in profile.coping_mechanisms:
                profile.coping_mechanisms.append(pattern)

        # Update support preferences based on recommended techniques
        for technique in insight.recommended_techniques:
            technique_name = technique.value
            if technique_name not in profile.support_preferences:
                profile.support_preferences.append(technique_name)

        # Update last updated timestamp
        profile.last_updated = datetime.now(timezone.utc)

        logger.info(f"Updated psychological profile for user {user_id}")

    async def generate_therapeutic_response(
        self,
        user_id: str,
        message: str,
        insight: EmotionalInsight,
        context: Optional[Dict[str, Any]] = None
    ) -> TherapeuticResponse:
        """Generate a therapeutic response based on emotional insight."""
        try:
            # Select appropriate therapeutic model and technique
            therapeutic_model = self._select_therapeutic_model(insight)
            therapeutic_technique = self._select_therapeutic_technique(insight)

            # Get user's psychological profile
            profile = self.psychological_profiles.get(user_id)

            # Prepare therapeutic goals
            therapeutic_goals = self._determine_therapeutic_goals(insight, profile)

            # Check if trauma-aware response is needed
            if self._needs_trauma_aware_response(insight, profile):
                response = await self._generate_trauma_aware_response(
                    message, insight, profile
                )
            else:
                response = await self._generate_standard_therapeutic_response(
                    message, insight, therapeutic_model, therapeutic_technique, therapeutic_goals
                )

            return TherapeuticResponse(
                response_text=response,
                therapeutic_technique=therapeutic_technique,
                psychological_model=therapeutic_model,
                empathy_level=self._calculate_empathy_level(insight),
                validation_elements=self._extract_validation_elements(insight),
                growth_opportunities=self._identify_growth_opportunities(insight),
                follow_up_suggestions=self._generate_follow_up_suggestions(insight)
            )

        except Exception as e:
            logger.error(f"Error generating therapeutic response: {e}")
            # Fallback response
            return TherapeuticResponse(
                response_text="I hear that you're going through something difficult right now. I'm here to listen and support you. Would you like to share more about what's on your mind?",
                therapeutic_technique=TherapeuticTechnique.ACTIVE_LISTENING,
                psychological_model=PsychologicalModel.COGNITIVE_BEHAVIORAL,
                empathy_level=0.8,
                validation_elements=["acknowledging_difficulty"],
                growth_opportunities=["emotional_expression"],
                follow_up_suggestions=["encourage_sharing"]
            )

    def _select_therapeutic_model(self, insight: EmotionalInsight) -> PsychologicalModel:
        """Select the most appropriate therapeutic model."""
        if insight.risk_assessment in [MentalHealthRisk.HIGH, MentalHealthRisk.CRITICAL]:
            return PsychologicalModel.TRAUMA_INFORMED
        elif "catastrophic" in insight.cognitive_patterns:
            return PsychologicalModel.COGNITIVE_BEHAVIORAL
        elif "attachment" in insight.underlying_needs:
            return PsychologicalModel.ATTACHMENT_THEORY
        elif insight.emotional_intensity > 0.8:
            return PsychologicalModel.DIALECTICAL_BEHAVIORAL
        else:
            return PsychologicalModel.POSITIVE_PSYCHOLOGY

    def _select_therapeutic_technique(self, insight: EmotionalInsight) -> TherapeuticTechnique:
        """Select the most appropriate therapeutic technique."""
        if insight.risk_assessment.value >= MentalHealthRisk.HIGH.value:
            return TherapeuticTechnique.GROUNDING
        elif insight.emotional_intensity > 0.8:
            return TherapeuticTechnique.VALIDATION
        elif "rumination" in insight.cognitive_patterns:
            return TherapeuticTechnique.MINDFULNESS
        elif "negative_thinking" in insight.cognitive_patterns:
            return TherapeuticTechnique.COGNITIVE_REFRAMING
        else:
            return TherapeuticTechnique.ACTIVE_LISTENING

    def _determine_therapeutic_goals(
        self,
        insight: EmotionalInsight,
        profile: Optional[PsychologicalProfile]
    ) -> List[str]:
        """Determine therapeutic goals for the session."""
        goals = []

        # Based on emotional state
        if insight.emotional_intensity > 0.7:
            goals.append("emotional_regulation")
        if insight.emotional_stability < 0.4:
            goals.append("emotional_stabilization")

        # Based on cognitive patterns
        if "catastrophic" in insight.cognitive_patterns:
            goals.append("cognitive_restructuring")
        if "rumination" in insight.cognitive_patterns:
            goals.append("present_moment_awareness")

        # Based on underlying needs
        if "safety" in insight.underlying_needs:
            goals.append("safety_establishment")
        if "validation" in insight.underlying_needs:
            goals.append("validation_and_acceptance")

        # Default goal
        if not goals:
            goals.append("emotional_support")

        return goals

    def _needs_trauma_aware_response(
        self,
        insight: EmotionalInsight,
        profile: Optional[PsychologicalProfile]
    ) -> bool:
        """Determine if a trauma-aware response is needed."""
        # High risk assessment
        if insight.risk_assessment.value >= MentalHealthRisk.HIGH.value:
            return True

        # Trauma indicators in profile
        if profile and profile.trauma_indicators:
            return True

        # Specific behavioral indicators
        trauma_behaviors = ["dissociation", "hypervigilance", "avoidance", "flashbacks"]
        if any(behavior in insight.behavioral_indicators for behavior in trauma_behaviors):
            return True

        return False

    async def _generate_trauma_aware_response(
        self,
        message: str,
        insight: EmotionalInsight,
        profile: Optional[PsychologicalProfile]
    ) -> str:
        """Generate a trauma-informed response."""
        try:
            trauma_indicators = profile.trauma_indicators if profile else []
            safety_level = "low" if insight.risk_assessment.value >= MentalHealthRisk.HIGH.value else "moderate"

            prompt = self.trauma_aware_prompt.format(
                message=message,
                emotional_state=json.dumps({
                    'primary_emotion': insight.primary_emotion.value,
                    'intensity': insight.emotional_intensity,
                    'stability': insight.emotional_stability
                }),
                trauma_indicators=trauma_indicators,
                safety_level=safety_level
            )

            response = await self.gemini_service.generate_response_async(prompt)
            return response.strip()

        except Exception as e:
            logger.error(f"Error generating trauma-aware response: {e}")
            return "I want you to know that you're safe here with me. Your feelings are completely valid, and you're showing incredible strength by reaching out. Take your time - there's no pressure to share more than you're comfortable with."

    async def _generate_standard_therapeutic_response(
        self,
        message: str,
        insight: EmotionalInsight,
        therapeutic_model: PsychologicalModel,
        therapeutic_technique: TherapeuticTechnique,
        therapeutic_goals: List[str]
    ) -> str:
        """Generate a standard therapeutic response."""
        try:
            prompt = self.therapeutic_response_prompt.format(
                therapeutic_model=therapeutic_model.value,
                message=message,
                emotional_analysis=json.dumps({
                    'primary_emotion': insight.primary_emotion.value,
                    'intensity': insight.emotional_intensity,
                    'underlying_needs': insight.underlying_needs,
                    'cognitive_patterns': insight.cognitive_patterns
                }),
                therapeutic_goals=therapeutic_goals,
                session_context="ongoing_support",
                therapeutic_technique=therapeutic_technique.value
            )

            response = await self.gemini_service.generate_response_async(prompt)
            return response.strip()

        except Exception as e:
            logger.error(f"Error generating therapeutic response: {e}")
            # Use technique-specific fallback
            technique_data = self.therapeutic_techniques.get(therapeutic_technique, {})
            phrases = technique_data.get('phrases', ["I'm here to listen and support you."])
            return phrases[0] + " How are you feeling right now?"

    def _calculate_empathy_level(self, insight: EmotionalInsight) -> float:
        """Calculate appropriate empathy level for the response."""
        base_empathy = 0.7

        # Increase empathy for high emotional intensity
        if insight.emotional_intensity > 0.7:
            base_empathy += 0.2

        # Increase empathy for high risk
        if insight.risk_assessment.value >= MentalHealthRisk.HIGH.value:
            base_empathy += 0.2

        # Increase empathy for complex emotional states
        if insight.emotional_complexity > 0.6:
            base_empathy += 0.1

        return min(base_empathy, 1.0)

    def _extract_validation_elements(self, insight: EmotionalInsight) -> List[str]:
        """Extract elements that need validation."""
        validation_elements = []

        # Validate emotions
        validation_elements.append(f"feeling_{insight.primary_emotion.value}")

        # Validate underlying needs
        for need in insight.underlying_needs:
            validation_elements.append(f"need_for_{need}")

        # Validate behavioral responses
        for behavior in insight.behavioral_indicators:
            if behavior in ["seeking_help", "reaching_out", "expressing_emotions"]:
                validation_elements.append(f"positive_behavior_{behavior}")

        return validation_elements

    def _identify_growth_opportunities(self, insight: EmotionalInsight) -> List[str]:
        """Identify opportunities for growth and development."""
        opportunities = []

        # Based on cognitive patterns
        if "catastrophic" in insight.cognitive_patterns:
            opportunities.append("developing_balanced_thinking")
        if "rumination" in insight.cognitive_patterns:
            opportunities.append("practicing_mindfulness")

        # Based on emotional patterns
        if insight.emotional_stability < 0.5:
            opportunities.append("emotional_regulation_skills")
        if insight.emotional_complexity > 0.7:
            opportunities.append("emotional_awareness_development")

        # Based on underlying needs
        if "autonomy" in insight.underlying_needs:
            opportunities.append("building_self_efficacy")
        if "connection" in insight.underlying_needs:
            opportunities.append("strengthening_relationships")

        return opportunities

    def _generate_follow_up_suggestions(self, insight: EmotionalInsight) -> List[str]:
        """Generate follow-up suggestions for continued support."""
        suggestions = []

        # Based on recommended techniques
        for technique in insight.recommended_techniques:
            if technique == TherapeuticTechnique.MINDFULNESS:
                suggestions.append("Try a 5-minute mindfulness exercise")
            elif technique == TherapeuticTechnique.GROUNDING:
                suggestions.append("Practice the 5-4-3-2-1 grounding technique")
            elif technique == TherapeuticTechnique.BREATHING_EXERCISES:
                suggestions.append("Try some deep breathing exercises")

        # Based on emotional state
        if insight.emotional_intensity > 0.7:
            suggestions.append("Check in with yourself in a few hours")

        # Based on risk level
        if insight.risk_assessment.value >= MentalHealthRisk.MODERATE.value:
            suggestions.append("Consider reaching out to a mental health professional")

        # Default suggestions
        if not suggestions:
            suggestions.extend([
                "Feel free to share more about what's on your mind",
                "I'm here whenever you need to talk"
            ])

        return suggestions

    def get_user_psychological_profile(self, user_id: str) -> Optional[PsychologicalProfile]:
        """Get the psychological profile for a user."""
        return self.psychological_profiles.get(user_id)

    def get_crisis_history(self, user_id: str) -> List[Dict[str, Any]]:
        """Get crisis history for a user."""
        return self.crisis_indicators.get(user_id, [])

    def get_therapeutic_alliance_strength(self, user_id: str) -> float:
        """Get the strength of therapeutic alliance with a user."""
        return self.therapeutic_alliance.get(user_id, 0.5)

    async def update_therapeutic_alliance(self, user_id: str, interaction_quality: float):
        """Update therapeutic alliance based on interaction quality."""
        current_alliance = self.therapeutic_alliance[user_id]
        # Weighted average with more weight on recent interactions
        self.therapeutic_alliance[user_id] = current_alliance * 0.8 + interaction_quality * 0.2

        # Update in psychological profile
        if user_id in self.psychological_profiles:
            self.psychological_profiles[user_id].therapeutic_alliance_strength = self.therapeutic_alliance[user_id]

# Backward compatibility alias
EmotionalIntelligenceService = AdvancedEmotionalIntelligence
