"""
Next-Generation Emotional Intelligence System for AI Companion.
Implements cutting-edge psychological models and therapeutic techniques.

Key Features:
- Advanced emotion recognition with dimensional models
- Trauma-informed care principles
- Crisis detection and intervention
- Therapeutic response generation
- Psychological profiling and adaptation
- Evidence-based intervention strategies
"""

import asyncio
import logging
import numpy as np
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
import json
import re

from models import EmotionType, InteractionType, utc_now
from gemini_service import GeminiService

logger = logging.getLogger(__name__)

class MentalHealthRisk(Enum):
    """Mental health risk levels for crisis detection."""
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    CRITICAL = "critical"

class TherapeuticTechnique(Enum):
    """Evidence-based therapeutic techniques."""
    COGNITIVE_BEHAVIORAL = "cbt"
    MINDFULNESS = "mindfulness"
    VALIDATION = "validation"
    REFRAMING = "reframing"
    GROUNDING = "grounding"
    ACTIVE_LISTENING = "active_listening"
    MOTIVATIONAL_INTERVIEWING = "motivational_interviewing"
    SOLUTION_FOCUSED = "solution_focused"
    PSYCHOEDUCATION = "psychoeducation"
    CRISIS_INTERVENTION = "crisis_intervention"

class EmotionalDimension(Enum):
    """Dimensional model of emotions (Russell, 1980)."""
    VALENCE = "valence"      # Pleasant vs. Unpleasant
    AROUSAL = "arousal"      # High vs. Low activation
    DOMINANCE = "dominance"  # Control vs. Submission

@dataclass
class EmotionalState:
    """Advanced emotional state representation."""
    primary_emotion: EmotionType
    secondary_emotions: List[EmotionType] = field(default_factory=list)
    
    # Dimensional model (Russell, 1980)
    valence: float = 0.0      # -1.0 (negative) to 1.0 (positive)
    arousal: float = 0.0      # 0.0 (calm) to 1.0 (excited)
    dominance: float = 0.0    # -1.0 (submissive) to 1.0 (dominant)
    
    # Intensity and confidence
    intensity: float = 0.5    # 0.0 to 1.0
    confidence: float = 0.5   # 0.0 to 1.0
    
    # Temporal aspects
    duration: Optional[timedelta] = None
    onset_time: datetime = field(default_factory=utc_now)
    
    # Context
    triggers: List[str] = field(default_factory=list)
    coping_mechanisms: List[str] = field(default_factory=list)
    social_context: Optional[str] = None

@dataclass
class PsychologicalProfile:
    """Comprehensive psychological profile for personalized responses."""
    user_id: str
    
    # Personality traits (Big Five model)
    openness: float = 0.5
    conscientiousness: float = 0.5
    extraversion: float = 0.5
    agreeableness: float = 0.5
    neuroticism: float = 0.5
    
    # Attachment style (Bowlby, 1988)
    attachment_style: str = "secure"  # secure, anxious, avoidant, disorganized
    
    # Emotional patterns
    emotional_baseline: Dict[str, float] = field(default_factory=dict)
    emotional_volatility: float = 0.5
    stress_tolerance: float = 0.5
    
    # Coping strategies
    preferred_coping_styles: List[str] = field(default_factory=list)
    effective_interventions: List[TherapeuticTechnique] = field(default_factory=list)
    
    # Risk factors
    trauma_indicators: List[str] = field(default_factory=list)
    mental_health_history: List[str] = field(default_factory=list)
    current_stressors: List[str] = field(default_factory=list)
    
    # Support system
    social_support_level: float = 0.5
    professional_support: bool = False
    
    # Learning and adaptation
    response_preferences: Dict[str, float] = field(default_factory=dict)
    communication_style: str = "balanced"  # direct, gentle, humorous, formal
    
    last_updated: datetime = field(default_factory=utc_now)

@dataclass
class TherapeuticResponse:
    """Structured therapeutic response with evidence-based techniques."""
    content: str
    technique: TherapeuticTechnique
    confidence: float
    
    # Response characteristics
    empathy_level: float = 0.8
    validation_level: float = 0.7
    challenge_level: float = 0.3
    
    # Follow-up suggestions
    follow_up_questions: List[str] = field(default_factory=list)
    coping_strategies: List[str] = field(default_factory=list)
    resources: List[str] = field(default_factory=list)
    
    # Risk assessment
    risk_level: MentalHealthRisk = MentalHealthRisk.LOW
    crisis_indicators: List[str] = field(default_factory=list)

class NextGenEmotionalIntelligence:
    """
    Next-generation emotional intelligence system implementing state-of-the-art
    psychological models and therapeutic techniques.
    """
    
    def __init__(self, gemini_service: GeminiService):
        """Initialize the next-gen emotional intelligence system."""
        self.gemini_service = gemini_service
        
        # User profiles and emotional states
        self.psychological_profiles: Dict[str, PsychologicalProfile] = {}
        self.emotional_histories: Dict[str, List[EmotionalState]] = {}
        
        # Crisis detection system
        self.crisis_keywords = self._load_crisis_keywords()
        self.risk_assessment_model = self._initialize_risk_model()
        
        # Therapeutic techniques database
        self.therapeutic_techniques = self._load_therapeutic_techniques()
        
        # Emotion recognition models
        self.emotion_patterns = self._load_emotion_patterns()
        
        # Statistics and monitoring
        self.interaction_stats = {
            'total_interactions': 0,
            'crisis_interventions': 0,
            'successful_de_escalations': 0,
            'therapeutic_responses': 0
        }
    
    def _load_crisis_keywords(self) -> Dict[str, List[str]]:
        """Load crisis detection keywords and phrases."""
        return {
            'suicide': [
                'kill myself', 'end it all', 'not worth living', 'better off dead',
                'suicide', 'suicidal', 'end my life', 'take my own life',
                'no point in living', 'want to die', 'wish I was dead'
            ],
            'self_harm': [
                'hurt myself', 'cut myself', 'self harm', 'self-harm',
                'cutting', 'burning myself', 'punish myself'
            ],
            'severe_depression': [
                'completely hopeless', 'nothing matters', 'empty inside',
                'can\'t go on', 'too much pain', 'unbearable'
            ],
            'panic': [
                'can\'t breathe', 'heart racing', 'panic attack',
                'losing control', 'going crazy', 'dying'
            ],
            'substance_abuse': [
                'drinking too much', 'using drugs', 'can\'t stop drinking',
                'overdose', 'getting high', 'need a drink'
            ]
        }
    
    def _initialize_risk_model(self) -> Dict[str, Any]:
        """Initialize risk assessment model parameters."""
        return {
            'crisis_threshold': 0.7,
            'moderate_risk_threshold': 0.4,
            'risk_factors': {
                'isolation': 0.3,
                'recent_loss': 0.4,
                'substance_use': 0.5,
                'previous_attempts': 0.8,
                'hopelessness': 0.6,
                'impulsivity': 0.4,
                'access_to_means': 0.7
            },
            'protective_factors': {
                'social_support': -0.4,
                'professional_help': -0.5,
                'coping_skills': -0.3,
                'future_plans': -0.4,
                'religious_beliefs': -0.2
            }
        }
    
    def _load_therapeutic_techniques(self) -> Dict[TherapeuticTechnique, Dict[str, Any]]:
        """Load therapeutic techniques and their applications."""
        return {
            TherapeuticTechnique.COGNITIVE_BEHAVIORAL: {
                'description': 'Challenge negative thought patterns',
                'applications': ['anxiety', 'depression', 'negative_thinking'],
                'prompts': [
                    "What evidence supports this thought?",
                    "Is there another way to look at this situation?",
                    "What would you tell a friend in this situation?"
                ]
            },
            TherapeuticTechnique.MINDFULNESS: {
                'description': 'Present-moment awareness and acceptance',
                'applications': ['anxiety', 'stress', 'overwhelm'],
                'prompts': [
                    "Let's focus on your breathing for a moment",
                    "What are you noticing in your body right now?",
                    "Can you observe these feelings without judgment?"
                ]
            },
            TherapeuticTechnique.VALIDATION: {
                'description': 'Acknowledge and validate emotions',
                'applications': ['all_emotions', 'trauma', 'distress'],
                'prompts': [
                    "Your feelings make complete sense",
                    "Anyone would feel this way in your situation",
                    "It's understandable that you're struggling"
                ]
            },
            TherapeuticTechnique.GROUNDING: {
                'description': 'Techniques to stay present and calm',
                'applications': ['panic', 'dissociation', 'overwhelm'],
                'prompts': [
                    "Name 5 things you can see, 4 you can touch, 3 you can hear",
                    "Feel your feet on the ground",
                    "Take slow, deep breaths with me"
                ]
            },
            TherapeuticTechnique.CRISIS_INTERVENTION: {
                'description': 'Immediate safety and stabilization',
                'applications': ['crisis', 'suicide_risk', 'severe_distress'],
                'prompts': [
                    "Your safety is the most important thing right now",
                    "Let's focus on getting through this moment",
                    "You don't have to face this alone"
                ]
            }
        }
    
    def _load_emotion_patterns(self) -> Dict[str, Any]:
        """Load emotion recognition patterns and linguistic markers."""
        return {
            'linguistic_markers': {
                EmotionType.SADNESS: ['sad', 'down', 'depressed', 'blue', 'low', 'empty'],
                EmotionType.ANXIETY: ['worried', 'anxious', 'nervous', 'scared', 'panicked'],
                EmotionType.ANGER: ['angry', 'mad', 'furious', 'irritated', 'frustrated'],
                EmotionType.JOY: ['happy', 'joyful', 'excited', 'elated', 'cheerful'],
                EmotionType.FEAR: ['afraid', 'terrified', 'frightened', 'scared'],
                EmotionType.STRESS: ['stressed', 'overwhelmed', 'pressure', 'burden'],
                EmotionType.LONELINESS: ['lonely', 'alone', 'isolated', 'disconnected']
            },
            'intensity_markers': {
                'high': ['extremely', 'incredibly', 'completely', 'totally', 'absolutely'],
                'moderate': ['quite', 'fairly', 'somewhat', 'pretty', 'rather'],
                'low': ['a little', 'slightly', 'somewhat', 'kind of', 'sort of']
            },
            'temporal_markers': {
                'current': ['now', 'currently', 'right now', 'at the moment'],
                'recent': ['today', 'yesterday', 'lately', 'recently'],
                'ongoing': ['always', 'constantly', 'all the time', 'continuously']
            }
        }
    
    async def analyze_emotional_state(
        self, 
        user_id: str, 
        text: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> EmotionalState:
        """
        Analyze emotional state using advanced NLP and psychological models.
        
        Args:
            user_id: User identifier
            text: User's message text
            context: Additional context information
        
        Returns:
            Comprehensive emotional state analysis
        """
        # Get or create psychological profile
        profile = self.get_psychological_profile(user_id)
        
        # Detect primary and secondary emotions
        emotions = await self._detect_emotions(text, profile)
        
        # Calculate dimensional values (valence, arousal, dominance)
        dimensions = self._calculate_emotional_dimensions(emotions, text)
        
        # Assess intensity and confidence
        intensity = self._assess_emotional_intensity(text)
        confidence = self._calculate_confidence(emotions, text)
        
        # Identify triggers and context
        triggers = self._identify_emotional_triggers(text, context)
        
        # Create emotional state
        emotional_state = EmotionalState(
            primary_emotion=emotions[0] if emotions else EmotionType.NEUTRAL,
            secondary_emotions=emotions[1:3] if len(emotions) > 1 else [],
            valence=dimensions['valence'],
            arousal=dimensions['arousal'],
            dominance=dimensions['dominance'],
            intensity=intensity,
            confidence=confidence,
            triggers=triggers,
            social_context=context.get('social_context') if context else None
        )
        
        # Update emotional history
        if user_id not in self.emotional_histories:
            self.emotional_histories[user_id] = []
        self.emotional_histories[user_id].append(emotional_state)
        
        # Keep only recent history (last 50 states)
        if len(self.emotional_histories[user_id]) > 50:
            self.emotional_histories[user_id] = self.emotional_histories[user_id][-50:]
        
        # Update psychological profile based on patterns
        await self._update_psychological_profile(user_id, emotional_state)
        
        self.interaction_stats['total_interactions'] += 1
        
        return emotional_state
    
    async def _detect_emotions(self, text: str, profile: PsychologicalProfile) -> List[EmotionType]:
        """Detect emotions using multiple approaches."""
        detected_emotions = []
        
        # Linguistic pattern matching
        text_lower = text.lower()
        emotion_scores = {}
        
        for emotion, markers in self.emotion_patterns['linguistic_markers'].items():
            score = sum(1 for marker in markers if marker in text_lower)
            if score > 0:
                emotion_scores[emotion] = score
        
        # Advanced NLP analysis using Gemini
        gemini_emotions = await self._gemini_emotion_analysis(text)
        
        # Combine results and rank by confidence
        all_emotions = set(emotion_scores.keys()) | set(gemini_emotions)
        
        # Score and rank emotions
        final_scores = {}
        for emotion in all_emotions:
            linguistic_score = emotion_scores.get(emotion, 0)
            gemini_score = 1 if emotion in gemini_emotions else 0
            
            # Weight based on user's emotional baseline
            baseline_weight = profile.emotional_baseline.get(emotion.value, 0.5)
            
            final_scores[emotion] = (linguistic_score + gemini_score) * (1 + baseline_weight)
        
        # Return top emotions
        sorted_emotions = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
        return [emotion for emotion, score in sorted_emotions if score > 0][:3]
    
    async def _gemini_emotion_analysis(self, text: str) -> List[EmotionType]:
        """Use Gemini for advanced emotion detection."""
        prompt = f"""
        Analyze the emotional content of this text and identify the primary emotions present.
        
        Text: "{text}"
        
        Available emotions: {[e.value for e in EmotionType]}
        
        Return only the emotion names that are clearly present, separated by commas.
        Focus on the most prominent emotions (maximum 3).
        """
        
        try:
            response = await self.gemini_service.generate_response_async(prompt)
            emotion_names = [name.strip().lower() for name in response.split(',')]
            
            # Convert to EmotionType objects
            emotions = []
            for name in emotion_names:
                try:
                    emotion = EmotionType(name)
                    emotions.append(emotion)
                except ValueError:
                    continue
            
            return emotions
        except Exception as e:
            logger.error(f"Error in Gemini emotion analysis: {e}")
            return []
    
    def _calculate_emotional_dimensions(self, emotions: List[EmotionType], text: str) -> Dict[str, float]:
        """Calculate valence, arousal, and dominance dimensions."""
        if not emotions:
            return {'valence': 0.0, 'arousal': 0.0, 'dominance': 0.0}
        
        # Emotion dimension mappings (Russell, 1980; Mehrabian, 1996)
        dimension_map = {
            EmotionType.JOY: {'valence': 0.8, 'arousal': 0.7, 'dominance': 0.6},
            EmotionType.SADNESS: {'valence': -0.7, 'arousal': 0.3, 'dominance': -0.4},
            EmotionType.ANGER: {'valence': -0.6, 'arousal': 0.9, 'dominance': 0.7},
            EmotionType.FEAR: {'valence': -0.8, 'arousal': 0.8, 'dominance': -0.6},
            EmotionType.SURPRISE: {'valence': 0.2, 'arousal': 0.8, 'dominance': 0.0},
            EmotionType.ANXIETY: {'valence': -0.5, 'arousal': 0.8, 'dominance': -0.3},
            EmotionType.EXCITEMENT: {'valence': 0.9, 'arousal': 0.9, 'dominance': 0.5},
            EmotionType.CONTENTMENT: {'valence': 0.6, 'arousal': 0.2, 'dominance': 0.3},
            EmotionType.STRESS: {'valence': -0.4, 'arousal': 0.7, 'dominance': -0.2},
            EmotionType.LONELINESS: {'valence': -0.8, 'arousal': 0.3, 'dominance': -0.5}
        }
        
        # Calculate weighted average based on detected emotions
        total_weight = len(emotions)
        dimensions = {'valence': 0.0, 'arousal': 0.0, 'dominance': 0.0}
        
        for i, emotion in enumerate(emotions):
            weight = 1.0 / (i + 1)  # First emotion has highest weight
            emotion_dims = dimension_map.get(emotion, {'valence': 0.0, 'arousal': 0.0, 'dominance': 0.0})
            
            for dim in dimensions:
                dimensions[dim] += emotion_dims[dim] * weight
        
        # Normalize by total weight
        for dim in dimensions:
            dimensions[dim] /= total_weight
        
        return dimensions
    
    def _assess_emotional_intensity(self, text: str) -> float:
        """Assess emotional intensity from text markers."""
        text_lower = text.lower()
        intensity = 0.5  # Base intensity
        
        # Check for intensity markers
        for level, markers in self.emotion_patterns['intensity_markers'].items():
            for marker in markers:
                if marker in text_lower:
                    if level == 'high':
                        intensity = min(intensity + 0.3, 1.0)
                    elif level == 'moderate':
                        intensity = min(intensity + 0.1, 1.0)
                    elif level == 'low':
                        intensity = max(intensity - 0.2, 0.1)
        
        # Check for exclamation marks and caps
        exclamation_count = text.count('!')
        caps_ratio = sum(1 for c in text if c.isupper()) / max(len(text), 1)
        
        intensity += min(exclamation_count * 0.1, 0.3)
        intensity += min(caps_ratio * 0.5, 0.2)
        
        return min(max(intensity, 0.1), 1.0)
    
    def _calculate_confidence(self, emotions: List[EmotionType], text: str) -> float:
        """Calculate confidence in emotion detection."""
        if not emotions:
            return 0.1
        
        # Base confidence
        confidence = 0.5
        
        # More emotions detected = higher confidence
        confidence += min(len(emotions) * 0.1, 0.3)
        
        # Longer text = higher confidence
        text_length_factor = min(len(text) / 100, 1.0)
        confidence += text_length_factor * 0.2
        
        # Presence of clear emotional words
        emotional_word_count = 0
        for emotion_markers in self.emotion_patterns['linguistic_markers'].values():
            emotional_word_count += sum(1 for marker in emotion_markers if marker in text.lower())
        
        confidence += min(emotional_word_count * 0.05, 0.3)
        
        return min(max(confidence, 0.1), 1.0)
    
    def _identify_emotional_triggers(self, text: str, context: Optional[Dict[str, Any]]) -> List[str]:
        """Identify potential emotional triggers from text and context."""
        triggers = []
        text_lower = text.lower()
        
        # Common trigger categories
        trigger_patterns = {
            'work_stress': ['work', 'job', 'boss', 'deadline', 'meeting', 'project'],
            'relationship': ['partner', 'boyfriend', 'girlfriend', 'husband', 'wife', 'friend'],
            'family': ['family', 'parents', 'mom', 'dad', 'mother', 'father', 'sibling'],
            'health': ['sick', 'pain', 'doctor', 'hospital', 'health', 'illness'],
            'financial': ['money', 'bills', 'debt', 'financial', 'broke', 'expensive'],
            'academic': ['school', 'exam', 'test', 'homework', 'grade', 'study'],
            'social': ['friends', 'social', 'party', 'lonely', 'isolated', 'rejected']
        }
        
        for trigger_type, keywords in trigger_patterns.items():
            if any(keyword in text_lower for keyword in keywords):
                triggers.append(trigger_type)
        
        # Add context-based triggers
        if context:
            if context.get('time_of_day') == 'late_night':
                triggers.append('late_night_thoughts')
            if context.get('day_of_week') in ['monday', 'sunday']:
                triggers.append('week_transition')
        
        return triggers
    
    async def _update_psychological_profile(self, user_id: str, emotional_state: EmotionalState):
        """Update psychological profile based on emotional patterns."""
        profile = self.get_psychological_profile(user_id)
        
        # Update emotional baseline
        emotion_key = emotional_state.primary_emotion.value
        if emotion_key not in profile.emotional_baseline:
            profile.emotional_baseline[emotion_key] = 0.5
        
        # Exponential moving average for baseline update
        alpha = 0.1  # Learning rate
        profile.emotional_baseline[emotion_key] = (
            (1 - alpha) * profile.emotional_baseline[emotion_key] + 
            alpha * emotional_state.intensity
        )
        
        # Update emotional volatility
        if len(self.emotional_histories[user_id]) > 1:
            recent_emotions = self.emotional_histories[user_id][-5:]  # Last 5 emotions
            intensities = [state.intensity for state in recent_emotions]
            volatility = np.std(intensities) if len(intensities) > 1 else 0.0
            profile.emotional_volatility = (1 - alpha) * profile.emotional_volatility + alpha * volatility
        
        # Update current stressors
        if emotional_state.triggers:
            for trigger in emotional_state.triggers:
                if trigger not in profile.current_stressors:
                    profile.current_stressors.append(trigger)
        
        # Keep only recent stressors (last 10)
        profile.current_stressors = profile.current_stressors[-10:]
        
        profile.last_updated = utc_now()
    
    def get_psychological_profile(self, user_id: str) -> PsychologicalProfile:
        """Get or create psychological profile for user."""
        if user_id not in self.psychological_profiles:
            self.psychological_profiles[user_id] = PsychologicalProfile(user_id=user_id)
        return self.psychological_profiles[user_id]
    
    async def assess_mental_health_risk(self, user_id: str, text: str, emotional_state: EmotionalState) -> MentalHealthRisk:
        """Assess mental health risk level for crisis detection."""
        risk_score = 0.0
        crisis_indicators = []
        
        # Check for crisis keywords
        text_lower = text.lower()
        for category, keywords in self.crisis_keywords.items():
            for keyword in keywords:
                if keyword in text_lower:
                    if category == 'suicide':
                        risk_score += 0.8
                        crisis_indicators.append(f"suicide_ideation: {keyword}")
                    elif category == 'self_harm':
                        risk_score += 0.6
                        crisis_indicators.append(f"self_harm: {keyword}")
                    elif category == 'severe_depression':
                        risk_score += 0.4
                        crisis_indicators.append(f"severe_depression: {keyword}")
                    elif category == 'panic':
                        risk_score += 0.3
                        crisis_indicators.append(f"panic: {keyword}")
        
        # Assess based on emotional state
        if emotional_state.primary_emotion in [EmotionType.SADNESS, EmotionType.FEAR, EmotionType.ANXIETY]:
            if emotional_state.intensity > 0.8:
                risk_score += 0.3
        
        # Check psychological profile
        profile = self.get_psychological_profile(user_id)
        if 'suicide' in profile.mental_health_history:
            risk_score += 0.4
        if 'depression' in profile.mental_health_history:
            risk_score += 0.2
        
        # Assess based on emotional history patterns
        if user_id in self.emotional_histories:
            recent_states = self.emotional_histories[user_id][-7:]  # Last week
            negative_emotions = [
                EmotionType.SADNESS, EmotionType.ANXIETY, EmotionType.FEAR, 
                EmotionType.STRESS, EmotionType.LONELINESS
            ]
            negative_count = sum(1 for state in recent_states if state.primary_emotion in negative_emotions)
            if negative_count > 5:  # Mostly negative emotions
                risk_score += 0.2
        
        # Determine risk level
        if risk_score >= self.risk_assessment_model['crisis_threshold']:
            return MentalHealthRisk.CRITICAL
        elif risk_score >= self.risk_assessment_model['moderate_risk_threshold']:
            return MentalHealthRisk.HIGH
        elif risk_score >= 0.2:
            return MentalHealthRisk.MODERATE
        else:
            return MentalHealthRisk.LOW

    async def generate_therapeutic_response(
        self,
        user_id: str,
        message: str,
        emotional_state: EmotionalState,
        risk_level: MentalHealthRisk,
        context: Optional[Dict[str, Any]] = None
    ) -> TherapeuticResponse:
        """
        Generate therapeutic response using evidence-based techniques.

        Args:
            user_id: User identifier
            message: User's message
            emotional_state: Current emotional state
            risk_level: Mental health risk level
            context: Additional context

        Returns:
            Structured therapeutic response
        """
        profile = self.get_psychological_profile(user_id)

        # Select appropriate therapeutic technique
        technique = self._select_therapeutic_technique(emotional_state, risk_level, profile)

        # Generate response based on technique and risk level
        if risk_level == MentalHealthRisk.CRITICAL:
            response = await self._generate_crisis_response(user_id, message, emotional_state)
            self.interaction_stats['crisis_interventions'] += 1
        else:
            response = await self._generate_standard_therapeutic_response(
                user_id, message, emotional_state, technique, profile
            )
            self.interaction_stats['therapeutic_responses'] += 1

        # Add follow-up suggestions
        response.follow_up_questions = self._generate_follow_up_questions(emotional_state, technique)
        response.coping_strategies = self._suggest_coping_strategies(emotional_state, profile)
        response.resources = self._suggest_resources(risk_level, emotional_state)

        return response

    def _select_therapeutic_technique(
        self,
        emotional_state: EmotionalState,
        risk_level: MentalHealthRisk,
        profile: PsychologicalProfile
    ) -> TherapeuticTechnique:
        """Select most appropriate therapeutic technique."""

        # Crisis situations require immediate intervention
        if risk_level in [MentalHealthRisk.CRITICAL, MentalHealthRisk.HIGH]:
            return TherapeuticTechnique.CRISIS_INTERVENTION

        # Check user's effective interventions history
        if profile.effective_interventions:
            # Use previously effective techniques
            return profile.effective_interventions[0]

        # Select based on primary emotion and intensity
        primary_emotion = emotional_state.primary_emotion
        intensity = emotional_state.intensity

        if primary_emotion in [EmotionType.ANXIETY, EmotionType.FEAR] and intensity > 0.7:
            return TherapeuticTechnique.GROUNDING
        elif primary_emotion == EmotionType.SADNESS and intensity > 0.6:
            return TherapeuticTechnique.VALIDATION
        elif primary_emotion in [EmotionType.ANGER, EmotionType.FRUSTRATION]:
            return TherapeuticTechnique.COGNITIVE_BEHAVIORAL
        elif primary_emotion == EmotionType.STRESS and intensity > 0.6:
            return TherapeuticTechnique.MINDFULNESS
        else:
            return TherapeuticTechnique.ACTIVE_LISTENING

    async def _generate_crisis_response(
        self,
        user_id: str,
        message: str,
        emotional_state: EmotionalState
    ) -> TherapeuticResponse:
        """Generate immediate crisis intervention response."""

        crisis_prompt = f"""
        You are a trained crisis counselor. A person is in emotional distress and may be at risk.

        Their message: "{message}"
        Emotional state: {emotional_state.primary_emotion.value} (intensity: {emotional_state.intensity:.1f})

        Provide an immediate, compassionate, and supportive response that:
        1. Validates their feelings
        2. Emphasizes their safety and worth
        3. Encourages them to seek immediate help if needed
        4. Offers hope and connection
        5. Is warm but professional

        Keep the response under 150 words and focus on immediate support.
        """

        try:
            content = await self.gemini_service.generate_response_async(crisis_prompt)

            # Add crisis resources
            crisis_resources = [
                "National Suicide Prevention Lifeline: 988",
                "Crisis Text Line: Text HOME to 741741",
                "Emergency Services: 911"
            ]

            return TherapeuticResponse(
                content=content,
                technique=TherapeuticTechnique.CRISIS_INTERVENTION,
                confidence=0.9,
                empathy_level=1.0,
                validation_level=1.0,
                challenge_level=0.0,
                risk_level=MentalHealthRisk.CRITICAL,
                resources=crisis_resources
            )

        except Exception as e:
            logger.error(f"Error generating crisis response: {e}")
            # Fallback crisis response
            return TherapeuticResponse(
                content="I'm really concerned about you right now. Your feelings are valid, and you don't have to face this alone. Please consider reaching out to a crisis helpline or emergency services if you're in immediate danger. You matter, and there are people who want to help.",
                technique=TherapeuticTechnique.CRISIS_INTERVENTION,
                confidence=0.8,
                empathy_level=1.0,
                validation_level=1.0,
                risk_level=MentalHealthRisk.CRITICAL,
                resources=["National Suicide Prevention Lifeline: 988", "Emergency Services: 911"]
            )

    async def _generate_standard_therapeutic_response(
        self,
        user_id: str,
        message: str,
        emotional_state: EmotionalState,
        technique: TherapeuticTechnique,
        profile: PsychologicalProfile
    ) -> TherapeuticResponse:
        """Generate standard therapeutic response using selected technique."""

        technique_info = self.therapeutic_techniques[technique]

        # Build context-aware prompt
        therapeutic_prompt = f"""
        You are an empathetic AI companion trained in {technique_info['description']}.

        User's message: "{message}"
        Primary emotion: {emotional_state.primary_emotion.value}
        Emotional intensity: {emotional_state.intensity:.1f}
        Communication style preference: {profile.communication_style}

        Using {technique.value} approach, provide a response that:
        1. Shows genuine empathy and understanding
        2. Validates their emotional experience
        3. Applies the therapeutic technique appropriately
        4. Maintains a warm, supportive tone
        5. Encourages self-reflection or positive coping

        Example prompts for this technique: {technique_info['prompts'][:2]}

        Keep response under 120 words and make it feel natural and conversational.
        """

        try:
            content = await self.gemini_service.generate_response_async(therapeutic_prompt)

            # Calculate response characteristics
            empathy_level = self._calculate_empathy_level(emotional_state, technique)
            validation_level = self._calculate_validation_level(emotional_state, technique)
            challenge_level = self._calculate_challenge_level(emotional_state, technique, profile)

            return TherapeuticResponse(
                content=content,
                technique=technique,
                confidence=0.8,
                empathy_level=empathy_level,
                validation_level=validation_level,
                challenge_level=challenge_level,
                risk_level=MentalHealthRisk.LOW
            )

        except Exception as e:
            logger.error(f"Error generating therapeutic response: {e}")
            # Fallback response
            return self._generate_fallback_response(emotional_state, technique)

    def _calculate_empathy_level(self, emotional_state: EmotionalState, technique: TherapeuticTechnique) -> float:
        """Calculate appropriate empathy level for response."""
        base_empathy = 0.7

        # Higher empathy for distressing emotions
        if emotional_state.primary_emotion in [EmotionType.SADNESS, EmotionType.FEAR, EmotionType.ANXIETY]:
            base_empathy += 0.2

        # Technique-specific adjustments
        if technique == TherapeuticTechnique.VALIDATION:
            base_empathy += 0.2
        elif technique == TherapeuticTechnique.COGNITIVE_BEHAVIORAL:
            base_empathy += 0.1

        # Intensity adjustment
        base_empathy += emotional_state.intensity * 0.1

        return min(base_empathy, 1.0)

    def _calculate_validation_level(self, emotional_state: EmotionalState, technique: TherapeuticTechnique) -> float:
        """Calculate appropriate validation level for response."""
        base_validation = 0.6

        # Higher validation for negative emotions
        if emotional_state.valence < 0:
            base_validation += 0.2

        # Technique-specific adjustments
        if technique == TherapeuticTechnique.VALIDATION:
            base_validation += 0.3
        elif technique == TherapeuticTechnique.CRISIS_INTERVENTION:
            base_validation += 0.4

        return min(base_validation, 1.0)

    def _calculate_challenge_level(
        self,
        emotional_state: EmotionalState,
        technique: TherapeuticTechnique,
        profile: PsychologicalProfile
    ) -> float:
        """Calculate appropriate challenge level for response."""
        base_challenge = 0.2

        # Lower challenge for high distress
        if emotional_state.intensity > 0.7:
            base_challenge -= 0.1

        # Technique-specific adjustments
        if technique == TherapeuticTechnique.COGNITIVE_BEHAVIORAL:
            base_challenge += 0.3
        elif technique == TherapeuticTechnique.MOTIVATIONAL_INTERVIEWING:
            base_challenge += 0.2
        elif technique in [TherapeuticTechnique.VALIDATION, TherapeuticTechnique.CRISIS_INTERVENTION]:
            base_challenge = 0.0

        # User preference adjustment
        if profile.communication_style == 'direct':
            base_challenge += 0.1
        elif profile.communication_style == 'gentle':
            base_challenge -= 0.1

        return max(min(base_challenge, 0.5), 0.0)

    def _generate_follow_up_questions(
        self,
        emotional_state: EmotionalState,
        technique: TherapeuticTechnique
    ) -> List[str]:
        """Generate appropriate follow-up questions."""
        questions = []

        if technique == TherapeuticTechnique.COGNITIVE_BEHAVIORAL:
            questions = [
                "What thoughts are going through your mind about this situation?",
                "Have you experienced something similar before? How did you handle it?",
                "What evidence do you have for and against this thought?"
            ]
        elif technique == TherapeuticTechnique.MINDFULNESS:
            questions = [
                "What are you noticing in your body right now?",
                "Can you describe what you're feeling without judging it?",
                "What would it be like to just observe these feelings for a moment?"
            ]
        elif technique == TherapeuticTechnique.SOLUTION_FOCUSED:
            questions = [
                "What would need to change for you to feel even slightly better?",
                "When was the last time you felt differently about this?",
                "What small step could you take today?"
            ]
        else:
            questions = [
                "How are you taking care of yourself through this?",
                "What support do you have available to you?",
                "What would be most helpful for you right now?"
            ]

        return questions[:2]  # Return top 2 questions

    def _suggest_coping_strategies(
        self,
        emotional_state: EmotionalState,
        profile: PsychologicalProfile
    ) -> List[str]:
        """Suggest personalized coping strategies."""
        strategies = []

        # Emotion-specific strategies
        if emotional_state.primary_emotion == EmotionType.ANXIETY:
            strategies.extend([
                "Deep breathing exercises (4-7-8 technique)",
                "Progressive muscle relaxation",
                "Grounding techniques (5-4-3-2-1 method)"
            ])
        elif emotional_state.primary_emotion == EmotionType.SADNESS:
            strategies.extend([
                "Gentle physical activity or walking",
                "Connecting with supportive friends or family",
                "Engaging in a comforting activity"
            ])
        elif emotional_state.primary_emotion == EmotionType.ANGER:
            strategies.extend([
                "Physical exercise to release tension",
                "Journaling to process thoughts",
                "Taking a break from the situation"
            ])
        elif emotional_state.primary_emotion == EmotionType.STRESS:
            strategies.extend([
                "Breaking tasks into smaller steps",
                "Prioritizing and organizing",
                "Taking regular breaks"
            ])

        # Add user's preferred coping styles if available
        if profile.preferred_coping_styles:
            strategies.extend(profile.preferred_coping_styles[:2])

        return strategies[:3]  # Return top 3 strategies

    def _suggest_resources(self, risk_level: MentalHealthRisk, emotional_state: EmotionalState) -> List[str]:
        """Suggest appropriate resources based on risk level and emotional state."""
        resources = []

        if risk_level in [MentalHealthRisk.HIGH, MentalHealthRisk.CRITICAL]:
            resources.extend([
                "National Suicide Prevention Lifeline: 988",
                "Crisis Text Line: Text HOME to 741741",
                "SAMHSA National Helpline: 1-************"
            ])

        # Emotion-specific resources
        if emotional_state.primary_emotion in [EmotionType.ANXIETY, EmotionType.STRESS]:
            resources.extend([
                "Anxiety and Depression Association of America (ADAA)",
                "Headspace or Calm apps for guided meditation",
                "Local therapy or counseling services"
            ])
        elif emotional_state.primary_emotion == EmotionType.SADNESS:
            resources.extend([
                "National Alliance on Mental Illness (NAMI)",
                "Psychology Today therapist finder",
                "Local support groups"
            ])

        return resources[:3]  # Return top 3 resources

    def _generate_fallback_response(
        self,
        emotional_state: EmotionalState,
        technique: TherapeuticTechnique
    ) -> TherapeuticResponse:
        """Generate fallback response when AI generation fails."""
        fallback_responses = {
            TherapeuticTechnique.VALIDATION: "I can hear that you're going through a difficult time, and your feelings are completely valid. It makes sense that you would feel this way given what you're experiencing.",
            TherapeuticTechnique.MINDFULNESS: "Let's take a moment to pause and breathe together. Sometimes when we're overwhelmed, focusing on the present moment can help us feel more grounded.",
            TherapeuticTechnique.COGNITIVE_BEHAVIORAL: "I notice you're having some challenging thoughts about this situation. Sometimes it can help to examine these thoughts and see if there might be other ways to look at what's happening.",
            TherapeuticTechnique.ACTIVE_LISTENING: "Thank you for sharing this with me. I can sense that this is really important to you, and I want you to know that I'm here to listen and support you."
        }

        content = fallback_responses.get(technique, "I'm here with you, and I want you to know that your feelings matter. You don't have to go through this alone.")

        return TherapeuticResponse(
            content=content,
            technique=technique,
            confidence=0.6,
            empathy_level=0.8,
            validation_level=0.7,
            challenge_level=0.1
        )

    def get_interaction_statistics(self) -> Dict[str, Any]:
        """Get comprehensive interaction statistics."""
        return {
            **self.interaction_stats,
            'active_users': len(self.psychological_profiles),
            'total_emotional_states': sum(len(history) for history in self.emotional_histories.values()),
            'average_risk_level': self._calculate_average_risk_level(),
            'most_common_emotions': self._get_most_common_emotions(),
            'intervention_success_rate': self._calculate_intervention_success_rate()
        }

    def _calculate_average_risk_level(self) -> str:
        """Calculate average risk level across all users."""
        # This would be implemented with actual risk assessments
        return "low"  # Placeholder

    def _get_most_common_emotions(self) -> List[str]:
        """Get most commonly detected emotions."""
        emotion_counts = {}
        for history in self.emotional_histories.values():
            for state in history:
                emotion = state.primary_emotion.value
                emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1

        sorted_emotions = sorted(emotion_counts.items(), key=lambda x: x[1], reverse=True)
        return [emotion for emotion, count in sorted_emotions[:5]]

    def _calculate_intervention_success_rate(self) -> float:
        """Calculate success rate of interventions."""
        if self.interaction_stats['crisis_interventions'] == 0:
            return 1.0
        return self.interaction_stats['successful_de_escalations'] / self.interaction_stats['crisis_interventions']
