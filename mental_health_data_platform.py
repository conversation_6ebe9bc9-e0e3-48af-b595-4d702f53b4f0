"""
Privacy-First Mental Health Data Platform.
Anonymizes emotional conversations for research and professional insights.
"""

import asyncio
import hashlib
import json
import logging
import uuid
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
from collections import defaultdict, Counter

from models import EmotionType
from enhanced_emotional_intelligence import (
    AttachmentStyle, CopingStrategy, PersonalityTrait, MentalHealthRisk
)

logger = logging.getLogger(__name__)

class DataPrivacyLevel(Enum):
    """Privacy levels for data anonymization."""
    PUBLIC = "public"  # Fully anonymized, safe for public research
    RESEARCH = "research"  # Anonymized for academic research
    CLINICAL = "clinical"  # Anonymized for clinical professionals
    INTERNAL = "internal"  # Internal analytics only

class InsightCategory(Enum):
    """Categories of mental health insights."""
    EMOTIONAL_PATTERNS = "emotional_patterns"
    CRISIS_INDICATORS = "crisis_indicators"
    THERAPEUTIC_EFFECTIVENESS = "therapeutic_effectiveness"
    POPULATION_TRENDS = "population_trends"
    INTERVENTION_OUTCOMES = "intervention_outcomes"
    RESILIENCE_FACTORS = "resilience_factors"

@dataclass
class AnonymizedUser:
    """Anonymized user profile for research."""
    anonymous_id: str
    age_range: str  # "18-25", "26-35", etc.
    region: str  # Broad geographic region
    demographic_cluster: str  # Statistical cluster ID
    created_at: datetime
    interaction_count: int = 0
    
@dataclass
class EmotionalDataPoint:
    """Anonymized emotional data point."""
    anonymous_user_id: str
    timestamp: datetime
    emotion: EmotionType
    intensity: float
    context_category: str  # "work", "relationships", "health", etc.
    intervention_type: Optional[str] = None
    outcome_rating: Optional[float] = None  # User-reported outcome (0-1)
    
@dataclass
class TherapeuticInteraction:
    """Anonymized therapeutic interaction."""
    anonymous_user_id: str
    timestamp: datetime
    technique_used: str
    user_engagement: float
    perceived_effectiveness: float
    emotional_state_before: EmotionType
    emotional_state_after: EmotionType
    session_duration: float  # in minutes
    
@dataclass
class PopulationInsight:
    """Population-level mental health insight."""
    insight_id: str
    category: InsightCategory
    title: str
    description: str
    statistical_significance: float
    sample_size: int
    confidence_interval: Tuple[float, float]
    generated_at: datetime
    privacy_level: DataPrivacyLevel
    metadata: Dict[str, Any] = field(default_factory=dict)

class MentalHealthDataPlatform:
    """
    Privacy-first mental health data platform for generating
    anonymized insights for research and clinical applications.
    """
    
    def __init__(self):
        # Anonymized data storage
        self.anonymized_users: Dict[str, AnonymizedUser] = {}
        self.emotional_data: List[EmotionalDataPoint] = []
        self.therapeutic_interactions: List[TherapeuticInteraction] = []
        
        # User ID mapping (one-way hash)
        self.user_id_mapping: Dict[str, str] = {}
        
        # Generated insights
        self.population_insights: List[PopulationInsight] = []
        
        # Privacy settings
        self.privacy_settings = {
            'min_sample_size': 50,  # Minimum sample size for insights
            'anonymization_salt': self._generate_salt(),
            'data_retention_days': 365,
            'consent_required': True
        }
        
        # Analytics cache
        self.analytics_cache: Dict[str, Any] = {}
        self.cache_expiry: Dict[str, datetime] = {}
    
    def _generate_salt(self) -> str:
        """Generate cryptographic salt for anonymization."""
        return hashlib.sha256(str(uuid.uuid4()).encode()).hexdigest()
    
    def _anonymize_user_id(self, user_id: str) -> str:
        """Convert user ID to anonymous ID using one-way hash."""
        if user_id not in self.user_id_mapping:
            # Create irreversible anonymous ID
            combined = f"{user_id}:{self.privacy_settings['anonymization_salt']}"
            anonymous_id = hashlib.sha256(combined.encode()).hexdigest()[:16]
            self.user_id_mapping[user_id] = anonymous_id
        
        return self.user_id_mapping[user_id]
    
    def _categorize_context(self, message: str, context: Dict[str, Any]) -> str:
        """Categorize conversation context for anonymized analysis."""
        message_lower = message.lower()
        
        # Work-related
        if any(word in message_lower for word in ['work', 'job', 'boss', 'colleague', 'office', 'career']):
            return "work"
        
        # Relationship-related
        if any(word in message_lower for word in ['relationship', 'partner', 'friend', 'family', 'love']):
            return "relationships"
        
        # Health-related
        if any(word in message_lower for word in ['health', 'sick', 'doctor', 'medical', 'pain']):
            return "health"
        
        # Financial
        if any(word in message_lower for word in ['money', 'financial', 'debt', 'bills', 'income']):
            return "financial"
        
        # Academic
        if any(word in message_lower for word in ['school', 'study', 'exam', 'grade', 'university']):
            return "academic"
        
        return "general"
    
    async def record_emotional_interaction(
        self,
        user_id: str,
        emotion: EmotionType,
        intensity: float,
        message: str,
        context: Dict[str, Any],
        intervention_type: Optional[str] = None,
        outcome_rating: Optional[float] = None
    ):
        """Record an anonymized emotional interaction."""
        try:
            anonymous_id = self._anonymize_user_id(user_id)
            
            # Create anonymized user if not exists
            if anonymous_id not in self.anonymized_users:
                self.anonymized_users[anonymous_id] = AnonymizedUser(
                    anonymous_id=anonymous_id,
                    age_range=self._get_age_range(context.get('age')),
                    region=self._get_region(context.get('location')),
                    demographic_cluster=self._get_demographic_cluster(context),
                    created_at=datetime.now(timezone.utc)
                )
            
            # Update interaction count
            self.anonymized_users[anonymous_id].interaction_count += 1
            
            # Record emotional data point
            data_point = EmotionalDataPoint(
                anonymous_user_id=anonymous_id,
                timestamp=datetime.now(timezone.utc),
                emotion=emotion,
                intensity=intensity,
                context_category=self._categorize_context(message, context),
                intervention_type=intervention_type,
                outcome_rating=outcome_rating
            )
            
            self.emotional_data.append(data_point)
            
            # Clean old data
            await self._cleanup_old_data()
            
        except Exception as e:
            logger.error(f"Error recording emotional interaction: {e}")
    
    async def record_therapeutic_interaction(
        self,
        user_id: str,
        technique_used: str,
        user_engagement: float,
        perceived_effectiveness: float,
        emotional_state_before: EmotionType,
        emotional_state_after: EmotionType,
        session_duration: float
    ):
        """Record an anonymized therapeutic interaction."""
        try:
            anonymous_id = self._anonymize_user_id(user_id)
            
            interaction = TherapeuticInteraction(
                anonymous_user_id=anonymous_id,
                timestamp=datetime.now(timezone.utc),
                technique_used=technique_used,
                user_engagement=user_engagement,
                perceived_effectiveness=perceived_effectiveness,
                emotional_state_before=emotional_state_before,
                emotional_state_after=emotional_state_after,
                session_duration=session_duration
            )
            
            self.therapeutic_interactions.append(interaction)
            
        except Exception as e:
            logger.error(f"Error recording therapeutic interaction: {e}")
    
    def _get_age_range(self, age: Optional[int]) -> str:
        """Convert age to anonymized age range."""
        if not age:
            return "unknown"
        
        if age < 18:
            return "under_18"
        elif age < 25:
            return "18_24"
        elif age < 35:
            return "25_34"
        elif age < 45:
            return "35_44"
        elif age < 55:
            return "45_54"
        elif age < 65:
            return "55_64"
        else:
            return "65_plus"
    
    def _get_region(self, location: Optional[str]) -> str:
        """Convert location to anonymized region."""
        if not location:
            return "unknown"
        
        # This would be more sophisticated in production
        location_lower = location.lower()
        
        if any(country in location_lower for country in ['us', 'usa', 'united states']):
            return "north_america"
        elif any(country in location_lower for country in ['uk', 'england', 'france', 'germany']):
            return "europe"
        elif any(country in location_lower for country in ['japan', 'china', 'india', 'korea']):
            return "asia"
        else:
            return "other"
    
    def _get_demographic_cluster(self, context: Dict[str, Any]) -> str:
        """Assign to demographic cluster for analysis."""
        # Simple clustering based on available context
        cluster_factors = []
        
        if context.get('age'):
            cluster_factors.append(f"age_{self._get_age_range(context['age'])}")
        
        if context.get('location'):
            cluster_factors.append(f"region_{self._get_region(context['location'])}")
        
        # Create cluster ID
        cluster_string = "_".join(sorted(cluster_factors))
        return hashlib.md5(cluster_string.encode()).hexdigest()[:8]
    
    async def generate_population_insights(self, privacy_level: DataPrivacyLevel = DataPrivacyLevel.RESEARCH) -> List[PopulationInsight]:
        """Generate population-level mental health insights."""
        insights = []
        
        try:
            # Emotional pattern insights
            emotional_insights = await self._analyze_emotional_patterns(privacy_level)
            insights.extend(emotional_insights)
            
            # Crisis indicator insights
            crisis_insights = await self._analyze_crisis_indicators(privacy_level)
            insights.extend(crisis_insights)
            
            # Therapeutic effectiveness insights
            therapeutic_insights = await self._analyze_therapeutic_effectiveness(privacy_level)
            insights.extend(therapeutic_insights)
            
            # Population trend insights
            trend_insights = await self._analyze_population_trends(privacy_level)
            insights.extend(trend_insights)
            
            # Store insights
            self.population_insights.extend(insights)
            
            return insights
            
        except Exception as e:
            logger.error(f"Error generating population insights: {e}")
            return []
    
    async def _analyze_emotional_patterns(self, privacy_level: DataPrivacyLevel) -> List[PopulationInsight]:
        """Analyze emotional patterns across the population."""
        if len(self.emotional_data) < self.privacy_settings['min_sample_size']:
            return []
        
        insights = []
        
        # Most common emotions
        emotion_counts = Counter([dp.emotion for dp in self.emotional_data])
        total_interactions = len(self.emotional_data)
        
        for emotion, count in emotion_counts.most_common(5):
            percentage = (count / total_interactions) * 100
            
            insight = PopulationInsight(
                insight_id=str(uuid.uuid4()),
                category=InsightCategory.EMOTIONAL_PATTERNS,
                title=f"Prevalence of {emotion.value.title()} Emotions",
                description=f"{emotion.value.title()} emotions account for {percentage:.1f}% of all recorded emotional states.",
                statistical_significance=0.95,  # Would be calculated properly
                sample_size=total_interactions,
                confidence_interval=(percentage - 2, percentage + 2),
                generated_at=datetime.now(timezone.utc),
                privacy_level=privacy_level,
                metadata={
                    'emotion': emotion.value,
                    'count': count,
                    'percentage': percentage
                }
            )
            insights.append(insight)
        
        return insights
    
    async def _analyze_crisis_indicators(self, privacy_level: DataPrivacyLevel) -> List[PopulationInsight]:
        """Analyze crisis indicators and risk factors."""
        high_intensity_data = [dp for dp in self.emotional_data if dp.intensity > 0.8]
        
        if len(high_intensity_data) < self.privacy_settings['min_sample_size']:
            return []
        
        insights = []
        
        # Time patterns for high-intensity emotions
        hour_counts = defaultdict(int)
        for dp in high_intensity_data:
            hour_counts[dp.timestamp.hour] += 1
        
        peak_hour = max(hour_counts, key=hour_counts.get)
        peak_count = hour_counts[peak_hour]
        
        insight = PopulationInsight(
            insight_id=str(uuid.uuid4()),
            category=InsightCategory.CRISIS_INDICATORS,
            title="Peak Hours for Emotional Distress",
            description=f"High-intensity emotional episodes peak at {peak_hour}:00, with {peak_count} incidents recorded.",
            statistical_significance=0.90,
            sample_size=len(high_intensity_data),
            confidence_interval=(peak_hour - 1, peak_hour + 1),
            generated_at=datetime.now(timezone.utc),
            privacy_level=privacy_level,
            metadata={
                'peak_hour': peak_hour,
                'incident_count': peak_count,
                'hourly_distribution': dict(hour_counts)
            }
        )
        insights.append(insight)
        
        return insights
    
    async def _analyze_therapeutic_effectiveness(self, privacy_level: DataPrivacyLevel) -> List[PopulationInsight]:
        """Analyze effectiveness of therapeutic interventions."""
        if len(self.therapeutic_interactions) < self.privacy_settings['min_sample_size']:
            return []
        
        insights = []
        
        # Technique effectiveness
        technique_effectiveness = defaultdict(list)
        for interaction in self.therapeutic_interactions:
            technique_effectiveness[interaction.technique_used].append(
                interaction.perceived_effectiveness
            )
        
        for technique, effectiveness_scores in technique_effectiveness.items():
            if len(effectiveness_scores) >= 10:  # Minimum sample per technique
                avg_effectiveness = np.mean(effectiveness_scores)
                std_effectiveness = np.std(effectiveness_scores)
                
                insight = PopulationInsight(
                    insight_id=str(uuid.uuid4()),
                    category=InsightCategory.THERAPEUTIC_EFFECTIVENESS,
                    title=f"Effectiveness of {technique.title()} Technique",
                    description=f"{technique.title()} shows an average effectiveness rating of {avg_effectiveness:.2f} (±{std_effectiveness:.2f}).",
                    statistical_significance=0.95,
                    sample_size=len(effectiveness_scores),
                    confidence_interval=(avg_effectiveness - std_effectiveness, avg_effectiveness + std_effectiveness),
                    generated_at=datetime.now(timezone.utc),
                    privacy_level=privacy_level,
                    metadata={
                        'technique': technique,
                        'average_effectiveness': avg_effectiveness,
                        'standard_deviation': std_effectiveness,
                        'sample_count': len(effectiveness_scores)
                    }
                )
                insights.append(insight)
        
        return insights
    
    async def _analyze_population_trends(self, privacy_level: DataPrivacyLevel) -> List[PopulationInsight]:
        """Analyze population-level mental health trends."""
        if len(self.emotional_data) < self.privacy_settings['min_sample_size']:
            return []
        
        insights = []
        
        # Weekly emotional trends
        recent_data = [
            dp for dp in self.emotional_data 
            if dp.timestamp > datetime.now(timezone.utc) - timedelta(days=30)
        ]
        
        if len(recent_data) >= 20:
            weekly_intensity = defaultdict(list)
            for dp in recent_data:
                week_day = dp.timestamp.weekday()
                weekly_intensity[week_day].append(dp.intensity)
            
            # Find day with highest average intensity
            daily_averages = {day: np.mean(intensities) for day, intensities in weekly_intensity.items()}
            peak_day = max(daily_averages, key=daily_averages.get)
            peak_intensity = daily_averages[peak_day]
            
            day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            
            insight = PopulationInsight(
                insight_id=str(uuid.uuid4()),
                category=InsightCategory.POPULATION_TRENDS,
                title="Weekly Emotional Intensity Patterns",
                description=f"{day_names[peak_day]} shows the highest average emotional intensity ({peak_intensity:.2f}).",
                statistical_significance=0.85,
                sample_size=len(recent_data),
                confidence_interval=(peak_intensity - 0.1, peak_intensity + 0.1),
                generated_at=datetime.now(timezone.utc),
                privacy_level=privacy_level,
                metadata={
                    'peak_day': day_names[peak_day],
                    'peak_intensity': peak_intensity,
                    'daily_averages': {day_names[day]: avg for day, avg in daily_averages.items()}
                }
            )
            insights.append(insight)
        
        return insights
    
    async def _cleanup_old_data(self):
        """Remove data older than retention period."""
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=self.privacy_settings['data_retention_days'])
        
        # Clean emotional data
        self.emotional_data = [dp for dp in self.emotional_data if dp.timestamp > cutoff_date]
        
        # Clean therapeutic interactions
        self.therapeutic_interactions = [ti for ti in self.therapeutic_interactions if ti.timestamp > cutoff_date]
    
    def get_anonymized_statistics(self) -> Dict[str, Any]:
        """Get anonymized platform statistics."""
        return {
            'total_users': len(self.anonymized_users),
            'total_interactions': len(self.emotional_data),
            'total_therapeutic_sessions': len(self.therapeutic_interactions),
            'insights_generated': len(self.population_insights),
            'data_retention_days': self.privacy_settings['data_retention_days'],
            'privacy_compliant': True
        }
    
    def export_research_data(self, privacy_level: DataPrivacyLevel = DataPrivacyLevel.RESEARCH) -> Dict[str, Any]:
        """Export anonymized data for research purposes."""
        if privacy_level == DataPrivacyLevel.PUBLIC:
            # Most restrictive - only aggregated insights
            return {
                'insights': [
                    {
                        'category': insight.category.value,
                        'title': insight.title,
                        'description': insight.description,
                        'sample_size': insight.sample_size,
                        'generated_at': insight.generated_at.isoformat()
                    }
                    for insight in self.population_insights
                    if insight.privacy_level.value in ['public', 'research']
                ]
            }
        
        elif privacy_level == DataPrivacyLevel.RESEARCH:
            # Include aggregated patterns
            return {
                'insights': [insight.__dict__ for insight in self.population_insights],
                'aggregated_patterns': {
                    'emotion_distribution': dict(Counter([dp.emotion.value for dp in self.emotional_data])),
                    'context_distribution': dict(Counter([dp.context_category for dp in self.emotional_data])),
                    'demographic_clusters': len(set([user.demographic_cluster for user in self.anonymized_users.values()]))
                },
                'sample_size': len(self.emotional_data),
                'export_timestamp': datetime.now(timezone.utc).isoformat()
            }
        
        return {'error': 'Invalid privacy level'}
