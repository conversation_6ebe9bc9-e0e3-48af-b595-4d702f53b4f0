# AI Companion System - Git Ignore File

# Environment variables and secrets
.env
.env.local
.env.production
.env.staging

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Jupyter Notebook
.ipynb_checkpoints

# pytest
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Data directories
data/
*.db
*.sqlite
*.sqlite3

# Logs
*.log
logs/
ai_companion_performance.log
enhanced_ai_companion.log

# Cache
.cache/
*.cache

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Model files (too large for git)
*.bin
*.safetensors
models/

# API keys and sensitive data
api_keys.txt
secrets.txt
credentials.json

# Backup files
*.bak
*.backup

# Docker
.dockerignore

# Redis dump
dump.rdb

# WhatsApp media
whatsapp_media/

# Performance data
performance_data/
metrics/
