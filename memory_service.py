"""
Advanced Memory Service for the AI Companion System.
Implements neural-symbolic dual-memory architecture with episodic memory,
semantic networks, and cognitive-inspired memory consolidation.
"""

import redis
import json
import hashlib
import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional, Any, Tuple, Set, Union
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
from sentence_transformers import SentenceTransformer
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import KMeans
import networkx as nx

from models import (
    MemoryEntry, PersonalMemory, UniversalMemory, MemoryType,
    InteractionType, EmotionType, UserProfile, ContextualMemory
)
from config import settings
from storage_service import PersistentStorageService

logger = logging.getLogger(__name__)

class MemoryConsolidationType(Enum):
    """Types of memory consolidation processes."""
    IMMEDIATE = "immediate"  # Working memory
    SHORT_TERM = "short_term"  # Recent experiences
    LONG_TERM = "long_term"  # Consolidated memories
    EPISODIC = "episodic"  # Specific life events
    SEMANTIC = "semantic"  # General knowledge

class MemoryImportanceLevel(Enum):
    """Importance levels for memory prioritization."""
    CRITICAL = 5  # Life-changing events, trauma, major decisions
    HIGH = 4      # Important personal events, strong emotions
    MEDIUM = 3    # Regular meaningful interactions
    LOW = 2       # Casual conversations
    MINIMAL = 1   # Background information

@dataclass
class EpisodicMemory:
    """Represents a specific life episode with rich contextual information."""
    episode_id: str
    user_id: str
    title: str
    description: str
    timestamp: datetime
    duration: Optional[timedelta]
    location_context: Optional[str]
    emotional_intensity: float  # 0.0 to 1.0
    people_involved: List[str]
    key_events: List[str]
    emotional_arc: List[Tuple[datetime, EmotionType, float]]  # Emotion timeline
    significance_score: float  # How important this episode is
    related_episodes: List[str]  # Connected memories
    sensory_details: Dict[str, str]  # Visual, auditory, etc.
    learned_insights: List[str]  # What was learned from this episode
    embedding: Optional[np.ndarray] = None

@dataclass
class SemanticNode:
    """Node in the semantic knowledge network."""
    node_id: str
    concept: str
    category: str
    attributes: Dict[str, Any]
    connections: Dict[str, float]  # Connected concepts with strength
    activation_level: float = 0.0
    last_accessed: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    access_frequency: int = 0

@dataclass
class MemoryConsolidationRule:
    """Rules for memory consolidation and forgetting."""
    consolidation_type: MemoryConsolidationType
    time_threshold: timedelta
    importance_threshold: float
    emotional_weight: float
    frequency_weight: float
    recency_weight: float

class AdvancedMemoryService:
    """
    Advanced memory service implementing neural-symbolic dual-memory architecture.

    Features:
    - Episodic memory for life events
    - Semantic networks for knowledge representation
    - Memory consolidation based on cognitive science
    - Adaptive forgetting and importance weighting
    - Neural-symbolic reasoning capabilities
    """

    def __init__(self):
        """Initialize the advanced memory service."""
        self.storage_service = PersistentStorageService()
        self.redis_client = self.storage_service.redis_client if self.storage_service.redis_available else None
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
        self.memory_ttl = settings.memory_ttl

        # Core memory stores
        self.episodic_memories: Dict[str, Dict[str, EpisodicMemory]] = defaultdict(dict)
        self.semantic_networks: Dict[str, nx.Graph] = defaultdict(nx.Graph)
        self.working_memory: Dict[str, deque] = defaultdict(lambda: deque(maxlen=7))  # Miller's 7±2

        # Memory consolidation system
        self.consolidation_rules = self._initialize_consolidation_rules()
        self.consolidation_queue: deque = deque()

        # Caching and optimization
        self.embedding_cache = {}
        self.importance_cache = {}
        self.retrieval_cache = {}

        # Memory statistics
        self.memory_stats = {
            'episodic_count': 0,
            'semantic_nodes': 0,
            'consolidations_performed': 0,
            'forgotten_memories': 0
        }

        # Start background consolidation process
        asyncio.create_task(self._background_consolidation())

    def _initialize_consolidation_rules(self) -> List[MemoryConsolidationRule]:
        """Initialize memory consolidation rules based on cognitive science."""
        return [
            # Immediate to short-term (working memory)
            MemoryConsolidationRule(
                consolidation_type=MemoryConsolidationType.IMMEDIATE,
                time_threshold=timedelta(minutes=15),
                importance_threshold=0.3,
                emotional_weight=0.4,
                frequency_weight=0.3,
                recency_weight=0.3
            ),
            # Short-term to long-term
            MemoryConsolidationRule(
                consolidation_type=MemoryConsolidationType.SHORT_TERM,
                time_threshold=timedelta(hours=24),
                importance_threshold=0.5,
                emotional_weight=0.5,
                frequency_weight=0.3,
                recency_weight=0.2
            ),
            # Long-term consolidation
            MemoryConsolidationRule(
                consolidation_type=MemoryConsolidationType.LONG_TERM,
                time_threshold=timedelta(days=7),
                importance_threshold=0.7,
                emotional_weight=0.6,
                frequency_weight=0.4,
                recency_weight=0.0
            ),
            # Episodic memory formation
            MemoryConsolidationRule(
                consolidation_type=MemoryConsolidationType.EPISODIC,
                time_threshold=timedelta(hours=1),
                importance_threshold=0.6,
                emotional_weight=0.7,
                frequency_weight=0.2,
                recency_weight=0.1
            )
        ]

    async def _background_consolidation(self):
        """Background process for memory consolidation."""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                await self._perform_memory_consolidation()
            except Exception as e:
                logger.error(f"Memory consolidation error: {e}")

    async def _perform_memory_consolidation(self):
        """Perform memory consolidation based on cognitive rules."""
        for user_id in list(self.episodic_memories.keys()):
            await self._consolidate_user_memories(user_id)

    async def _consolidate_user_memories(self, user_id: str):
        """Consolidate memories for a specific user."""
        current_time = datetime.now(timezone.utc)

        # Get all memories for user
        user_episodes = self.episodic_memories.get(user_id, {})

        for episode_id, episode in list(user_episodes.items()):
            time_since_creation = current_time - episode.timestamp

            # Apply consolidation rules
            for rule in self.consolidation_rules:
                if time_since_creation >= rule.time_threshold:
                    importance_score = self._calculate_memory_importance(episode, rule)

                    if importance_score >= rule.importance_threshold:
                        await self._promote_memory(user_id, episode, rule.consolidation_type)
                    else:
                        await self._consider_forgetting(user_id, episode_id, importance_score)

    def _calculate_memory_importance(self, episode: EpisodicMemory, rule: MemoryConsolidationRule) -> float:
        """Calculate the importance score of a memory."""
        # Base importance from emotional intensity
        emotional_score = episode.emotional_intensity * rule.emotional_weight

        # Frequency score (how often this memory is accessed)
        frequency_score = min(episode.significance_score, 1.0) * rule.frequency_weight

        # Recency score (more recent = more important for some rules)
        time_since = datetime.now(timezone.utc) - episode.timestamp
        recency_score = max(0, 1 - (time_since.total_seconds() / (30 * 24 * 3600))) * rule.recency_weight

        return emotional_score + frequency_score + recency_score

    def _get_embedding(self, text: str) -> np.ndarray:
        """Get embedding for text with caching."""
        text_hash = hashlib.md5(text.encode()).hexdigest()
        
        if text_hash not in self.embedding_cache:
            embedding = self.embedding_model.encode([text])[0]
            self.embedding_cache[text_hash] = embedding
            
        return self.embedding_cache[text_hash]

    async def create_episodic_memory(
        self,
        user_id: str,
        title: str,
        description: str,
        emotional_context: Dict[str, Any],
        conversation_context: Optional[Dict[str, Any]] = None
    ) -> EpisodicMemory:
        """Create a new episodic memory from a significant interaction."""
        episode_id = f"ep_{user_id}_{int(datetime.now(timezone.utc).timestamp())}"

        # Extract emotional information
        emotional_intensity = emotional_context.get('intensity', 0.5)
        primary_emotion = emotional_context.get('primary_emotion', EmotionType.NEUTRAL)

        # Create emotional arc (simplified for now)
        emotional_arc = [(datetime.now(timezone.utc), primary_emotion, emotional_intensity)]

        # Extract key events and insights
        key_events = self._extract_key_events(description, conversation_context)
        learned_insights = self._extract_insights(description, emotional_context)

        # Calculate significance score
        significance_score = self._calculate_episode_significance(
            emotional_intensity, key_events, learned_insights
        )

        # Create episodic memory
        episode = EpisodicMemory(
            episode_id=episode_id,
            user_id=user_id,
            title=title,
            description=description,
            timestamp=datetime.now(timezone.utc),
            duration=None,  # Could be calculated from conversation length
            location_context=conversation_context.get('location') if conversation_context else None,
            emotional_intensity=emotional_intensity,
            people_involved=[user_id],  # Could include other participants
            key_events=key_events,
            emotional_arc=emotional_arc,
            significance_score=significance_score,
            related_episodes=[],
            sensory_details={},
            learned_insights=learned_insights,
            embedding=self._get_embedding(f"{title} {description}")
        )

        # Store episodic memory
        self.episodic_memories[user_id][episode_id] = episode
        self.memory_stats['episodic_count'] += 1

        # Update semantic network
        await self._update_semantic_network(user_id, episode)

        # Add to working memory
        self.working_memory[user_id].append(episode_id)

        logger.info(f"Created episodic memory {episode_id} for user {user_id}")
        return episode

    def _extract_key_events(self, description: str, context: Optional[Dict[str, Any]]) -> List[str]:
        """Extract key events from the description."""
        # Simple keyword-based extraction (could be enhanced with NLP)
        key_phrases = [
            "decided to", "realized that", "felt", "learned", "discovered",
            "changed", "started", "stopped", "met", "lost", "gained"
        ]

        events = []
        description_lower = description.lower()

        for phrase in key_phrases:
            if phrase in description_lower:
                # Extract sentence containing the phrase
                sentences = description.split('.')
                for sentence in sentences:
                    if phrase in sentence.lower():
                        events.append(sentence.strip())
                        break

        return events[:5]  # Limit to top 5 events

    def _extract_insights(self, description: str, emotional_context: Dict[str, Any]) -> List[str]:
        """Extract learned insights from the interaction."""
        insights = []

        # Pattern-based insight extraction
        insight_patterns = [
            r"I (?:learned|realized|understood) (?:that )?(.+)",
            r"(?:This|It) (?:taught|showed|made) me (?:that )?(.+)",
            r"I (?:now|finally) (?:know|understand) (?:that )?(.+)"
        ]

        import re
        for pattern in insight_patterns:
            matches = re.findall(pattern, description, re.IGNORECASE)
            insights.extend(matches)

        return insights[:3]  # Limit to top 3 insights

    def _calculate_episode_significance(
        self,
        emotional_intensity: float,
        key_events: List[str],
        insights: List[str]
    ) -> float:
        """Calculate how significant this episode is."""
        # Base score from emotional intensity
        significance = emotional_intensity * 0.4

        # Add points for key events
        significance += min(len(key_events) * 0.1, 0.3)

        # Add points for insights
        significance += min(len(insights) * 0.15, 0.3)

        return min(significance, 1.0)

    async def _update_semantic_network(self, user_id: str, episode: EpisodicMemory):
        """Update the user's semantic knowledge network."""
        network = self.semantic_networks[user_id]

        # Extract concepts from the episode
        concepts = self._extract_concepts(episode.description)

        # Add nodes and connections
        for concept in concepts:
            if not network.has_node(concept):
                network.add_node(concept,
                    category=self._categorize_concept(concept),
                    activation_level=0.5,
                    episodes=[episode.episode_id]
                )
            else:
                # Update existing node
                node_data = network.nodes[concept]
                node_data['episodes'] = node_data.get('episodes', []) + [episode.episode_id]
                node_data['activation_level'] = min(node_data.get('activation_level', 0) + 0.1, 1.0)

        # Create connections between concepts
        for i, concept1 in enumerate(concepts):
            for concept2 in concepts[i+1:]:
                if network.has_edge(concept1, concept2):
                    network[concept1][concept2]['weight'] += 0.1
                else:
                    network.add_edge(concept1, concept2, weight=0.5)

        self.memory_stats['semantic_nodes'] = network.number_of_nodes()

    def _extract_concepts(self, text: str) -> List[str]:
        """Extract key concepts from text."""
        # Simple concept extraction (could be enhanced with NER)
        import re

        # Remove common words and extract meaningful terms
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'}

        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
        concepts = [word for word in words if word not in stop_words]

        # Return unique concepts, limited to most frequent
        from collections import Counter
        concept_counts = Counter(concepts)
        return [concept for concept, count in concept_counts.most_common(10)]

    def _categorize_concept(self, concept: str) -> str:
        """Categorize a concept into semantic categories."""
        # Simple categorization (could be enhanced with word embeddings)
        emotion_words = {'happy', 'sad', 'angry', 'excited', 'worried', 'calm', 'stressed', 'joy', 'fear', 'love'}
        activity_words = {'work', 'study', 'exercise', 'travel', 'cook', 'read', 'write', 'play', 'sleep', 'eat'}
        relationship_words = {'friend', 'family', 'partner', 'colleague', 'parent', 'child', 'sibling', 'relationship'}

        if concept in emotion_words:
            return 'emotion'
        elif concept in activity_words:
            return 'activity'
        elif concept in relationship_words:
            return 'relationship'
        else:
            return 'general'

    async def retrieve_contextual_memories(
        self,
        user_id: str,
        query: str,
        context: Optional[Dict[str, Any]] = None,
        max_memories: int = 10
    ) -> List[EpisodicMemory]:
        """
        Retrieve contextually relevant memories using neural-symbolic reasoning.

        This method combines:
        1. Semantic similarity search
        2. Episodic memory retrieval
        3. Semantic network activation
        4. Temporal and emotional context
        """
        # Get query embedding
        query_embedding = self._get_embedding(query)

        # Retrieve candidate memories
        candidates = []
        user_episodes = self.episodic_memories.get(user_id, {})

        for episode_id, episode in user_episodes.items():
            if episode.embedding is not None:
                # Calculate semantic similarity
                similarity = cosine_similarity(
                    query_embedding.reshape(1, -1),
                    episode.embedding.reshape(1, -1)
                )[0][0]

                # Calculate contextual relevance
                relevance_score = self._calculate_contextual_relevance(
                    episode, query, context, similarity
                )

                candidates.append((episode, relevance_score))

        # Sort by relevance and return top memories
        candidates.sort(key=lambda x: x[1], reverse=True)
        return [episode for episode, score in candidates[:max_memories]]

    def _calculate_contextual_relevance(
        self,
        episode: EpisodicMemory,
        query: str,
        context: Optional[Dict[str, Any]],
        semantic_similarity: float
    ) -> float:
        """Calculate how contextually relevant a memory is."""
        relevance = semantic_similarity * 0.4  # Base semantic similarity

        # Emotional context matching
        if context and 'emotion' in context:
            query_emotion = context['emotion']
            for timestamp, emotion, intensity in episode.emotional_arc:
                if emotion == query_emotion:
                    relevance += 0.2 * intensity

        # Temporal relevance (recent memories are more relevant)
        time_since = datetime.now(timezone.utc) - episode.timestamp
        days_since = time_since.total_seconds() / (24 * 3600)
        temporal_relevance = max(0, 1 - (days_since / 30))  # Decay over 30 days
        relevance += temporal_relevance * 0.2

        # Importance weighting
        relevance += episode.significance_score * 0.2

        return min(relevance, 1.0)

    async def activate_semantic_network(self, user_id: str, concepts: List[str]) -> Dict[str, float]:
        """
        Activate the semantic network based on input concepts.
        Returns activation levels for all connected concepts.
        """
        network = self.semantic_networks.get(user_id)
        if not network:
            return {}

        # Initialize activation levels
        activations = {node: 0.0 for node in network.nodes()}

        # Set initial activation for input concepts
        for concept in concepts:
            if concept in activations:
                activations[concept] = 1.0

        # Spread activation through the network
        for _ in range(3):  # 3 iterations of spreading
            new_activations = activations.copy()

            for node in network.nodes():
                if activations[node] > 0:
                    # Spread activation to connected nodes
                    for neighbor in network.neighbors(node):
                        edge_weight = network[node][neighbor].get('weight', 0.5)
                        spread_amount = activations[node] * edge_weight * 0.3
                        new_activations[neighbor] = min(
                            new_activations[neighbor] + spread_amount, 1.0
                        )

            activations = new_activations

        # Filter out low activations
        return {node: activation for node, activation in activations.items()
                if activation > 0.1}

    async def get_memory_insights(self, user_id: str) -> Dict[str, Any]:
        """Generate insights about the user's memory patterns."""
        user_episodes = self.episodic_memories.get(user_id, {})
        network = self.semantic_networks.get(user_id)

        if not user_episodes:
            return {"message": "No memories available for analysis"}

        # Analyze emotional patterns
        emotions = []
        for episode in user_episodes.values():
            for _, emotion, intensity in episode.emotional_arc:
                emotions.append((emotion, intensity))

        emotion_analysis = self._analyze_emotional_patterns(emotions)

        # Analyze memory themes
        themes = self._extract_memory_themes(user_episodes)

        # Analyze semantic network
        network_insights = self._analyze_semantic_network(network) if network else {}

        return {
            "total_episodes": len(user_episodes),
            "emotional_patterns": emotion_analysis,
            "key_themes": themes,
            "semantic_insights": network_insights,
            "memory_consolidation_stats": self.memory_stats
        }

    def _analyze_emotional_patterns(self, emotions: List[Tuple[EmotionType, float]]) -> Dict[str, Any]:
        """Analyze patterns in emotional memories."""
        if not emotions:
            return {}

        emotion_counts = defaultdict(int)
        emotion_intensities = defaultdict(list)

        for emotion, intensity in emotions:
            emotion_counts[emotion.value] += 1
            emotion_intensities[emotion.value].append(intensity)

        # Calculate average intensities
        avg_intensities = {
            emotion: np.mean(intensities)
            for emotion, intensities in emotion_intensities.items()
        }

        return {
            "most_frequent_emotions": dict(sorted(emotion_counts.items(),
                                                key=lambda x: x[1], reverse=True)[:5]),
            "average_intensities": avg_intensities,
            "emotional_volatility": np.std(list(avg_intensities.values())) if avg_intensities else 0
        }

    def _extract_memory_themes(self, episodes: Dict[str, EpisodicMemory]) -> List[str]:
        """Extract common themes from episodic memories."""
        all_concepts = []
        for episode in episodes.values():
            all_concepts.extend(self._extract_concepts(episode.description))

        from collections import Counter
        concept_counts = Counter(all_concepts)
        return [concept for concept, count in concept_counts.most_common(10)]

    def _analyze_semantic_network(self, network: nx.Graph) -> Dict[str, Any]:
        """Analyze the structure of the semantic network."""
        if not network or network.number_of_nodes() == 0:
            return {}

        # Calculate network metrics
        try:
            centrality = nx.degree_centrality(network)
            clustering = nx.clustering(network)

            return {
                "total_concepts": network.number_of_nodes(),
                "total_connections": network.number_of_edges(),
                "most_central_concepts": sorted(centrality.items(),
                                              key=lambda x: x[1], reverse=True)[:5],
                "average_clustering": np.mean(list(clustering.values())) if clustering else 0,
                "network_density": nx.density(network)
            }
        except Exception as e:
            logger.error(f"Error analyzing semantic network: {e}")
            return {"error": "Could not analyze network structure"}

    async def _promote_memory(self, user_id: str, episode: EpisodicMemory, consolidation_type: MemoryConsolidationType):
        """Promote a memory to a higher consolidation level."""
        logger.info(f"Promoting memory {episode.episode_id} to {consolidation_type.value}")

        # Update memory importance
        episode.significance_score = min(episode.significance_score + 0.1, 1.0)

        # Update statistics
        self.memory_stats['consolidations_performed'] += 1

    async def _consider_forgetting(self, user_id: str, episode_id: str, importance_score: float):
        """Consider forgetting a low-importance memory."""
        if importance_score < 0.2:  # Very low importance
            # Move to "forgotten" state (don't actually delete for safety)
            episode = self.episodic_memories[user_id].get(episode_id)
            if episode:
                episode.significance_score = max(episode.significance_score - 0.05, 0.0)
                logger.info(f"Reducing importance of memory {episode_id} (score: {importance_score})")

                if episode.significance_score <= 0.05:
                    # Mark as forgotten but keep for potential recovery
                    self.memory_stats['forgotten_memories'] += 1

    def _create_memory_key(self, user_id: str, memory_type: MemoryType, memory_id: str) -> str:
        """Create Redis key for memory storage."""
        return f"memory:{memory_type.value}:{user_id}:{memory_id}"

    def _create_user_profile_key(self, user_id: str) -> str:
        """Create Redis key for user profile."""
        return f"profile:{user_id}"
    
    def _create_embedding_key(self, user_id: str, memory_type: MemoryType) -> str:
        """Create Redis key for embeddings."""
        return f"embeddings:{memory_type.value}:{user_id}"
    
    def store_personal_memory(
        self,
        user_id: str,
        content: str,
        interaction_type: InteractionType,
        emotion: Optional[EmotionType] = None,
        context: Optional[Dict[str, Any]] = None,
        importance: Optional[float] = None
    ) -> PersonalMemory:
        """Store a personal memory entry for a specific user."""
        
        # Create content hash for ID generation
        content_hash = hashlib.md5(content.encode()).hexdigest()[:8]
        memory_id = f"personal_{user_id}_{content_hash}_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}"
        
        # Calculate importance if not provided
        if importance is None:
            user_profile = self.get_user_profile(user_id)
            user_frequency = user_profile.interaction_count if user_profile else 0
            importance = calculate_memory_importance(
                emotion, interaction_type, user_frequency, len(content)
            )
        
        # Create personal memory entry
        memory = PersonalMemory(
            id=memory_id,
            user_id=user_id,
            memory_type=MemoryType.PERSONAL,
            interaction_type=interaction_type,
            content=content,
            context=context or {},
            emotion=emotion,
            importance=importance
        )
        
        # Store in persistent storage
        self.storage_service.store_memory(memory)
        
        # Store embedding for similarity search
        embedding = self._get_embedding(content)
        if self.redis_client:
            embedding_key = self._create_embedding_key(user_id, MemoryType.PERSONAL)

            # Store embedding with memory ID
            embedding_data = {
                'memory_id': memory_id,
                'embedding': embedding.tolist(),
                'importance': importance,
                'created_at': memory.created_at.isoformat()
            }

            self.redis_client.lpush(embedding_key, json.dumps(embedding_data))
            self.redis_client.expire(embedding_key, self.memory_ttl)
        
        # Update user profile
        self._update_user_profile(user_id, interaction_type, emotion)
        
        return memory
    
    def store_universal_memory(
        self,
        content: str,
        interaction_type: InteractionType,
        topic_tags: Optional[List[str]] = None,
        emotion: Optional[EmotionType] = None,
        context: Optional[Dict[str, Any]] = None,
        source_user_id: Optional[str] = None
    ) -> UniversalMemory:
        """Store a universal memory entry shared across all users."""
        
        # Create content hash for ID generation
        content_hash = hashlib.md5(content.encode()).hexdigest()[:8]
        memory_id = f"universal_{content_hash}_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}"
        
        # Check if similar universal memory already exists
        existing_memory = self._find_similar_universal_memory(content)
        if existing_memory:
            # Update existing memory
            existing_memory.source_count += 1
            existing_memory.last_accessed = datetime.now(timezone.utc)
            existing_memory.access_count += 1
            
            # Update in Redis
            memory_key = self._create_memory_key("", MemoryType.UNIVERSAL, existing_memory.id)
            self.redis_client.setex(
                memory_key,
                self.memory_ttl,
                existing_memory.json()
            )
            
            return existing_memory
        
        # Create new universal memory entry
        memory = UniversalMemory(
            id=memory_id,
            user_id="",  # Empty for universal memory
            memory_type=MemoryType.UNIVERSAL,
            interaction_type=interaction_type,
            content=content,
            context=context or {},
            emotion=emotion,
            topic_tags=topic_tags or [],
            source_count=1
        )
        
        # Store in Redis
        memory_key = self._create_memory_key("", MemoryType.UNIVERSAL, memory_id)
        self.redis_client.setex(
            memory_key,
            self.memory_ttl,
            memory.json()
        )
        
        # Store embedding for similarity search
        embedding = self._get_embedding(content)
        embedding_key = self._create_embedding_key("", MemoryType.UNIVERSAL)
        
        embedding_data = {
            'memory_id': memory_id,
            'embedding': embedding.tolist(),
            'global_relevance': memory.global_relevance,
            'topic_tags': topic_tags or [],
            'created_at': memory.created_at.isoformat()
        }
        
        self.redis_client.lpush(embedding_key, json.dumps(embedding_data))
        self.redis_client.expire(embedding_key, self.memory_ttl)
        
        return memory
    
    def retrieve_relevant_memories(
        self,
        user_id: str,
        query: str,
        memory_type: MemoryType = MemoryType.PERSONAL,
        limit: int = 10,
        min_similarity: float = 0.3
    ) -> List[MemoryEntry]:
        """Retrieve memories relevant to a query using semantic similarity."""
        
        query_embedding = self._get_embedding(query)
        relevant_memories = []
        
        # Get embeddings for the specified memory type
        embedding_key = self._create_embedding_key(user_id, memory_type)
        embedding_data_list = self.redis_client.lrange(embedding_key, 0, -1)
        
        similarities = []
        
        for embedding_data_str in embedding_data_list:
            try:
                embedding_data = json.loads(embedding_data_str)
                memory_embedding = np.array(embedding_data['embedding'])
                
                # Calculate similarity
                similarity = cosine_similarity(
                    [query_embedding], [memory_embedding]
                )[0][0]
                
                # Apply importance/relevance weighting
                weight = embedding_data.get('importance', 0.5)
                if memory_type == MemoryType.UNIVERSAL:
                    weight = embedding_data.get('global_relevance', 0.5)
                
                weighted_similarity = similarity * weight
                
                if weighted_similarity >= min_similarity:
                    similarities.append((embedding_data['memory_id'], weighted_similarity))
                    
            except (json.JSONDecodeError, KeyError) as e:
                continue
        
        # Sort by similarity and get top results
        similarities.sort(key=lambda x: x[1], reverse=True)
        top_memory_ids = [mem_id for mem_id, _ in similarities[:limit]]
        
        # Retrieve full memory objects
        for memory_id in top_memory_ids:
            memory_key = self._create_memory_key(user_id, memory_type, memory_id)
            memory_data = self.redis_client.get(memory_key)
            
            if memory_data:
                try:
                    memory_dict = json.loads(memory_data)
                    if memory_type == MemoryType.PERSONAL:
                        memory = PersonalMemory(**memory_dict)
                    else:
                        memory = UniversalMemory(**memory_dict)
                    
                    # Update access statistics
                    memory.last_accessed = datetime.now(timezone.utc)
                    memory.access_count += 1
                    
                    # Store updated memory
                    self.redis_client.setex(
                        memory_key,
                        self.memory_ttl,
                        memory.json()
                    )
                    
                    relevant_memories.append(memory)
                    
                except Exception as e:
                    continue
        
        return relevant_memories
    
    def get_user_profile(self, user_id: str) -> Optional[UserProfile]:
        """Get user profile from persistent storage."""
        return self.storage_service.retrieve_user_profile(user_id)
    
    def create_user_profile(self, user_id: str, name: Optional[str] = None) -> UserProfile:
        """Create a new user profile."""
        profile = UserProfile(
            user_id=user_id,
            name=name
        )

        self.storage_service.store_user_profile(profile)
        return profile
    
    def _update_user_profile(
        self,
        user_id: str,
        interaction_type: InteractionType,
        emotion: Optional[EmotionType] = None
    ):
        """Update user profile based on interaction."""
        profile = self.get_user_profile(user_id)
        
        if not profile:
            profile = self.create_user_profile(user_id)
        
        # Update interaction count
        profile.interaction_count += 1
        profile.last_interaction = datetime.now(timezone.utc)
        
        # Update emotional patterns
        if emotion:
            current_weight = profile.emotional_patterns.get(emotion, 0.0)
            profile.emotional_patterns[emotion] = current_weight + settings.learning_rate
        
        # Normalize emotional patterns
        total_weight = sum(profile.emotional_patterns.values())
        if total_weight > 0:
            for emotion_type in profile.emotional_patterns:
                profile.emotional_patterns[emotion_type] /= total_weight
        
        # Store updated profile
        profile_key = self._create_user_profile_key(user_id)
        self.redis_client.setex(
            profile_key,
            self.memory_ttl,
            profile.json()
        )
    
    def _find_similar_universal_memory(self, content: str, similarity_threshold: float = 0.8) -> Optional[UniversalMemory]:
        """Find similar universal memory to avoid duplicates."""
        content_embedding = self._get_embedding(content)
        embedding_key = self._create_embedding_key("", MemoryType.UNIVERSAL)
        embedding_data_list = self.redis_client.lrange(embedding_key, 0, -1)
        
        for embedding_data_str in embedding_data_list:
            try:
                embedding_data = json.loads(embedding_data_str)
                memory_embedding = np.array(embedding_data['embedding'])
                
                similarity = cosine_similarity(
                    [content_embedding], [memory_embedding]
                )[0][0]
                
                if similarity >= similarity_threshold:
                    # Retrieve the full memory
                    memory_key = self._create_memory_key("", MemoryType.UNIVERSAL, embedding_data['memory_id'])
                    memory_data = self.redis_client.get(memory_key)
                    
                    if memory_data:
                        memory_dict = json.loads(memory_data)
                        return UniversalMemory(**memory_dict)
                        
            except (json.JSONDecodeError, KeyError):
                continue
        
        return None
    
    def create_contextual_memory(
        self,
        conversation_id: str,
        user_id: str,
        query: str
    ) -> ContextualMemory:
        """Create contextual memory for a conversation."""
        
        # Retrieve relevant personal and universal memories
        personal_memories = self.retrieve_relevant_memories(
            user_id, query, MemoryType.PERSONAL, limit=5
        )
        
        universal_memories = self.retrieve_relevant_memories(
            user_id, query, MemoryType.UNIVERSAL, limit=3
        )
        
        # Combine memories
        relevant_memories = personal_memories + universal_memories
        
        # Analyze emotional context
        emotional_context = {}
        for memory in relevant_memories:
            if memory.emotion:
                current_weight = emotional_context.get(memory.emotion, 0.0)
                emotional_context[memory.emotion] = current_weight + memory.importance
        
        # Normalize emotional context
        total_weight = sum(emotional_context.values())
        if total_weight > 0:
            for emotion in emotional_context:
                emotional_context[emotion] /= total_weight
        
        # Extract topic context
        topic_context = []
        for memory in relevant_memories:
            if hasattr(memory, 'topic_tags'):
                topic_context.extend(memory.topic_tags)
        
        # Remove duplicates and limit
        topic_context = list(set(topic_context))[:10]
        
        # Create contextual memory
        contextual_memory = ContextualMemory(
            conversation_id=conversation_id,
            user_id=user_id,
            relevant_memories=relevant_memories,
            emotional_context=emotional_context,
            topic_context=topic_context
        )
        
        return contextual_memory
    
    def cleanup_expired_memories(self):
        """Clean up expired memories (Redis handles TTL automatically)."""
        # This is mainly for logging and monitoring
        # Redis automatically removes expired keys
        pass
    
    def get_memory_statistics(self, user_id: str) -> Dict[str, Any]:
        """Get memory statistics for a user."""
        personal_count = len(self.redis_client.keys(f"memory:personal:{user_id}:*"))
        universal_count = len(self.redis_client.keys("memory:universal:*"))
        
        profile = self.get_user_profile(user_id)
        
        return {
            "personal_memories": personal_count,
            "universal_memories": universal_count,
            "total_interactions": profile.interaction_count if profile else 0,
            "last_interaction": profile.last_interaction.isoformat() if profile else None,
            "emotional_patterns": profile.emotional_patterns if profile else {}
        }

# Import the utility function
from models import calculate_memory_importance

# Backward compatibility alias
MemoryService = AdvancedMemoryService