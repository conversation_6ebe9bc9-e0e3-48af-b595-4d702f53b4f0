"""
Next-Generation AI Companion System - Main Application
Integrates advanced memory, emotional intelligence, and performance optimizations.
"""

import asyncio
import logging
import time
import sys
from datetime import datetime, timezone
from typing import Dict, Any, Optional

import gradio as gr
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware

# Import next-generation services
from next_gen_conversation_service import NextGenConversationService
from advanced_emotional_intelligence import AdvancedEmotionalIntelligence
from memory_service import AdvancedMemoryService
from gemini_service import GeminiService
from config import settings, validate_settings

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NextGenAICompanion:
    """Next-generation AI companion application with advanced capabilities."""
    
    def __init__(self):
        """Initialize the next-generation AI companion."""
        self.conversation_service = None
        self.memory_service = None
        self.emotional_intelligence = None
        self.gemini_service = None
        
        # Performance tracking
        self.startup_time = 0.0
        self.total_interactions = 0
        self.system_ready = False
        
        # Gradio interface
        self.gradio_app = None
        self.fastapi_app = None
    
    async def initialize(self):
        """Initialize all services asynchronously."""
        start_time = time.time()
        logger.info("🚀 Initializing Next-Generation AI Companion System...")
        
        try:
            # Validate configuration
            validate_settings()
            logger.info("✅ Configuration validated")
            
            # Initialize core services
            logger.info("🧠 Initializing AI services...")
            self.gemini_service = GeminiService()
            self.memory_service = AdvancedMemoryService()
            self.emotional_intelligence = AdvancedEmotionalIntelligence(self.gemini_service)
            self.conversation_service = NextGenConversationService()
            
            # Test services
            await self._test_services()
            
            # Initialize interfaces
            self._create_gradio_interface()
            self._create_fastapi_app()
            
            self.startup_time = time.time() - start_time
            self.system_ready = True
            
            logger.info(f"🎉 System initialized successfully in {self.startup_time:.2f} seconds")
            logger.info("🌟 Next-Generation AI Companion is ready!")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize system: {e}")
            raise
    
    async def _test_services(self):
        """Test all services to ensure they're working."""
        try:
            # Test Gemini service
            test_response = await self.gemini_service.generate_response_async("Hello, this is a test.")
            if not test_response:
                raise Exception("Gemini service test failed")
            
            # Test memory service
            test_memory = await self.memory_service.create_episodic_memory(
                user_id="test_user",
                title="System Test",
                description="Testing memory service functionality",
                emotional_context={'primary_emotion': 'neutral', 'intensity': 0.5}
            )
            if not test_memory:
                raise Exception("Memory service test failed")
            
            # Test emotional intelligence
            test_insight = await self.emotional_intelligence.analyze_emotional_state(
                "test_user", "I'm feeling good today", {}
            )
            if not test_insight:
                raise Exception("Emotional intelligence test failed")
            
            logger.info("✅ All services tested successfully")
            
        except Exception as e:
            logger.error(f"❌ Service testing failed: {e}")
            raise
    
    def _create_gradio_interface(self):
        """Create the Gradio interface with enhanced features."""
        
        async def chat_interface(message: str, history: list, user_id: str = "demo_user") -> tuple:
            """Enhanced chat interface with comprehensive response."""
            if not message.strip():
                return history, ""
            
            try:
                # Process message through next-gen conversation service
                response_data = await self.conversation_service.process_message(
                    user_id=user_id,
                    message=message,
                    context={"interface": "gradio", "timestamp": datetime.now(timezone.utc).isoformat()}
                )
                
                # Extract response and metadata
                ai_response = response_data.get("response", "I'm having trouble responding right now.")
                emotional_insight = response_data.get("emotional_insight", {})
                therapeutic_info = response_data.get("therapeutic_response", {})
                memory_info = response_data.get("memory_insights", {})
                metrics = response_data.get("metrics", {})
                
                # Create enhanced response with metadata
                enhanced_response = ai_response
                
                # Add emotional context if significant
                if emotional_insight.get("intensity", 0) > 0.7:
                    emotion = emotional_insight.get("primary_emotion", "neutral")
                    enhanced_response += f"\n\n*I sense you're feeling {emotion} quite strongly right now.*"
                
                # Add performance info for demo
                if response_data.get("cached"):
                    enhanced_response += "\n\n💨 *Instant response (cached)*"
                else:
                    processing_time = response_data.get("processing_time", 0)
                    enhanced_response += f"\n\n⚡ *Response time: {processing_time:.2f}s*"
                
                # Update history
                history.append([message, enhanced_response])
                self.total_interactions += 1
                
                return history, ""
                
            except Exception as e:
                logger.error(f"Error in chat interface: {e}")
                error_response = "I apologize, but I'm experiencing some technical difficulties. Please try again."
                history.append([message, error_response])
                return history, ""
        
        def get_system_status():
            """Get current system status and performance metrics."""
            if not self.system_ready:
                return "🔄 System initializing..."
            
            performance = self.conversation_service.get_system_performance()
            memory_stats = self.memory_service.memory_stats
            
            status = f"""
            🟢 **System Status: READY**
            
            **Performance Metrics:**
            - Active Conversations: {performance['active_conversations']}
            - Average Response Time: {performance['average_response_time']:.3f}s
            - Cache Hit Rate: {performance['cache_hit_rate']:.1%}
            - Total Interactions: {self.total_interactions}
            
            **Memory System:**
            - Episodic Memories: {memory_stats['episodic_count']}
            - Semantic Nodes: {memory_stats['semantic_nodes']}
            - Consolidations: {memory_stats['consolidations_performed']}
            
            **System Info:**
            - Startup Time: {self.startup_time:.2f}s
            - Uptime: {time.time() - (time.time() - self.startup_time):.0f}s
            - Environment: {settings.environment}
            """
            return status
        
        def get_conversation_insights(user_id: str = "demo_user"):
            """Get insights about the current conversation."""
            try:
                insights = self.conversation_service.get_conversation_insights(user_id)
                
                if "error" in insights:
                    return "No active conversation found."
                
                return f"""
                **Conversation Insights for {user_id}:**
                
                - Total Interactions: {insights['total_interactions']}
                - Emotional Patterns: {insights['emotional_patterns']}
                - Average Emotional Intensity: {insights['average_emotional_intensity']:.2f}
                - Therapeutic Alliance: {insights['therapeutic_alliance_strength']:.2f}
                - Current Topics: {', '.join(insights['current_topics'][:5])}
                - Cache Hit Rate: {insights['performance_metrics']['cache_hit_rate']:.1%}
                """
                
            except Exception as e:
                return f"Error retrieving insights: {e}"
        
        async def get_memory_insights(user_id: str = "demo_user"):
            """Get memory insights for the user."""
            try:
                insights = await self.memory_service.get_memory_insights(user_id)
                
                if "message" in insights:
                    return insights["message"]
                
                return f"""
                **Memory Analysis for {user_id}:**
                
                - Total Episodes: {insights['total_episodes']}
                - Key Themes: {', '.join(insights['key_themes'][:5])}
                - Emotional Patterns: {insights['emotional_patterns']}
                - Semantic Network: {insights['semantic_insights']}
                """
                
            except Exception as e:
                return f"Error retrieving memory insights: {e}"
        
        # Create Gradio interface
        with gr.Blocks(
            title="🧠 Next-Gen AI Companion",
            theme=gr.themes.Soft(),
            css="""
            .gradio-container {
                max-width: 1200px !important;
            }
            .chat-container {
                height: 600px !important;
            }
            """
        ) as interface:
            
            gr.Markdown("""
            # 🧠 Next-Generation AI Companion
            
            **Advanced Features:**
            - 🧠 Neural-symbolic memory architecture
            - 💝 Advanced emotional intelligence
            - 🔬 Psychological profiling and therapeutic responses
            - ⚡ High-performance optimization
            - 🛡️ Crisis detection and intervention
            """)
            
            with gr.Row():
                with gr.Column(scale=2):
                    chatbot = gr.Chatbot(
                        label="Conversation",
                        height=500,
                        show_label=True,
                        container=True,
                        elem_classes=["chat-container"]
                    )
                    
                    with gr.Row():
                        msg = gr.Textbox(
                            label="Your message",
                            placeholder="Share what's on your mind...",
                            lines=2,
                            scale=4
                        )
                        send_btn = gr.Button("Send", variant="primary", scale=1)
                    
                    user_id_input = gr.Textbox(
                        label="User ID (for demo)",
                        value="demo_user",
                        placeholder="Enter your user ID"
                    )
                
                with gr.Column(scale=1):
                    gr.Markdown("### 📊 System Dashboard")
                    
                    status_display = gr.Markdown(value=get_system_status())
                    refresh_status_btn = gr.Button("🔄 Refresh Status")
                    
                    gr.Markdown("### 🧠 Insights")
                    
                    insights_display = gr.Markdown(value="Click buttons below to view insights")
                    
                    with gr.Row():
                        conv_insights_btn = gr.Button("💬 Conversation")
                        memory_insights_btn = gr.Button("🧠 Memory")
            
            # Event handlers
            def submit_message(message, history, user_id):
                return asyncio.run(chat_interface(message, history, user_id))
            
            send_btn.click(
                submit_message,
                inputs=[msg, chatbot, user_id_input],
                outputs=[chatbot, msg]
            )
            
            msg.submit(
                submit_message,
                inputs=[msg, chatbot, user_id_input],
                outputs=[chatbot, msg]
            )
            
            refresh_status_btn.click(
                get_system_status,
                outputs=status_display
            )
            
            conv_insights_btn.click(
                get_conversation_insights,
                inputs=user_id_input,
                outputs=insights_display
            )
            
            memory_insights_btn.click(
                lambda user_id: asyncio.run(get_memory_insights(user_id)),
                inputs=user_id_input,
                outputs=insights_display
            )
        
        self.gradio_app = interface
    
    def _create_fastapi_app(self):
        """Create FastAPI app for API access."""
        app = FastAPI(
            title="Next-Gen AI Companion API",
            description="Advanced AI companion with neural-symbolic memory and emotional intelligence",
            version="2.0.0"
        )
        
        # Add CORS middleware
        app.add_middleware(
            CORSMiddleware,
            allow_origins=settings.get_cors_origins(),
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        @app.post("/chat")
        async def chat_endpoint(
            message: str,
            user_id: str,
            context: Optional[Dict[str, Any]] = None
        ):
            """Chat endpoint for API access."""
            try:
                response = await self.conversation_service.process_message(
                    user_id=user_id,
                    message=message,
                    context=context or {}
                )
                return response
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        @app.get("/health")
        async def health_check():
            """Health check endpoint."""
            return {
                "status": "healthy" if self.system_ready else "initializing",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "uptime": time.time() - (time.time() - self.startup_time)
            }
        
        @app.get("/metrics")
        async def get_metrics():
            """Get system performance metrics."""
            return self.conversation_service.get_system_performance()
        
        self.fastapi_app = app
    
    async def run(self, share: bool = False, server_port: int = None):
        """Run the next-generation AI companion system."""
        port = server_port or settings.gradio_port
        
        logger.info(f"🌐 Starting Next-Gen AI Companion on port {port}")
        logger.info(f"🔗 Access the interface at: http://localhost:{port}")
        
        if share:
            logger.info("🌍 Creating public share link...")
        
        # Launch Gradio interface
        self.gradio_app.launch(
            server_name="0.0.0.0",
            server_port=port,
            share=share,
            show_error=True,
            quiet=False
        )

async def main():
    """Main entry point for the next-generation AI companion."""
    try:
        # Create and initialize the application
        app = NextGenAICompanion()
        await app.initialize()
        
        # Run the application
        await app.run(share=False)
        
    except KeyboardInterrupt:
        logger.info("👋 Shutting down gracefully...")
    except Exception as e:
        logger.error(f"❌ Application error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
