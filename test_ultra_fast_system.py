"""
Test script for the Ultra-Fast AI Companion System.
Benchmarks performance and validates sub-second response times.
"""

import asyncio
import time
import statistics
import logging
from typing import List, Dict, Any

from ultra_fast_conversation_service import UltraFastConversationService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UltraFastSystemTester:
    """Comprehensive tester for ultra-fast AI companion system."""
    
    def __init__(self):
        self.conversation_service = UltraFastConversationService()
        self.test_results: Dict[str, List[float]] = {}
    
    async def run_performance_benchmark(self) -> Dict[str, Any]:
        """Run comprehensive performance benchmark."""
        print("🚀 Starting Ultra-Fast AI Companion Performance Benchmark...")
        
        # Test scenarios
        test_scenarios = [
            {
                "name": "Cold Start",
                "user_id": "benchmark_user_1",
                "messages": [
                    "Hi there! How are you today?",
                    "I'm feeling a bit stressed about work.",
                    "Thank you for listening to me."
                ]
            },
            {
                "name": "Warm Cache",
                "user_id": "benchmark_user_1",  # Same user for cache hits
                "messages": [
                    "Hi there! How are you today?",  # Should hit cache
                    "I'm feeling much better now.",
                    "What do you think about that?"
                ]
            },
            {
                "name": "Emotional Support",
                "user_id": "benchmark_user_2",
                "messages": [
                    "I'm feeling really anxious about my presentation tomorrow.",
                    "I keep thinking I'm going to mess everything up.",
                    "Do you have any advice for managing anxiety?"
                ]
            },
            {
                "name": "Parallel Processing",
                "user_id": "benchmark_user_3",
                "messages": [
                    "I had the most amazing day today!",
                    "Everything went perfectly at work.",
                    "I feel like I'm finally making progress in my career."
                ]
            }
        ]
        
        benchmark_results = {}
        
        for scenario in test_scenarios:
            print(f"\n📊 Testing: {scenario['name']}")
            
            # Warm up user cache if needed
            if scenario['name'] == "Warm Cache":
                await self.conversation_service.warm_up_user(scenario['user_id'])
                await asyncio.sleep(0.1)  # Allow preload to complete
            
            scenario_times = []
            
            for i, message in enumerate(scenario['messages']):
                print(f"  Message {i+1}: {message[:50]}...")
                
                start_time = time.time()
                response, processing_time, metrics = await self.conversation_service.process_message_ultra_fast(
                    user_id=scenario['user_id'],
                    message=message
                )
                total_time = time.time() - start_time
                
                scenario_times.append(processing_time)
                
                print(f"    Response ({processing_time:.3f}s): {response[:80]}...")
                print(f"    Cache hit: {metrics.get('cache_hit', False)}")
                print(f"    Source: {metrics.get('source', 'unknown')}")
                
                # Brief pause between messages
                await asyncio.sleep(0.1)
            
            benchmark_results[scenario['name']] = {
                'response_times': scenario_times,
                'average_time': statistics.mean(scenario_times),
                'min_time': min(scenario_times),
                'max_time': max(scenario_times),
                'median_time': statistics.median(scenario_times)
            }
        
        return benchmark_results
    
    async def test_concurrent_users(self, num_users: int = 5) -> Dict[str, Any]:
        """Test system performance with concurrent users."""
        print(f"\n🔄 Testing concurrent users ({num_users} users)...")
        
        async def simulate_user_conversation(user_id: str) -> List[float]:
            """Simulate a conversation for one user."""
            messages = [
                f"Hello, I'm user {user_id}",
                "How are you doing today?",
                "I wanted to talk about my feelings.",
                "Thank you for the conversation!"
            ]
            
            times = []
            for message in messages:
                start_time = time.time()
                response, processing_time, metrics = await self.conversation_service.process_message_ultra_fast(
                    user_id=f"concurrent_user_{user_id}",
                    message=message
                )
                times.append(processing_time)
                await asyncio.sleep(0.05)  # Small delay between messages
            
            return times
        
        # Run concurrent conversations
        start_time = time.time()
        tasks = [simulate_user_conversation(str(i)) for i in range(num_users)]
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        # Analyze results
        all_times = [time for user_times in results for time in user_times]
        
        return {
            'total_concurrent_time': total_time,
            'num_users': num_users,
            'total_messages': len(all_times),
            'average_response_time': statistics.mean(all_times),
            'min_response_time': min(all_times),
            'max_response_time': max(all_times),
            'median_response_time': statistics.median(all_times),
            'messages_per_second': len(all_times) / total_time
        }
    
    async def test_cache_effectiveness(self) -> Dict[str, Any]:
        """Test cache hit rates and effectiveness."""
        print("\n💾 Testing cache effectiveness...")
        
        # Clear caches to start fresh
        self.conversation_service.clear_caches()
        
        test_user = "cache_test_user"
        repeated_message = "How are you feeling today?"
        
        # First request (cache miss)
        print("  First request (should be cache miss)...")
        response1, time1, metrics1 = await self.conversation_service.process_message_ultra_fast(
            user_id=test_user,
            message=repeated_message
        )
        
        # Second request (should be cache hit)
        print("  Second request (should be cache hit)...")
        response2, time2, metrics2 = await self.conversation_service.process_message_ultra_fast(
            user_id=test_user,
            message=repeated_message
        )
        
        # Third request (should be cache hit)
        print("  Third request (should be cache hit)...")
        response3, time3, metrics3 = await self.conversation_service.process_message_ultra_fast(
            user_id=test_user,
            message=repeated_message
        )
        
        return {
            'first_request': {
                'time': time1,
                'cache_hit': metrics1.get('cache_hit', False),
                'source': metrics1.get('source', 'unknown')
            },
            'second_request': {
                'time': time2,
                'cache_hit': metrics2.get('cache_hit', False),
                'source': metrics2.get('source', 'unknown')
            },
            'third_request': {
                'time': time3,
                'cache_hit': metrics3.get('cache_hit', False),
                'source': metrics3.get('source', 'unknown')
            },
            'cache_speedup': time1 / time2 if time2 > 0 else 0,
            'consistency_check': response1 == response2 == response3
        }
    
    async def test_error_handling(self) -> Dict[str, Any]:
        """Test error handling and fallback mechanisms."""
        print("\n🛡️ Testing error handling...")
        
        # Test with various edge cases
        edge_cases = [
            "",  # Empty message
            "a" * 1000,  # Very long message
            "🎉🎊🎈" * 50,  # Emoji spam
            "What's 2+2?" * 100,  # Repetitive content
        ]
        
        results = []
        for i, message in enumerate(edge_cases):
            print(f"  Testing edge case {i+1}...")
            try:
                response, processing_time, metrics = await self.conversation_service.process_message_ultra_fast(
                    user_id="edge_case_user",
                    message=message
                )
                results.append({
                    'case': f"edge_case_{i+1}",
                    'success': True,
                    'processing_time': processing_time,
                    'response_length': len(response),
                    'source': metrics.get('source', 'unknown')
                })
            except Exception as e:
                results.append({
                    'case': f"edge_case_{i+1}",
                    'success': False,
                    'error': str(e)
                })
        
        return {
            'edge_cases_tested': len(edge_cases),
            'successful_cases': sum(1 for r in results if r['success']),
            'failed_cases': sum(1 for r in results if not r['success']),
            'results': results
        }
    
    def print_benchmark_summary(self, results: Dict[str, Any]):
        """Print a comprehensive benchmark summary."""
        print("\n" + "="*80)
        print("🎯 ULTRA-FAST AI COMPANION BENCHMARK RESULTS")
        print("="*80)
        
        if 'benchmark_results' in results:
            print("\n📊 Performance Benchmark:")
            for scenario, data in results['benchmark_results'].items():
                print(f"  {scenario}:")
                print(f"    Average: {data['average_time']:.3f}s")
                print(f"    Min: {data['min_time']:.3f}s")
                print(f"    Max: {data['max_time']:.3f}s")
                print(f"    Median: {data['median_time']:.3f}s")
        
        if 'concurrent_results' in results:
            print(f"\n🔄 Concurrent Users Test:")
            cr = results['concurrent_results']
            print(f"    Users: {cr['num_users']}")
            print(f"    Total messages: {cr['total_messages']}")
            print(f"    Average response time: {cr['average_response_time']:.3f}s")
            print(f"    Messages per second: {cr['messages_per_second']:.1f}")
        
        if 'cache_results' in results:
            print(f"\n💾 Cache Effectiveness:")
            cr = results['cache_results']
            print(f"    First request: {cr['first_request']['time']:.3f}s (cache: {cr['first_request']['cache_hit']})")
            print(f"    Second request: {cr['second_request']['time']:.3f}s (cache: {cr['second_request']['cache_hit']})")
            print(f"    Cache speedup: {cr['cache_speedup']:.1f}x")
        
        if 'error_results' in results:
            print(f"\n🛡️ Error Handling:")
            er = results['error_results']
            print(f"    Edge cases tested: {er['edge_cases_tested']}")
            print(f"    Successful: {er['successful_cases']}")
            print(f"    Failed: {er['failed_cases']}")
        
        # Performance metrics
        metrics = self.conversation_service.get_performance_metrics()
        print(f"\n⚡ System Metrics:")
        print(f"    Total requests: {metrics['total_requests']}")
        print(f"    Average response time: {metrics['average_response_time']:.3f}s")
        print(f"    Active conversations: {metrics['active_conversations']}")
        print(f"    Cache sizes: {metrics['cache_sizes']}")
        
        print("\n" + "="*80)

async def main():
    """Run the ultra-fast system benchmark."""
    tester = UltraFastSystemTester()
    
    try:
        # Run all tests
        results = {}
        
        # Performance benchmark
        results['benchmark_results'] = await tester.run_performance_benchmark()
        
        # Concurrent users test
        results['concurrent_results'] = await tester.test_concurrent_users(num_users=3)
        
        # Cache effectiveness test
        results['cache_results'] = await tester.test_cache_effectiveness()
        
        # Error handling test
        results['error_results'] = await tester.test_error_handling()
        
        # Print comprehensive summary
        tester.print_benchmark_summary(results)
        
        # Check if we achieved sub-second performance
        avg_time = results['benchmark_results']['Cold Start']['average_time']
        if avg_time < 1.0:
            print(f"\n🎉 SUCCESS: Achieved sub-second performance! ({avg_time:.3f}s average)")
        else:
            print(f"\n⚠️ Target not met: {avg_time:.3f}s average (target: <1.0s)")
        
        return results
        
    except Exception as e:
        logger.error(f"Benchmark failed: {e}")
        return None
    finally:
        await tester.conversation_service.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
