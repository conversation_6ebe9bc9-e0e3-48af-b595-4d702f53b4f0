"""
AI Companion with Login System for Private Memory and Conversation Persistence.
Each user gets their own private memory and conversation history.
"""

import gradio as gr
import asyncio
import time
import json
import logging
import hashlib
import sqlite3
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import os

# Import your optimized services
from optimized_conversation_service import OptimizedConversationService
from optimized_memory_service import HighPerformanceMemoryService
from user_memory_manager import UserMemoryManager
from models import (
    MemoryEntry, MemoryType, InteractionType, EmotionType,
    UserProfile, EmotionalState
)
from config import settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UserAuthSystem:
    """Simple user authentication and session management."""
    
    def __init__(self):
        """Initialize the auth system."""
        self.db_path = "users.db"
        self.init_database()
        self.active_sessions = {}
    
    def init_database(self):
        """Initialize user database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                user_id TEXT PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                email TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP
            )
        ''')
        
        # Create user sessions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_sessions (
                session_id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (user_id)
            )
        ''')
        
        # Create conversations table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS conversations (
                conversation_id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                title TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_message_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (user_id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def hash_password(self, password: str) -> str:
        """Hash password for secure storage."""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def register_user(self, username: str, password: str, email: str = "") -> Tuple[bool, str]:
        """Register a new user."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if username exists
            cursor.execute("SELECT username FROM users WHERE username = ?", (username,))
            if cursor.fetchone():
                return False, "Username already exists"
            
            # Create new user
            user_id = f"user_{int(time.time())}_{hash(username) % 10000}"
            password_hash = self.hash_password(password)
            
            cursor.execute('''
                INSERT INTO users (user_id, username, password_hash, email)
                VALUES (?, ?, ?, ?)
            ''', (user_id, username, password_hash, email))
            
            conn.commit()
            conn.close()
            
            return True, f"User {username} registered successfully!"
            
        except Exception as e:
            logger.error(f"Registration error: {e}")
            return False, f"Registration failed: {str(e)}"
    
    def login_user(self, username: str, password: str) -> Tuple[bool, str, Optional[str]]:
        """Login user and create session."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Verify credentials
            password_hash = self.hash_password(password)
            cursor.execute('''
                SELECT user_id FROM users 
                WHERE username = ? AND password_hash = ?
            ''', (username, password_hash))
            
            result = cursor.fetchone()
            if not result:
                return False, "Invalid username or password", None
            
            user_id = result[0]
            
            # Create session
            session_id = f"session_{int(time.time())}_{hash(username) % 10000}"
            cursor.execute('''
                INSERT INTO user_sessions (session_id, user_id)
                VALUES (?, ?)
            ''', (session_id, user_id))
            
            # Update last login
            cursor.execute('''
                UPDATE users SET last_login = CURRENT_TIMESTAMP
                WHERE user_id = ?
            ''', (user_id,))
            
            conn.commit()
            conn.close()
            
            # Store in active sessions
            self.active_sessions[session_id] = {
                "user_id": user_id,
                "username": username,
                "login_time": time.time()
            }
            
            return True, f"Welcome back, {username}!", session_id
            
        except Exception as e:
            logger.error(f"Login error: {e}")
            return False, f"Login failed: {str(e)}", None
    
    def get_user_from_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get user info from session."""
        return self.active_sessions.get(session_id)
    
    def logout_user(self, session_id: str) -> bool:
        """Logout user and clear session."""
        try:
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            
            # Remove from database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM user_sessions WHERE session_id = ?", (session_id,))
            conn.commit()
            conn.close()
            
            return True
        except Exception as e:
            logger.error(f"Logout error: {e}")
            return False

class AICompanionWithLogin:
    """AI Companion with user authentication and private memory."""
    
    def __init__(self):
        """Initialize the AI companion with login system."""
        self.auth_system = UserAuthSystem()
        self.conversation_service = OptimizedConversationService()
        self.memory_service = HighPerformanceMemoryService()
        self.user_memory_manager = UserMemoryManager()

        # User-specific conversation tracking
        self.user_conversations = {}
        self.user_memories = {}
    
    def create_interface(self) -> gr.Blocks:
        """Create the Gradio interface with login system."""
        
        with gr.Blocks(
            title="AI Companion - Private Memory System",
            theme=gr.themes.Soft(),
            css="""
            .login-container {
                max-width: 400px;
                margin: 0 auto;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 10px;
                background: #f9f9f9;
            }
            .chat-container {
                display: none;
            }
            .logged-in .chat-container {
                display: block;
            }
            .logged-in .login-container {
                display: none;
            }
            """
        ) as interface:
            
            # State variables
            session_state = gr.State(value=None)
            user_state = gr.State(value=None)
            
            gr.Markdown("""
            # 🤖 AI Companion - Private Memory System
            
            **Your personal AI companion that remembers everything about you!**
            
            Each user gets their own private memory and conversation history.
            """)
            
            # Login/Register Section
            with gr.Column(elem_classes=["login-container"]) as login_section:
                gr.Markdown("## 🔐 Login or Register")
                
                with gr.Tabs():
                    with gr.Tab("Login"):
                        login_username = gr.Textbox(label="Username", placeholder="Enter your username")
                        login_password = gr.Textbox(label="Password", type="password", placeholder="Enter your password")
                        login_btn = gr.Button("Login", variant="primary")
                        login_message = gr.Textbox(label="Status", interactive=False)
                    
                    with gr.Tab("Register"):
                        reg_username = gr.Textbox(label="Username", placeholder="Choose a username")
                        reg_password = gr.Textbox(label="Password", type="password", placeholder="Choose a password")
                        reg_email = gr.Textbox(label="Email (optional)", placeholder="<EMAIL>")
                        register_btn = gr.Button("Register", variant="secondary")
                        register_message = gr.Textbox(label="Status", interactive=False)
            
            # Main Chat Interface (hidden until login)
            with gr.Column(elem_classes=["chat-container"], visible=False) as chat_section:
                
                # User info header
                user_info = gr.Markdown("### Welcome!")
                logout_btn = gr.Button("Logout", size="sm")
                
                # Chat interface
                with gr.Row():
                    with gr.Column(scale=3):
                        chatbot = gr.Chatbot(
                            label="💬 Your Personal AI Companion",
                            height=500,
                            type="messages"
                        )
                        
                        with gr.Row():
                            msg_input = gr.Textbox(
                                placeholder="Share anything with me - I'll remember it all!",
                                label="Your Message",
                                lines=2,
                                scale=4
                            )
                            send_btn = gr.Button("Send", variant="primary", scale=1)
                        
                        # Quick actions
                        with gr.Row():
                            memory_btn = gr.Button("📚 View My Memories", size="sm")
                            mood_btn = gr.Button("😊 How I'm Feeling", size="sm")
                            summary_btn = gr.Button("📝 Conversation Summary", size="sm")
                    
                    with gr.Column(scale=1):
                        gr.Markdown("### 🧠 Your Personal Memory")
                        memory_display = gr.JSON(label="Recent Memories")
                        
                        gr.Markdown("### 📊 Your Stats")
                        stats_display = gr.JSON(label="Conversation Stats")
            
            # Event handlers
            def handle_login(username: str, password: str):
                """Handle user login."""
                success, message, session_id = self.auth_system.login_user(username, password)
                
                if success:
                    user_info = self.auth_system.get_user_from_session(session_id)
                    return (
                        message,  # login_message
                        gr.update(visible=False),  # login_section
                        gr.update(visible=True),   # chat_section
                        session_id,  # session_state
                        user_info,   # user_state
                        f"### Welcome back, {username}! 🎉",  # user_info
                        [],  # chatbot (empty)
                        {},  # memory_display
                        {"total_conversations": 0, "total_memories": 0}  # stats_display
                    )
                else:
                    return (
                        message,  # login_message
                        gr.update(visible=True),   # login_section
                        gr.update(visible=False),  # chat_section
                        None,  # session_state
                        None,  # user_state
                        "### Welcome!",  # user_info
                        [],  # chatbot
                        {},  # memory_display
                        {}   # stats_display
                    )
            
            def handle_register(username: str, password: str, email: str):
                """Handle user registration."""
                success, message = self.auth_system.register_user(username, password, email)
                return message
            
            def handle_logout(session_id: str):
                """Handle user logout."""
                if session_id:
                    self.auth_system.logout_user(session_id)
                
                return (
                    gr.update(visible=True),   # login_section
                    gr.update(visible=False),  # chat_section
                    None,  # session_state
                    None,  # user_state
                    "### Welcome!",  # user_info
                    [],  # chatbot
                    {},  # memory_display
                    {}   # stats_display
                )
            
            async def handle_message(message: str, history: List, session_id: str, user_info: Dict):
                """Handle chat message with user-specific memory."""
                if not session_id or not user_info:
                    return history, "", {}, {}
                
                user_id = user_info["user_id"]
                
                # Get or create conversation for this user
                if user_id not in self.user_conversations:
                    conv_id = await self.conversation_service.start_conversation_fast(
                        user_id, user_info["username"]
                    )
                    self.user_conversations[user_id] = conv_id
                else:
                    conv_id = self.user_conversations[user_id]
                
                # Process message
                result = await self.conversation_service.process_message_fast(
                    conv_id, message, user_id
                )
                
                # Update history
                history.append({"role": "user", "content": message})
                history.append({"role": "assistant", "content": result["response"]})
                
                # Get user memories
                memories = await self.memory_service.search_memories_fast(
                    user_id, message, limit=5
                )
                
                memory_data = {
                    "recent_memories": [
                        {
                            "content": mem.content[:100] + "..." if len(mem.content) > 100 else mem.content,
                            "emotion": mem.emotion.value if mem.emotion else "neutral",
                            "importance": mem.importance
                        }
                        for mem in memories[:3]
                    ]
                }
                
                # Get stats
                stats = {
                    "total_messages": len(history),
                    "current_emotion": result.get("emotion", "neutral"),
                    "response_time": f"{result.get('processing_time', 0):.3f}s",
                    "memory_count": len(memories)
                }
                
                return history, "", memory_data, stats
            
            # Wire up events
            login_btn.click(
                fn=handle_login,
                inputs=[login_username, login_password],
                outputs=[login_message, login_section, chat_section, session_state, user_state, user_info, chatbot, memory_display, stats_display]
            )
            
            register_btn.click(
                fn=handle_register,
                inputs=[reg_username, reg_password, reg_email],
                outputs=[register_message]
            )
            
            logout_btn.click(
                fn=handle_logout,
                inputs=[session_state],
                outputs=[login_section, chat_section, session_state, user_state, user_info, chatbot, memory_display, stats_display]
            )
            
            send_btn.click(
                fn=handle_message,
                inputs=[msg_input, chatbot, session_state, user_state],
                outputs=[chatbot, msg_input, memory_display, stats_display]
            )
            
            msg_input.submit(
                fn=handle_message,
                inputs=[msg_input, chatbot, session_state, user_state],
                outputs=[chatbot, msg_input, memory_display, stats_display]
            )
        
        return interface
    
    def launch(self):
        """Launch the AI companion with login system."""
        interface = self.create_interface()
        
        print("\n" + "="*60)
        print("🔐 AI COMPANION WITH PRIVATE MEMORY SYSTEM")
        print("="*60)
        print("✨ Features:")
        print("  - User authentication and registration")
        print("  - Private memory for each user")
        print("  - Persistent conversation history")
        print("  - Personal emotion tracking")
        print("  - Secure user data storage")
        print(f"🌐 Access at: http://localhost:{settings.gradio_port}")
        print("="*60)
        
        interface.launch(
            server_name="0.0.0.0",
            server_port=settings.gradio_port,
            share=True,
            debug=False,
            show_error=True
        )

def main():
    """Launch the AI companion with login system."""
    app = AICompanionWithLogin()
    app.launch()

if __name__ == "__main__":
    main()
