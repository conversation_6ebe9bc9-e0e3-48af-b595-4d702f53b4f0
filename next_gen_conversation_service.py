"""
Next-Generation Conversation Service for AI Companion System.
Integrates advanced memory architecture, emotional intelligence, and performance optimizations.
"""

import asyncio
import uuid
import logging
import time
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque

# Configure logging
logger = logging.getLogger(__name__)

from models import (
    UserProfile, EmotionType, InteractionType, MemoryType, ContextualMemory,
    EmotionalState, EmpathyModel, EmotionalMemory
)
from memory_service import AdvancedMemoryService
from learning_service import LearningService
from gemini_service import GeminiService
from advanced_emotional_intelligence import AdvancedEmotionalIntelligence
from storage_service import PersistentStorageService
from config import settings

@dataclass
class ConversationMetrics:
    """Performance metrics for conversation processing."""
    response_time: float = 0.0
    memory_retrieval_time: float = 0.0
    emotional_analysis_time: float = 0.0
    ai_generation_time: float = 0.0
    total_processing_time: float = 0.0
    cache_hits: int = 0
    cache_misses: int = 0

@dataclass
class EnhancedConversationContext:
    """Enhanced context for ongoing conversation with advanced capabilities."""
    user_id: str
    conversation_id: str
    user_profile: Optional[UserProfile]
    conversation_history: List[Dict[str, Any]]
    contextual_memory: Optional[ContextualMemory]
    emotional_state: EmotionalState
    empathy_model: Optional[EmpathyModel]
    current_topics: List[str]
    psychological_profile: Optional[Any]  # PsychologicalProfile from advanced_emotional_intelligence
    therapeutic_alliance_strength: float = 0.5
    conversation_metrics: ConversationMetrics = field(default_factory=ConversationMetrics)
    last_activity: datetime = field(default_factory=lambda: datetime.now(timezone.utc))

class NextGenConversationService:
    """
    Next-generation conversation service with advanced AI capabilities.
    
    Features:
    - Neural-symbolic memory integration
    - Advanced emotional intelligence
    - Performance optimization with caching
    - Proactive mental health monitoring
    - Adaptive conversation strategies
    """
    
    def __init__(self):
        """Initialize the next-generation conversation service."""
        self.memory_service = AdvancedMemoryService()
        self.gemini_service = GeminiService()
        self.emotional_intelligence = AdvancedEmotionalIntelligence(self.gemini_service)
        self.learning_service = LearningService(self.memory_service, self.gemini_service)
        self.storage_service = PersistentStorageService()

        # Enhanced conversation management
        self.active_conversations: Dict[str, EnhancedConversationContext] = {}
        self.conversation_cache: Dict[str, Any] = {}
        self.response_cache: Dict[str, str] = {}
        
        # Performance optimization
        self.cache_hit_rate = 0.0
        self.average_response_time = 0.0
        self.total_conversations = 0
        
        # Conversation strategies
        self.conversation_strategies = self._initialize_conversation_strategies()
        
        # Background tasks
        self._start_background_tasks()
    
    def _initialize_conversation_strategies(self) -> Dict[str, Dict[str, Any]]:
        """Initialize adaptive conversation strategies."""
        return {
            "supportive": {
                "description": "Emotional support and validation",
                "techniques": ["active_listening", "validation", "empathy"],
                "response_style": "warm_supportive",
                "when_to_use": ["emotional_distress", "seeking_comfort"]
            },
            "therapeutic": {
                "description": "Therapeutic intervention and guidance",
                "techniques": ["cognitive_reframing", "mindfulness", "grounding"],
                "response_style": "professional_caring",
                "when_to_use": ["mental_health_concerns", "crisis_situations"]
            },
            "exploratory": {
                "description": "Encouraging self-discovery and insight",
                "techniques": ["open_questions", "reflection", "curiosity"],
                "response_style": "curious_encouraging",
                "when_to_use": ["personal_growth", "decision_making"]
            },
            "educational": {
                "description": "Information sharing and learning",
                "techniques": ["explanation", "examples", "guided_discovery"],
                "response_style": "informative_friendly",
                "when_to_use": ["seeking_information", "learning_goals"]
            },
            "casual": {
                "description": "Light, friendly conversation",
                "techniques": ["humor", "storytelling", "shared_interests"],
                "response_style": "friendly_relaxed",
                "when_to_use": ["social_interaction", "mood_lifting"]
            }
        }
    
    def _start_background_tasks(self):
        """Start background tasks for optimization."""
        asyncio.create_task(self._cleanup_inactive_conversations())
        asyncio.create_task(self._update_performance_metrics())
        asyncio.create_task(self._proactive_mental_health_monitoring())
    
    async def process_message(
        self, 
        user_id: str, 
        message: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process a message with advanced AI capabilities.
        
        Returns comprehensive response with metadata.
        """
        start_time = time.time()
        
        try:
            # Get or create conversation context
            conversation_context = await self._get_conversation_context(user_id, context)
            
            # Check response cache first
            cache_key = self._generate_cache_key(user_id, message, context)
            cached_response = self.response_cache.get(cache_key)
            
            if cached_response:
                conversation_context.conversation_metrics.cache_hits += 1
                return {
                    "response": cached_response,
                    "cached": True,
                    "processing_time": time.time() - start_time,
                    "emotional_state": conversation_context.emotional_state,
                    "conversation_id": conversation_context.conversation_id
                }
            
            conversation_context.conversation_metrics.cache_misses += 1
            
            # Perform emotional analysis
            emotional_start = time.time()
            emotional_insight = await self.emotional_intelligence.analyze_emotional_state(
                user_id, message, context
            )
            conversation_context.conversation_metrics.emotional_analysis_time = time.time() - emotional_start
            
            # Retrieve relevant memories
            memory_start = time.time()
            relevant_memories = await self.memory_service.retrieve_contextual_memories(
                user_id, message, context, max_memories=8
            )
            conversation_context.conversation_metrics.memory_retrieval_time = time.time() - memory_start
            
            # Activate semantic network
            concepts = self.memory_service._extract_concepts(message)
            activated_concepts = await self.memory_service.activate_semantic_network(user_id, concepts)
            
            # Select conversation strategy
            strategy = self._select_conversation_strategy(emotional_insight, context)
            
            # Generate therapeutic response
            therapeutic_response = await self.emotional_intelligence.generate_therapeutic_response(
                user_id, message, emotional_insight, context
            )
            
            # Generate AI response
            ai_start = time.time()
            ai_response = await self._generate_enhanced_response(
                conversation_context, message, emotional_insight, relevant_memories, 
                activated_concepts, strategy, therapeutic_response
            )
            conversation_context.conversation_metrics.ai_generation_time = time.time() - ai_start
            
            # Create episodic memory
            await self.memory_service.create_episodic_memory(
                user_id=user_id,
                title=f"Conversation: {message[:50]}...",
                description=f"User: {message}\nAI: {ai_response}",
                emotional_context={
                    'primary_emotion': emotional_insight.primary_emotion,
                    'intensity': emotional_insight.emotional_intensity
                },
                conversation_context=context
            )
            
            # Update conversation context
            await self._update_conversation_context(
                conversation_context, message, ai_response, emotional_insight
            )
            
            # Cache the response
            self.response_cache[cache_key] = ai_response
            if len(self.response_cache) > 1000:  # Limit cache size
                # Remove oldest entries
                oldest_keys = list(self.response_cache.keys())[:100]
                for key in oldest_keys:
                    del self.response_cache[key]
            
            # Calculate total processing time
            total_time = time.time() - start_time
            conversation_context.conversation_metrics.total_processing_time = total_time
            
            # Update therapeutic alliance
            interaction_quality = self._assess_interaction_quality(emotional_insight, therapeutic_response)
            await self.emotional_intelligence.update_therapeutic_alliance(user_id, interaction_quality)
            
            return {
                "response": ai_response,
                "cached": False,
                "processing_time": total_time,
                "emotional_insight": {
                    "primary_emotion": emotional_insight.primary_emotion.value,
                    "intensity": emotional_insight.emotional_intensity,
                    "risk_level": emotional_insight.risk_assessment.value,
                    "recommended_techniques": [t.value for t in emotional_insight.recommended_techniques]
                },
                "therapeutic_response": {
                    "technique": therapeutic_response.therapeutic_technique.value,
                    "empathy_level": therapeutic_response.empathy_level,
                    "growth_opportunities": therapeutic_response.growth_opportunities
                },
                "memory_insights": {
                    "relevant_memories_count": len(relevant_memories),
                    "activated_concepts": list(activated_concepts.keys())[:5],
                    "conversation_strategy": strategy
                },
                "conversation_id": conversation_context.conversation_id,
                "metrics": {
                    "memory_time": conversation_context.conversation_metrics.memory_retrieval_time,
                    "emotional_time": conversation_context.conversation_metrics.emotional_analysis_time,
                    "ai_time": conversation_context.conversation_metrics.ai_generation_time,
                    "cache_hit_rate": self._calculate_cache_hit_rate(conversation_context)
                }
            }
            
        except Exception as e:
            logger.error(f"Error processing message for user {user_id}: {e}")
            return {
                "response": "I'm experiencing some technical difficulties right now. I'm here to listen and support you - could you try sharing that again?",
                "error": True,
                "processing_time": time.time() - start_time,
                "conversation_id": conversation_context.conversation_id if 'conversation_context' in locals() else None
            }
    
    async def _get_conversation_context(
        self, 
        user_id: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> EnhancedConversationContext:
        """Get or create enhanced conversation context."""
        if user_id not in self.active_conversations:
            # Create new conversation context
            conversation_id = str(uuid.uuid4())
            user_profile = self.memory_service.get_user_profile(user_id)
            
            if not user_profile:
                user_profile = self.memory_service.create_user_profile(user_id)
            
            # Get psychological profile
            psychological_profile = self.emotional_intelligence.get_user_psychological_profile(user_id)
            
            # Get therapeutic alliance strength
            alliance_strength = self.emotional_intelligence.get_therapeutic_alliance_strength(user_id)
            
            self.active_conversations[user_id] = EnhancedConversationContext(
                user_id=user_id,
                conversation_id=conversation_id,
                user_profile=user_profile,
                conversation_history=[],
                contextual_memory=None,
                emotional_state=EmotionalState(),
                empathy_model=None,
                current_topics=[],
                psychological_profile=psychological_profile,
                therapeutic_alliance_strength=alliance_strength
            )
        
        # Update last activity
        self.active_conversations[user_id].last_activity = datetime.now(timezone.utc)
        return self.active_conversations[user_id]

    def _generate_cache_key(self, user_id: str, message: str, context: Optional[Dict[str, Any]]) -> str:
        """Generate cache key for response caching."""
        import hashlib

        # Create a hash of user_id, message, and relevant context
        cache_data = f"{user_id}:{message}"
        if context:
            # Only include stable context elements
            stable_context = {k: v for k, v in context.items() if k in ['location', 'time_of_day', 'conversation_type']}
            cache_data += f":{str(stable_context)}"

        return hashlib.md5(cache_data.encode()).hexdigest()

    def _select_conversation_strategy(self, emotional_insight, context: Optional[Dict[str, Any]]) -> str:
        """Select the most appropriate conversation strategy."""
        # High-risk situations require therapeutic approach
        if emotional_insight.risk_assessment.value >= 4:
            return "therapeutic"

        # Emotional distress needs supportive approach
        if emotional_insight.emotional_intensity > 0.7:
            return "supportive"

        # Check context for specific needs
        if context:
            if context.get('seeking_advice'):
                return "exploratory"
            if context.get('learning_mode'):
                return "educational"
            if context.get('casual_chat'):
                return "casual"

        # Default based on emotional state
        if emotional_insight.primary_emotion.value in ['sadness', 'fear', 'anger']:
            return "supportive"
        elif emotional_insight.primary_emotion.value in ['joy', 'surprise']:
            return "casual"
        else:
            return "exploratory"

    async def _generate_enhanced_response(
        self,
        conversation_context: EnhancedConversationContext,
        message: str,
        emotional_insight,
        relevant_memories: List,
        activated_concepts: Dict[str, float],
        strategy: str,
        therapeutic_response
    ) -> str:
        """Generate enhanced AI response using all available context."""
        try:
            # Prepare memory context
            memory_context = ""
            if relevant_memories:
                memory_summaries = []
                for memory in relevant_memories[:3]:  # Use top 3 memories
                    if hasattr(memory, 'description'):
                        memory_summaries.append(f"- {memory.description[:100]}...")
                    elif hasattr(memory, 'content'):
                        memory_summaries.append(f"- {memory.content[:100]}...")
                memory_context = "\n".join(memory_summaries)

            # Prepare concept context
            concept_context = ""
            if activated_concepts:
                top_concepts = sorted(activated_concepts.items(), key=lambda x: x[1], reverse=True)[:5]
                concept_context = ", ".join([concept for concept, _ in top_concepts])

            # Get strategy details
            strategy_info = self.conversation_strategies.get(strategy, self.conversation_strategies["supportive"])

            # Create enhanced prompt
            enhanced_prompt = f"""
            You are an advanced AI companion with deep emotional intelligence and memory.

            CONVERSATION CONTEXT:
            - User Message: "{message}"
            - Conversation Strategy: {strategy} ({strategy_info['description']})
            - Response Style: {strategy_info['response_style']}
            - Recommended Techniques: {', '.join(strategy_info['techniques'])}

            EMOTIONAL ANALYSIS:
            - Primary Emotion: {emotional_insight.primary_emotion.value}
            - Emotional Intensity: {emotional_insight.emotional_intensity:.2f}
            - Risk Level: {emotional_insight.risk_assessment.value}
            - Underlying Needs: {', '.join(emotional_insight.underlying_needs)}

            MEMORY CONTEXT:
            {memory_context if memory_context else "No specific memories retrieved"}

            ACTIVATED CONCEPTS:
            {concept_context if concept_context else "No specific concepts activated"}

            THERAPEUTIC GUIDANCE:
            {therapeutic_response.response_text}

            USER PROFILE:
            - Therapeutic Alliance: {conversation_context.therapeutic_alliance_strength:.2f}
            - Conversation History Length: {len(conversation_context.conversation_history)}

            INSTRUCTIONS:
            1. Create a response that feels natural and human-like
            2. Use the {strategy} strategy with {strategy_info['response_style']} tone
            3. Incorporate relevant memories and concepts naturally
            4. Address the user's emotional needs with appropriate empathy
            5. Be concise but meaningful (2-4 sentences)
            6. Avoid being overly clinical or robotic
            7. Show genuine care and understanding

            Generate a warm, empathetic response that demonstrates you truly understand and care about this person:
            """

            response = await self.gemini_service.generate_response_async(enhanced_prompt)

            # Clean up the response
            response = response.strip()
            if response.startswith('"') and response.endswith('"'):
                response = response[1:-1]

            return response

        except Exception as e:
            logger.error(f"Error generating enhanced response: {e}")
            # Fallback to therapeutic response
            return therapeutic_response.response_text

    async def _update_conversation_context(
        self,
        context: EnhancedConversationContext,
        user_message: str,
        ai_response: str,
        emotional_insight
    ):
        """Update conversation context with new interaction."""
        # Add to conversation history
        interaction = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'user_message': user_message,
            'ai_response': ai_response,
            'emotion': emotional_insight.primary_emotion.value,
            'intensity': emotional_insight.emotional_intensity,
            'risk_level': emotional_insight.risk_assessment.value
        }

        context.conversation_history.append(interaction)

        # Limit history size
        if len(context.conversation_history) > 50:
            context.conversation_history = context.conversation_history[-50:]

        # Update emotional state
        context.emotional_state.primary_emotion = emotional_insight.primary_emotion
        context.emotional_state.intensity = emotional_insight.emotional_intensity

        # Update current topics
        new_concepts = self.memory_service._extract_concepts(user_message)
        context.current_topics.extend(new_concepts)
        context.current_topics = list(set(context.current_topics))[-10:]  # Keep last 10 unique topics

        # Update therapeutic alliance
        context.therapeutic_alliance_strength = self.emotional_intelligence.get_therapeutic_alliance_strength(context.user_id)

    def _assess_interaction_quality(self, emotional_insight, therapeutic_response) -> float:
        """Assess the quality of the interaction for therapeutic alliance."""
        quality = 0.5  # Base quality

        # Higher quality for appropriate empathy level
        if therapeutic_response.empathy_level > 0.7:
            quality += 0.2

        # Higher quality for addressing high-risk situations
        if emotional_insight.risk_assessment.value >= 4 and therapeutic_response.therapeutic_technique.value in ['grounding', 'validation']:
            quality += 0.3

        # Higher quality for growth opportunities
        if therapeutic_response.growth_opportunities:
            quality += 0.1

        return min(quality, 1.0)

    def _calculate_cache_hit_rate(self, context: EnhancedConversationContext) -> float:
        """Calculate cache hit rate for the conversation."""
        total_requests = context.conversation_metrics.cache_hits + context.conversation_metrics.cache_misses
        if total_requests == 0:
            return 0.0
        return context.conversation_metrics.cache_hits / total_requests

    async def _cleanup_inactive_conversations(self):
        """Clean up inactive conversations to free memory."""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour
                current_time = datetime.now(timezone.utc)
                inactive_users = []

                for user_id, context in self.active_conversations.items():
                    time_since_activity = current_time - context.last_activity
                    if time_since_activity.total_seconds() > 7200:  # 2 hours
                        inactive_users.append(user_id)

                for user_id in inactive_users:
                    logger.info(f"Cleaning up inactive conversation for user {user_id}")
                    del self.active_conversations[user_id]

            except Exception as e:
                logger.error(f"Error in conversation cleanup: {e}")

    async def _update_performance_metrics(self):
        """Update global performance metrics."""
        while True:
            try:
                await asyncio.sleep(300)  # Update every 5 minutes

                # Calculate average response time
                total_time = 0
                total_conversations = 0

                for context in self.active_conversations.values():
                    if context.conversation_metrics.total_processing_time > 0:
                        total_time += context.conversation_metrics.total_processing_time
                        total_conversations += 1

                if total_conversations > 0:
                    self.average_response_time = total_time / total_conversations

                # Calculate global cache hit rate
                total_hits = sum(c.conversation_metrics.cache_hits for c in self.active_conversations.values())
                total_misses = sum(c.conversation_metrics.cache_misses for c in self.active_conversations.values())
                total_requests = total_hits + total_misses

                if total_requests > 0:
                    self.cache_hit_rate = total_hits / total_requests

                self.total_conversations = len(self.active_conversations)

                logger.info(f"Performance metrics - Avg response time: {self.average_response_time:.3f}s, Cache hit rate: {self.cache_hit_rate:.2%}, Active conversations: {self.total_conversations}")

            except Exception as e:
                logger.error(f"Error updating performance metrics: {e}")

    async def _proactive_mental_health_monitoring(self):
        """Proactive monitoring for mental health concerns."""
        while True:
            try:
                await asyncio.sleep(1800)  # Check every 30 minutes

                for user_id, context in self.active_conversations.items():
                    # Check for concerning patterns
                    recent_interactions = context.conversation_history[-5:]

                    if len(recent_interactions) >= 3:
                        # Check for escalating risk levels
                        risk_levels = [interaction.get('risk_level', 1) for interaction in recent_interactions]
                        if all(level >= 3 for level in risk_levels[-3:]):
                            logger.warning(f"Escalating mental health risk detected for user {user_id}")
                            # In production, this would trigger appropriate interventions

                        # Check for emotional volatility
                        intensities = [interaction.get('intensity', 0.5) for interaction in recent_interactions]
                        if max(intensities) - min(intensities) > 0.6:
                            logger.info(f"High emotional volatility detected for user {user_id}")

            except Exception as e:
                logger.error(f"Error in proactive monitoring: {e}")

    def get_conversation_insights(self, user_id: str) -> Dict[str, Any]:
        """Get insights about a user's conversation patterns."""
        context = self.active_conversations.get(user_id)
        if not context:
            return {"error": "No active conversation found"}

        # Analyze conversation history
        history = context.conversation_history
        if not history:
            return {"message": "No conversation history available"}

        # Calculate emotional patterns
        emotions = [interaction.get('emotion', 'neutral') for interaction in history]
        emotion_counts = {}
        for emotion in emotions:
            emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1

        # Calculate average intensity and risk
        intensities = [interaction.get('intensity', 0.5) for interaction in history]
        risk_levels = [interaction.get('risk_level', 1) for interaction in history]

        return {
            "conversation_id": context.conversation_id,
            "total_interactions": len(history),
            "emotional_patterns": emotion_counts,
            "average_emotional_intensity": sum(intensities) / len(intensities) if intensities else 0,
            "average_risk_level": sum(risk_levels) / len(risk_levels) if risk_levels else 1,
            "therapeutic_alliance_strength": context.therapeutic_alliance_strength,
            "current_topics": context.current_topics,
            "performance_metrics": {
                "average_response_time": context.conversation_metrics.total_processing_time,
                "cache_hit_rate": self._calculate_cache_hit_rate(context)
            }
        }

    def get_system_performance(self) -> Dict[str, Any]:
        """Get overall system performance metrics."""
        return {
            "active_conversations": self.total_conversations,
            "average_response_time": self.average_response_time,
            "cache_hit_rate": self.cache_hit_rate,
            "response_cache_size": len(self.response_cache),
            "memory_stats": self.memory_service.memory_stats
        }

# Backward compatibility alias
ConversationService = NextGenConversationService
