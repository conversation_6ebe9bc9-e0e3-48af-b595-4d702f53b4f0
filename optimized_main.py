"""
Optimized Main Entry Point for AI Companion System.
Ultra-fast startup and high-performance operation.
"""

import os
import sys
import logging
import signal
import asyncio
import time
from pathlib import Path
from contextlib import asynccontextmanager

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

from optimized_gradio_interface import OptimizedAICompanionInterface
from optimized_conversation_service import OptimizedConversationService
from optimized_memory_service import HighPerformanceMemoryService
from config import settings
from monitoring_service import monitoring, monitor_performance, AlertLevel, HealthStatus

# Configure logging for performance
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('ai_companion_performance.log')
    ]
)

logger = logging.getLogger(__name__)

class PerformanceOptimizedSystem:
    """High-performance AI companion system with optimized startup."""
    
    def __init__(self):
        """Initialize the optimized system."""
        self.interface = None
        self.conversation_service = None
        self.memory_service = None
        self.startup_time = None
        
    async def initialize_async(self):
        """Initialize async components."""
        start_time = time.time()
        
        logger.info("🚀 Starting optimized AI Companion System...")
        
        # Initialize services concurrently
        tasks = [
            self._init_memory_service(),
            self._init_conversation_service(),
            self._check_dependencies_async()
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Check for initialization errors
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Initialization error in task {i}: {result}")
                raise result
        
        # Initialize interface
        self.interface = OptimizedAICompanionInterface()
        
        self.startup_time = time.time() - start_time
        logger.info(f"✅ System initialized in {self.startup_time:.2f} seconds")
        
        return True
    
    async def _init_memory_service(self):
        """Initialize memory service asynchronously."""
        logger.info("🧠 Initializing high-performance memory service...")
        self.memory_service = HighPerformanceMemoryService()
        # Allow time for async initialization
        await asyncio.sleep(0.1)
        logger.info("✅ Memory service ready")
    
    async def _init_conversation_service(self):
        """Initialize conversation service asynchronously."""
        logger.info("💬 Initializing optimized conversation service...")
        self.conversation_service = OptimizedConversationService()
        # Allow time for async initialization
        await asyncio.sleep(0.1)
        logger.info("✅ Conversation service ready")
    
    async def _check_dependencies_async(self):
        """Check dependencies asynchronously."""
        logger.info("🔍 Checking dependencies...")
        
        required_packages = [
            'torch', 'transformers', 'sentence_transformers',
            'google.generativeai', 'gradio', 'redis',
            'numpy', 'scikit-learn', 'pydantic'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                import_name = package
                if package == 'google.generativeai':
                    import_name = 'google.generativeai'
                elif package == 'scikit-learn':
                    import_name = 'sklearn'
                
                __import__(import_name)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            error_msg = f"Missing required packages: {', '.join(missing_packages)}"
            logger.error(error_msg)
            raise ImportError(error_msg)
        
        logger.info("✅ All dependencies available")
    
    def check_environment_fast(self):
        """Fast environment check."""
        logger.info("🔍 Checking environment configuration...")
        
        if not settings.gemini_api_key:
            raise ValueError("GEMINI_API_KEY is not set!")
        
        logger.info("✅ Environment configuration valid")
        return True
    
    def print_performance_banner(self):
        """Print optimized startup banner."""
        banner = f"""
    ╔══════════════════════════════════════════════════════════════╗
    ║                🚀 AI Companion - ULTRA FAST                 ║
    ║                                                              ║
    ║  High-Performance Conversational AI with:                   ║
    ║  • ⚡ Async Operations & Intelligent Caching               ║
    ║  • 🧠 Optimized Dual-Memory Architecture                   ║
    ║  • 💬 Lightning-Fast Response Generation                   ║
    ║  • 📊 Real-time Performance Monitoring                     ║
    ║                                                              ║
    ║  Startup Time: {self.startup_time:.2f}s                                    ║
    ║  Ready for ultra-fast conversations!                        ║
    ╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def get_performance_stats(self):
        """Get system performance statistics."""
        stats = {
            "startup_time": self.startup_time,
            "system_ready": True
        }
        
        if self.conversation_service:
            stats.update(self.conversation_service.get_performance_stats())
        
        if self.memory_service:
            stats["memory_stats"] = self.memory_service.get_cache_stats()
        
        return stats
    
    def launch(self):
        """Launch the optimized system."""
        try:
            # Check environment quickly
            self.check_environment_fast()
            
            # Initialize system asynchronously
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.initialize_async())
            loop.close()
            
            # Print performance banner
            self.print_performance_banner()
            
            # Log performance stats
            stats = self.get_performance_stats()
            logger.info(f"Performance stats: {stats}")
            
            # Launch interface
            logger.info(f"🌐 Starting web interface on port {settings.gradio_port}...")
            print(f"📱 Access the interface at: http://localhost:{settings.gradio_port}")
            print(f"⚡ Ultra-fast responses enabled!")
            print("🔄 Press Ctrl+C to stop the server")
            print("\n" + "="*60)
            
            # Record successful startup
            monitoring.record_metric('optimized_startup_success', 1.0)
            monitoring.record_metric('startup_time', self.startup_time)
            
            # Launch the interface
            self.interface.launch(
                server_name="0.0.0.0",
                debug=settings.debug_mode,
                show_error=True,
                quiet=False
            )
            
        except Exception as e:
            logger.error(f"Error starting optimized system: {e}")
            monitoring.record_error(e, 'optimized_startup')
            raise

def setup_signal_handlers():
    """Setup graceful shutdown signal handlers."""
    def signal_handler(signum, frame):
        print(f"\n🛑 Received signal {signum}, shutting down gracefully...")
        monitoring.create_alert(
            AlertLevel.INFO,
            f"Optimized system shutdown signal received: {signum}",
            'system'
        )
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def main():
    """Main function for optimized AI Companion System."""
    
    # Setup signal handlers
    setup_signal_handlers()
    
    try:
        # Create and launch optimized system
        system = PerformanceOptimizedSystem()
        system.launch()
        
    except KeyboardInterrupt:
        print("\n🛑 Shutting down optimized AI Companion System...")
        logger.info("Optimized system shutdown requested by user")
        monitoring.create_alert(
            AlertLevel.INFO,
            "Optimized system shutdown completed",
            'system'
        )
    except Exception as e:
        logger.error(f"Error in optimized system: {e}")
        print(f"❌ Error: {e}")
        monitoring.record_error(e, 'optimized_main')
        sys.exit(1)

if __name__ == "__main__":
    main()
